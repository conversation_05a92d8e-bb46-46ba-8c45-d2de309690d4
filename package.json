{
  "name": "crypto-monitor",
  "version": "1.0.0",
  "license": "CC0-1.0",
  "author": {
    "name": "serhii"
  },
  "main": "./out/index.js",
  "engines": {
    "node": ">=20.0.0"
  },
  "scripts": {
    "clean": "rimraf ./out",
    "build": "npm run clean && tsc -p ./",
    "watch": "tsc -watch -p ./",
    "lint": "eslint src --ext ts",
    "dev": "npm run build && node --enable-source-maps --trace-warnings ./out/index.js",
    "inspect": "npm run build && node --enable-source-maps --inspect ./out/index.js",
    "start": "node --trace-warnings ./out/index.js",
    "pm2:deploy": "npm run build && pm2 startOrReload ecosystem.config.js",
    "pm2:start": "pm2 start ecosystem.config.js --log ./logs/",
    "pm2:stop": "pm2 stop crypto-monitor",
    "pm2:reload": "pm2 reload crypto-monitor",
    "pm2:delete": "pm2 delete crypto-monitor",
    "test:my-test": "npm run build && node --trace-warnings ./out/my-tests/my-tests.js",
  },
  "devDependencies": {
    "@types/node": "^22.14.1",
    "@typescript-eslint/eslint-plugin": "^8.31.0",
    "@typescript-eslint/parser": "^8.34.0",
    "eslint": "^9.28.0",
    "eslint-plugin-unicorn": "^59.0.1",
    "pino-pretty": "^13.0.0",
    "rimraf": "^6.0.1",
    "ts-node": "^10.9.2",
    "typescript": "^5.8.3"
  },
  "dependencies": {
    "@esfx/async-deferred": "^1.0.0",
    "@esfx/canceltoken": "^1.0.0",
    "ccxt": "^4.5.2",
    "dotenv": "^16.5.0",
    "event-loop-lag": "^1.4.0",
    "pino": "^9.6.0",
    "protobufjs": "^7.5.4",
    "sorted-array-type": "^1.3.0"
  }
}
