{
  "compilerOptions": {
    "target": "es2020",
    "lib": [],
    "types": ["node"],
    "module": "ES2022",
    "moduleResolution": "Node",
    "allowJs": true,
    "checkJs": false,
    "rootDir": "./ts",
    "outDir": "./js",
    "removeComments": false,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "noImplicitAny": false,
    "skipLibCheck": true,
    "strictNullChecks":false
  },
  "include": [
    "./ts/*.ts",
  ],
  "exclude": [
    "node_modules/",
    "./ts/cli.ts",
  ]
}
