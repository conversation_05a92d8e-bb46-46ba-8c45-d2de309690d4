import os
import sys

root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(root + '/python')

# ----------------------------------------------------------------------------

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

# ----------------------------------------------------------------------------


# AUTO-TRANSPILE #
async def example():
    exchange = ccxt.binance({
        'apiKey': 'MY_API_KEY',
        'secret': 'MY_SECRET',
    })
    exchange.set_sandbox_mode(True)
    await exchange.load_markets()
    exchange.verbose = True  # uncomment for debugging purposes if necessary
    orders = await exchange.create_orders([{
    'symbol': 'LTC/USDT:USDT',
    'type': 'limit',
    'side': 'buy',
    'amount': 10,
    'price': 55,
}, {
    'symbol': 'ETH/USDT:USDT',
    'type': 'market',
    'side': 'buy',
    'amount': 0.5,
}])
    print(orders)

    await exchange.close()


asyncio.run(example())
