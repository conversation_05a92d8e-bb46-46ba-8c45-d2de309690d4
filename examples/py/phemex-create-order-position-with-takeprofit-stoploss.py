import os
import sys

root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(root + '/python')

# ----------------------------------------------------------------------------

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

# ----------------------------------------------------------------------------


# AUTO-TRANSPILE #
# ------------------------------------------------------------------------------
async def example():
    exchange = ccxt.phemex({
        'apiKey': 'YOUR_API_KEY',
        'secret': 'YOUR_API_SECRET',
    })
    symbol = 'XRP/USDT:USDT'
    side = 'buy'  # set it to 'buy' for a long position, 'sell' for a short position
    order_type = 'limit'  # set it to 'market' or 'limit'
    amount = 1  # how many contracts
    price = 0.5  # set a price at your desired level
    # take profit and stop loss prices and types
    take_profit_trigger_price = 0.6
    stop_loss_trigger_price = 0.4
    take_profit_limit_price = 0.7
    stop_loss_limit_price = 0.3
    await exchange.load_markets()
    # when symbol's price reaches your predefined "trigger price", stop-loss order would be activated as a "market order". but if you want it to be activated as a "limit order", then set a 'price' parameter for it
    params = {
        'posSide': 'Long',
        'stopLoss': {
            'triggerPrice': stop_loss_trigger_price,
            'type': 'limit',
            'price': stop_loss_limit_price,
        },
        'takeProfit': {
            'triggerPrice': take_profit_trigger_price,
            'type': 'limit',
            'price': take_profit_limit_price,
        },
    }
    print('-----------------------------------------------------------------------')
    try:
        created_order = await exchange.create_order(symbol, order_type, side, amount, price, params)
        print('Created an order', created_order)
        # Fetch all your open orders for this symbol
        all_open_orders = await exchange.fetch_open_orders(symbol)
        print('Fetched all your orders for this symbol', all_open_orders)
    except Exception as e:
        print(str(e))

    await exchange.close()


asyncio.run(example())
