# CCXT Python Examples

To run Python examples from any folder, type in console:

```shell
python path/to/example.py # substitute for actual filename here
```

Example files starting with `async-` require Python 3.6 with `async`/`await` and async generators support.

![basic-chart](https://user-images.githubusercontent.com/1294454/29979754-6d62354c-8f4f-11e7-9e0a-22e87b4a093b.jpg)

## See Also

[co3k-crypto-currency-note](https://github.com/co3k/co3k-crypto-currency-note/blob/master/Untitled.ipynb) – an example of using ccxt to fetch OHLCV candles from Kraken and charting them with matplotlib in a Jupyter Notebook, made by [co3k](https://github.com/co3k).
