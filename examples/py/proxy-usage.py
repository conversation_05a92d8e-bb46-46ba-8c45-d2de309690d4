import os
import sys

root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(root + '/python')

# ----------------------------------------------------------------------------

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

# ----------------------------------------------------------------------------
import asyncio
import ccxt.pro as ccxt  # noqa: E402


# AUTO-TRANSPILE #
# 1) ABOUT CCXT PROXIES, READ MORE AT: https://docs.ccxt.com/#/README?id=proxy
# 2) in python, uncomment the below:
# if sys.platform == 'win32':
#     asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
async def example_proxy_url():
    my_ex = ccxt.kucoin()
    my_ex.proxy_url = 'http://**************:8090/proxy_url.php?caller=https://ccxt.com&url='
    print(await my_ex.fetch('https://api.ipify.org/'))

    await my_ex.close()

async def example_http_proxy():
    my_ex = ccxt.kucoin()
    my_ex.http_proxy = 'http://**************:8911'  # "httpProxy" or "httpsProxy" (depending on your proxy protocol)
    print(await my_ex.fetch('https://api.ipify.org/'))

    await my_ex.close()

async def example_socks_proxy():
    my_ex = ccxt.kucoin()
    my_ex.socks_proxy = 'socks5://127.0.0.1:1080'  # from protocols: socks, socks5, socks5h
    print(await my_ex.fetch('https://api.ipify.org/'))

    await my_ex.close()

async def example_web_sockets():
    my_ex = ccxt.kucoin()
    my_ex.http_proxy = 'http://**************:8911'  # even though you are using WebSockets, you might also need to set up proxy for the exchange's REST requests
    my_ex.ws_proxy = 'http://**************:8911'  # "wsProxy" or "wssProxy" or "wsSocksProxy" (depending on your proxy protocol)
    await my_ex.load_markets()
    #
    # To ensure your WS proxy works, uncomment below code and watch the log
    #
    # myEx.verbose = true;
    # await myEx.loadHttpProxyAgent ();
    # await myEx.watch ('ws://**************:9876/', 'myip'); # in the incoming logs, confirm that you see the proxy IP in "hello" message
    #
    print(await my_ex.watch_ticker('BTC/USDT'))
    await my_ex.close()


    await my_ex.close()

asyncio.run(example_proxy_url())
