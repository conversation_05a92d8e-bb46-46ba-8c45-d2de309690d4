import os
import sys

root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(root + '/python')

# ----------------------------------------------------------------------------

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

# ----------------------------------------------------------------------------


# AUTO-TRANSPILE #
async def example():
    prefix = '-'
    exchange_1 = ccxt.okx()
    exchange_2 = ccxt.htx()
    keys_1 = list(exchange_1.has.keys())
    keys_2 = list(exchange_2.has.keys())
    # check missing from exchange-1
    print('### checking missing functionalities from exchange-1:', exchange_1.id)
    for i in range(0, len(keys_2)):
        key = keys_2[i]
        if exchange_2.has[key]:
            if not key in keys_1:
                print(prefix, key, 'does not exist in', exchange_1.id, 'as opposed to', exchange_2.id)
            elif exchange_2.has[key] != exchange_1.has[key]:
                print(prefix, key, '> ', exchange_1.id, ':', exchange_1.has[key], ',', exchange_2.id, ':', exchange_2.has[key])
    # check missing from exchange-2
    print('### checking missing functionalities from exchange-2:', exchange_2.id)
    for i in range(0, len(keys_1)):
        key = keys_1[i]
        if exchange_1.has[key]:
            if not key in keys_2:
                print(prefix, key, 'does not exist in', exchange_2.id, 'as opposed to', exchange_1.id)
            elif exchange_1.has[key] != exchange_2.has[key]:
                print(prefix, key, '> ', exchange_2.id, ':', exchange_2.has[key], ',', exchange_1.id, ':', exchange_1.has[key])

    await exchange_1.close()
    await exchange_2.close()


asyncio.run(example())
