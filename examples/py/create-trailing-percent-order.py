import os
import sys

root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(root + '/python')

# ----------------------------------------------------------------------------

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

# ----------------------------------------------------------------------------


# AUTO-TRANSPILE #
async def example():
    exchange = ccxt.bingx({
        'apiKey': 'MY_API_KEY',
        'secret': 'MY_SECRET',
    })
    # exchange.setSandboxMode (true);
    # exchange.verbose = true; # uncomment for debugging purposes if necessary
    await exchange.load_markets()
    symbol = 'BTC/USDT:USDT'
    order_type = 'market'
    side = 'sell'
    amount = 0.0001
    price = None
    reduce_only = True
    trailing_percent = 10
    # const trailingTriggerPrice = undefined; # not supported on all exchanges
    params = {
        'reduceOnly': reduce_only,
        'trailingPercent': trailing_percent,
    }
    try:
        create_order = await exchange.create_order(symbol, order_type, side, amount, price, params)
        # Alternatively use the createTrailingAmountOrder method:
        # const create_order = await exchange.createTrailingPercentOrder (symbol, order_type, side, amount, price, trailingPercent, trailingTriggerPrice, {
        #     'reduceOnly': reduceOnly,
        # });
        print(create_order)
    except Exception as e:
        print(str(e))

    await exchange.close()


asyncio.run(example())
