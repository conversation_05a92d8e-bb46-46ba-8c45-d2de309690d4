import os
import sys

root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(root + '/python')

# ----------------------------------------------------------------------------

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

# ----------------------------------------------------------------------------
import asyncio
import ccxt.pro as ccxt  # noqa: E402


# AUTO-TRANSPILE #
async def example():
    exchange = ccxt.binance({
        'apiKey': 'MY_API_KEY',
        'secret': 'MY_SECRET',
    })
    exchange.set_sandbox_mode(True)
    exchange.verbose = True  # uncomment for debugging purposes if necessary
    # load markets
    await exchange.load_markets()
    symbol = 'ETH/USDT'
    type = 'limit'
    side = 'buy'
    amount = 0.01
    price = 1000
    orders = []
    for i in range(1, 5):
        response = await exchange.create_order_ws(symbol, type, side, amount, price)
        price += i
        orders.append(response)
    print(orders)

    await exchange.close()


asyncio.run(example())
