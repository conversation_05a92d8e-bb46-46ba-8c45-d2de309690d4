{"compilerOptions": {"rootDir": "src", "outDir": "build", "allowUnreachableCode": false, "allowUnusedLabels": false, "declaration": true, "forceConsistentCasingInFileNames": true, "lib": ["es2016"], "module": "commonjs", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "pretty": true, "sourceMap": true, "strict": true, "target": "es2017"}, "include": ["src/**/*.ts", "test/**/*.ts"], "exclude": ["node_modules"]}