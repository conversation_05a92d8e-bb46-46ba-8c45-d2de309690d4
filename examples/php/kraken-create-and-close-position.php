<?php
namespace ccxt;
include_once (__DIR__.'/../../ccxt.php');
// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -----------------------------------------------------------------------------

error_reporting(E_ALL);
date_default_timezone_set('UTC');

use ccxt\Precise;
use React\Async;
use React\Promise;


// AUTO-TRANSPILE //
var_dump('CCXT Version:', $ccxt->version);


// ------------------------------------------------------------------------------
function example() {
    return Async\async(function () {
        $exchange = new \ccxt\async\kraken(array(
            'apiKey' => 'YOUR_API_KEY',
            'secret' => 'YOUR_API_SECRET',
        ));
        $symbol = 'UNI/USD';
        $side = 'buy'; // set it to 'buy' for a long position, 'sell' for a short position
        $order_type = 'market'; // set it to 'market' or 'limit'
        $amount = 1;
        $leverage = 2;
        Async\await($exchange->load_markets());
        $market = $exchange->market($symbol);
        // if order_type is 'market', then price is not needed
        $price = null;
        // if order_type is 'limit', then set a price at your desired level
        // you can fetch the ticker and update price
        // const ticker = await exchange.fetchTicker (symbol);
        // const last_price = ticker['last'];
        // const ask_price = ticker['ask'];
        // const bid_price = ticker['bid'];
        // if (order_type === 'limit') {
        //     price = (side === 'buy') ? bid_price * 0.95 : ask_price * 1.05; // i.e. 5% from current price
        // }
        $params = array(
            'leverage' => $leverage,
        );
        // log
        var_dump('Going to open a position', 'for', $amount, 'worth', $amount, $market['base'], '~', $market['settle'], 'using', $side, $order_type, 'order (', ($order_type === 'limit' ? $exchange->price_to_precision($symbol, $price) : ''), '), using the following params:');
        var_dump($params);
        var_dump('-----------------------------------------------------------------------');
        try {
            $created_order = Async\await($exchange->create_order($symbol, $order_type, $side, $amount, $price, $params));
            var_dump('Created an order', $created_order);
            // Fetch all your closed orders for this symbol (because we used market order)
            // - use 'fetchClosedOrders' or 'fetchOrders' and filter with 'closed' status
            $all_closed_orders = Async\await($exchange->fetch_closed_orders($symbol));
            var_dump('Fetched all your closed orders for this symbol', $all_closed_orders);
            $all_open_positions = Async\await($exchange->fetch_positions($symbol));
            var_dump('Fetched all your positions for this symbol', $all_open_positions);
        } catch(Exception $e) {
            var_dump(((string) $e));
        }
    }) ();
}


Async\await(example());
