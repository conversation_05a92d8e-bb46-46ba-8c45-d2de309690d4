<?php
namespace ccxt;
include_once (__DIR__.'/../../ccxt.php');
// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -----------------------------------------------------------------------------

error_reporting(E_ALL);
date_default_timezone_set('UTC');

use ccxt\Precise;
use React\Async;
use React\Promise;


// AUTO-TRANSPILE //
function example() {
    return Async\async(function () {
        $exchange = new \ccxt\async\bingx(array(
            'apiKey' => 'MY_API_KEY',
            'secret' => 'MY_SECRET',
        ));
        // exchange.setSandboxMode (true);
        // exchange.verbose = true; // uncomment for debugging purposes if necessary
        Async\await($exchange->load_markets());
        $symbol = 'BTC/USDT:USDT';
        $order_type = 'market';
        $side = 'sell';
        $amount = 0.0001;
        $price = null;
        $reduce_only = true;
        $trailing_percent = 10;
        // const trailingTriggerPrice = undefined; // not supported on all exchanges
        $params = array(
            'reduceOnly' => $reduce_only,
            'trailingPercent' => $trailing_percent,
        );
        try {
            $create_order = Async\await($exchange->create_order($symbol, $order_type, $side, $amount, $price, $params));
            // Alternatively use the createTrailingAmountOrder method:
            // const create_order = await exchange.createTrailingPercentOrder (symbol, order_type, side, amount, price, trailingPercent, trailingTriggerPrice, {
            //     'reduceOnly': reduceOnly,
            // });
            var_dump($create_order);
        } catch(Exception $e) {
            var_dump(((string) $e));
        }
    }) ();
}


Async\await(example());
