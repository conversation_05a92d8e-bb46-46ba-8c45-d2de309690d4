<?php
namespace ccxt;
include_once (__DIR__.'/../../ccxt.php');
// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -----------------------------------------------------------------------------

error_reporting(E_ALL);
date_default_timezone_set('UTC');

use ccxt\Precise;
use React\Async;
use React\Promise;


// AUTO-TRANSPILE //
var_dump('CCXT Version:', $ccxt->version);


// ------------------------------------------------------------------------------
function example() {
    // at this moment, only OKX support embedded stop-loss & take-profit orders in unified manner. other exchanges are being added actively and will be available soon.
    return Async\async(function () {
        $exchange = new \ccxt\async\okx(array(
            'apiKey' => 'YOUR_API_KEY',
            'secret' => 'YOUR_API_SECRET',
            'password' => 'YOUR_API_PASSWORD',
        ));
        $symbol = 'DOGE/USDT:USDT';
        $side = 'buy'; // 'buy' | 'sell'
        $order_type = 'limit'; // 'market' | 'limit'
        $amount = 1; // how many contracts (see `market(symbol).contractSize` to find out coin portion per one contract)
        Async\await($exchange->load_markets());
        $market = $exchange->market($symbol);
        $ticker = Async\await($exchange->fetch_ticker($symbol));
        $last_price = $ticker['last'];
        $ask_price = $ticker['ask'];
        $bid_price = $ticker['bid'];
        // if order_type is 'market', then price is not needed
        $price = null;
        // if order_type is 'limit', then set a price at your desired level
        if ($order_type === 'limit') {
            $price = ($side === 'buy') ? $bid_price * 0.95 : $ask_price * 1.05; // i.e. 5% from current price
        }
        // set trigger price for stop-loss/take-profit to 2% from current price
        // (note, across different exchanges "trigger" price can be also mentioned with different synonyms, like "activation price", "stop price", "conditional price", etc. )
        $stop_loss_trigger_price = ($order_type === 'market' ? $last_price : $price) * ($side === 'buy' ? 0.98 : 1.02);
        $take_profit_trigger_price = ($order_type === 'market' ? $last_price : $price) * ($side === 'buy' ? 1.02 : 0.98);
        // when symbol's price reaches your predefined "trigger price", stop-loss order would be activated as a "market order". but if you want it to be activated as a "limit order", then set a 'price' parameter for it
        $params = array(
            'stopLoss' => array(
                'triggerPrice' => $stop_loss_trigger_price,
                'price' => $stop_loss_trigger_price * 0.98,
            ),
            'takeProfit' => array(
                'triggerPrice' => $take_profit_trigger_price,
                'price' => $take_profit_trigger_price * 0.98,
            ),
        );
        $position_amount = $market['contractSize'] * $amount;
        $position_value = $position_amount * $last_price;
        // log
        var_dump('Going to open a position', 'for', $amount, 'contracts worth', $position_amount, $market['base'], '~', $position_value, $market['settle'], 'using', $side, $order_type, 'order (', ($order_type === 'limit' ? $exchange->price_to_precision($symbol, $price) : ''), '), using the following params:');
        var_dump($params);
        var_dump('-----------------------------------------------------------------------');
        try {
            $created_order = Async\await($exchange->create_order($symbol, $order_type, $side, $amount, $price, $params));
            var_dump('Created an order', $created_order);
            // Fetch all your open orders for this symbol
            // - use 'fetchOpenOrders' or 'fetchOrders' and filter with 'open' status
            // - note, that some exchanges might return one order object with embedded stoploss/takeprofit fields, while other exchanges might have separate stoploss/takeprofit order objects
            $all_open_orders = Async\await($exchange->fetch_open_orders($symbol));
            var_dump('Fetched all your orders for this symbol', $all_open_orders);
        } catch(Exception $e) {
            var_dump(((string) $e));
        }
    }) ();
}


Async\await(example());
