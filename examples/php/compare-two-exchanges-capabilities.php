<?php
namespace ccxt;
include_once (__DIR__.'/../../ccxt.php');
// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -----------------------------------------------------------------------------

error_reporting(E_ALL);
date_default_timezone_set('UTC');

use ccxt\Precise;
use React\Async;
use React\Promise;


// AUTO-TRANSPILE //
function example() {
    return Async\async(function () {
        $prefix = '-';
        $exchange_1 = new \ccxt\async\okx();
        $exchange_2 = new \ccxt\async\htx();
        $keys_1 = is_array($exchange_1->has) ? array_keys($exchange_1->has) : array();
        $keys_2 = is_array($exchange_2->has) ? array_keys($exchange_2->has) : array();
        // check missing from exchange-1
        var_dump('### checking missing functionalities from exchange-1:', $exchange_1->id);
        for ($i = 0; $i < count($keys_2); $i++) {
            $key = $keys_2[$i];
            if ($exchange_2->has[$key]) {
                if (!in_array($key, $keys_1)) {
                    var_dump($prefix, $key, 'does not exist in', $exchange_1->id, 'as opposed to', $exchange_2->id);
                } elseif ($exchange_2->has[$key] !== $exchange_1->has[$key]) {
                    var_dump($prefix, $key, '> ', $exchange_1->id, ':', $exchange_1->has[$key], ',', $exchange_2->id, ':', $exchange_2->has[$key]);
                }
            }
        }
        // check missing from exchange-2
        var_dump('### checking missing functionalities from exchange-2:', $exchange_2->id);
        for ($i = 0; $i < count($keys_1); $i++) {
            $key = $keys_1[$i];
            if ($exchange_1->has[$key]) {
                if (!in_array($key, $keys_2)) {
                    var_dump($prefix, $key, 'does not exist in', $exchange_2->id, 'as opposed to', $exchange_1->id);
                } elseif ($exchange_1->has[$key] !== $exchange_2->has[$key]) {
                    var_dump($prefix, $key, '> ', $exchange_2->id, ':', $exchange_2->has[$key], ',', $exchange_1->id, ':', $exchange_1->has[$key]);
                }
            }
        }
    }) ();
}


Async\await(example());
