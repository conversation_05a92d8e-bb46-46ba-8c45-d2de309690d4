<?php
namespace ccxt;
include_once (__DIR__.'/../../ccxt.php');
// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -----------------------------------------------------------------------------

error_reporting(E_ALL);
date_default_timezone_set('UTC');

use ccxt\Precise;
use React\Async;
use React\Promise;


// AUTO-TRANSPILE //
function example() {
    return Async\async(function () {
        $exchange = new \ccxt\pro\binance(array(
            'apiKey' => 'MY_API_KEY',
            'secret' => 'MY_SECRET',
        ));
        $exchange->set_sandbox_mode(true);
        $exchange->verbose = true; // uncomment for debugging purposes if necessary
        // load markets
        Async\await($exchange->load_markets());
        $symbol = 'ETH/USDT';
        $type = 'limit';
        $side = 'buy';
        $amount = 0.01;
        $price = 1000;
        $orders = [];
        for ($i = 1; $i < 5; $i++) {
            $response = Async\await($exchange->create_order_ws($symbol, $type, $side, $amount, $price));
            $price += $i;
            $orders[] = $response;
        }
        var_dump($orders);
    }) ();
}


Async\await(example());
