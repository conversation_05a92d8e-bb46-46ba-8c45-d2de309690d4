<?php
include dirname(dirname(dirname(__FILE__))) . '/ccxt.php';
date_default_timezone_set('UTC');

$exchange = new \ccxt\binance(array(
    'apiKey' => 'YOUR_API_KEY',
    'secret' => 'YOUR_SECRET',
    // 'verbose' => true,
));

try {

    $symbol = 'XRP/BTC'; 

    // if you want to find out your open orders, you can use the below code,
    if ($exchange->has['fetchOpenOrders']) {
        $open_orders = $exchange->fetchOpenOrders($symbol);
    } else if ($exchange->has['fetchOrders']) {
        $all_orders = $exchange->fetchOrders($symbol);
        $open_orders = $exchange->filter_by($all_orders, 'status', 'open');
    } else {
        echo ($exchange->id . ' fetch(Open)Orders not supported yet');
    }

    // now, depending the $open_orders array, fill the below ID
    $orderId = 'xxxxxxxx'; 

    // to cancel multiple orders together asynchronously, see the "async-await-multiple.php" example file to adopt the code
    $exchange->cancel_order($orderId, $symbol);

} catch (\ccxt\NetworkError $e) {
    echo '[Network Error] ' . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo '[Error] ' . $e->getMessage() . "\n";
}
