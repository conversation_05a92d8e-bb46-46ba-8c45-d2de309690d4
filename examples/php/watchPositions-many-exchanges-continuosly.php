<?php
namespace ccxt;
include_once (__DIR__.'/../../ccxt.php');
// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -----------------------------------------------------------------------------

error_reporting(E_ALL);
date_default_timezone_set('UTC');

use ccxt\Precise;
use React\Async;
use React\Promise;


// AUTO-TRANSPILE //
// watch and handle constinuosly
function watch_positions_continuously($exchange) {
    return Async\async(function () use ($exchange) {
        while (true) {
            try {
                $positions = Async\await($exchange->watch_positions());
                var_dump('Fetched ', $exchange->id, ' - Positions: ', $positions);
            } catch(Exception $e) {
                var_dump($e);
                break;
            }
        }

    }) ();
}


// start exchanges and fetch OHLCV loop
function start_exchange($exchange_name, $config) {
    return Async\async(function () use ($exchange_name, $config) {
        $exchange_class = '\ccxt\async\\'.$exchange_name;
        $ex = new $exchange_class($config);
        $promises = [];
        $promises[] = watch_positions_continuously($ex);
        Async\await(Promise\all($promises));
        Async\await($ex->close());
    }) ();
}


// main function
function example() {
    return Async\async(function () {
        $exchanges = array(
            'binanceusdm' => array(
                'apiKey' => 'YOUR_API_KEY',
                'secret' => 'YOUR_API_SECRET',
            ),
            'okx' => array(
                'apiKey' => 'YOUR_API_KEY',
                'secret' => 'YOUR_API_SECRET',
            ),
            'huobi' => array(
                'apiKey' => 'YOUR_API_KEY',
                'secret' => 'YOUR_API_SECRET',
            ),
        );
        $promises = [];
        $exchange_ids = is_array($exchanges) ? array_keys($exchanges) : array();
        for ($i = 0; $i < count($exchange_ids); $i++) {
            $exchange_name = $exchange_ids[$i];
            $config = $exchanges[$exchange_name];
            $promises[] = start_exchange($exchange_name, $config);
        }
        Async\await(Promise\all($promises));
    }) ();
}


Async\await(example());
