<?php
namespace ccxt;
include_once (__DIR__.'/../../ccxt.php');
// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -----------------------------------------------------------------------------

error_reporting(E_ALL);
date_default_timezone_set('UTC');

use ccxt\Precise;
use React\Async;
use React\Promise;


// AUTO-TRANSPILE //
// ------------------------------------------------------------------------------
function example() {
    return Async\async(function () {
        $exchange = new \ccxt\async\phemex(array(
            'apiKey' => 'YOUR_API_KEY',
            'secret' => 'YOUR_API_SECRET',
        ));
        $symbol = 'XRP/USDT:USDT';
        $side = 'buy'; // set it to 'buy' for a long position, 'sell' for a short position
        $order_type = 'limit'; // set it to 'market' or 'limit'
        $amount = 1; // how many contracts
        $price = 0.5; // set a price at your desired level
        // take profit and stop loss prices and types
        $take_profit_trigger_price = 0.6;
        $stop_loss_trigger_price = 0.4;
        $take_profit_limit_price = 0.7;
        $stop_loss_limit_price = 0.3;
        Async\await($exchange->load_markets());
        // when symbol's price reaches your predefined "trigger price", stop-loss order would be activated as a "market order". but if you want it to be activated as a "limit order", then set a 'price' parameter for it
        $params = array(
            'posSide' => 'Long',
            'stopLoss' => array(
                'triggerPrice' => $stop_loss_trigger_price,
                'type' => 'limit',
                'price' => $stop_loss_limit_price,
            ),
            'takeProfit' => array(
                'triggerPrice' => $take_profit_trigger_price,
                'type' => 'limit',
                'price' => $take_profit_limit_price,
            ),
        );
        var_dump('-----------------------------------------------------------------------');
        try {
            $created_order = Async\await($exchange->create_order($symbol, $order_type, $side, $amount, $price, $params));
            var_dump('Created an order', $created_order);
            // Fetch all your open orders for this symbol
            $all_open_orders = Async\await($exchange->fetch_open_orders($symbol));
            var_dump('Fetched all your orders for this symbol', $all_open_orders);
        } catch(Exception $e) {
            var_dump(((string) $e));
        }
    }) ();
}


Async\await(example());
