<?php
namespace ccxt;
include_once (__DIR__.'/../../ccxt.php');
// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -----------------------------------------------------------------------------

error_reporting(E_ALL);
date_default_timezone_set('UTC');

use ccxt\Precise;
use React\Async;
use React\Promise;


// AUTO-TRANSPILE //
function example() {
    return Async\async(function () {
        $exchange = new \ccxt\pro\binanceusdm(array(
            'apiKey' => 'YOUR_API_KEY',
            'secret' => 'YOUR_API_SECRET',
        ));
        while (true) {
            $trades = Async\await($exchange->watch_positions());
            var_dump($trades);
        }
    }) ();
}


Async\await(example());
