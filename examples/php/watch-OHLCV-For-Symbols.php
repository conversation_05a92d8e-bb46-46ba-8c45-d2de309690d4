<?php
namespace ccxt;
include_once (__DIR__.'/../../ccxt.php');
// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -----------------------------------------------------------------------------

error_reporting(E_ALL);
date_default_timezone_set('UTC');

use ccxt\Precise;
use React\Async;
use React\Promise;


// AUTO-TRANSPILE //
function example() {
    return Async\async(function () {
        $binance = new \ccxt\pro\binance(array());
        $subscriptions = [['BTC/USDT', '5m'], ['ETH/USDT', '5m'], ['BTC/USDT', '1h']];
        while (true) {
            $ohlcv = Async\await($binance->watch_ohlcv_for_symbols($subscriptions));
            var_dump($ohlcv);
        }
    }) ();
}


Async\await(example());
