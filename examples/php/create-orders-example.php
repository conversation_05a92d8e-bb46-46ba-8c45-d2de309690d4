<?php
namespace ccxt;
include_once (__DIR__.'/../../ccxt.php');
// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -----------------------------------------------------------------------------

error_reporting(E_ALL);
date_default_timezone_set('UTC');

use ccxt\Precise;
use React\Async;
use React\Promise;


// AUTO-TRANSPILE //
function example() {
    return Async\async(function () {
        $exchange = new \ccxt\async\binance(array(
            'apiKey' => 'MY_API_KEY',
            'secret' => 'MY_SECRET',
        ));
        $exchange->set_sandbox_mode(true);
        Async\await($exchange->load_markets());
        $exchange->verbose = true; // uncomment for debugging purposes if necessary
        $orders = Async\await($exchange->create_orders([array(
    'symbol' => 'LTC/USDT:USDT',
    'type' => 'limit',
    'side' => 'buy',
    'amount' => 10,
    'price' => 55,
), array(
    'symbol' => 'ETH/USDT:USDT',
    'type' => 'market',
    'side' => 'buy',
    'amount' => 0.5,
)]));
        var_dump($orders);
    }) ();
}


Async\await(example());
