<!DOCTYPE html>
<html>
    <body>
        <h2>CCXT running on a Webworker example</h2>
        <p>This example uses a web worker to continuously call <b>fetchTicker</b> on a user-defined exchange, symbol and interval. Then, the web worker reaches back with the results.</p>
        <h3>Parameters</h3>
        <label for="exchange">Exchange:</label>
        <input type="text" id="exchange" name="exchange" ><br><br>
        <label for="symbol">Symbol:   </label>
        <input type="text" id="symbol" name="symbol"><br><br>
        <label for="interval">Interval (ms):   </label>
        <input type="text" id="interval" name="interval"><br><br>
        <button onclick="startWorker()">Start CCXT worker</button>
        <button onclick="stopWorker()">Stop CCXT worker</button>
        <h3> Received Results:</h3>
        <output id="result"></output>
        <table id="resultsTable" style="left:300px;width:400px; border:1px solid black;" >
            <thead>
                <tr>
                    <th>Symbol</th>
                    <th>Last Price</th>
                    <th>Base Volume</th>
                    <th>Exchange Ts</th>
                    <th>Local Ts</th>
                </tr>
            </thead>
            <tbody id="tbody">
            </tbody>
        </table>
    </body>
    <script>
        // fill in default values
        document.addEventListener('DOMContentLoaded', function(event) { 
                document.getElementById('exchange').value = 'coinbasepro';
                document.getElementById('symbol').value = 'BTC/USDT';
                document.getElementById('interval').value = '1000';
        });
        
        var ccxtWorker;
        function startWorker() {
        
          if (typeof(Worker) !== "undefined") {
            // if the worker does not exist yet, creates it
            if (typeof(ccxtWorker) == "undefined") {
              ccxtWorker = new Worker("worker.js");
            }
        
            // reads user-defined parameters
            var exchange = document.getElementById('exchange').value
            var symbol = document.getElementById('symbol').value
            var interval = document.getElementById('interval').value
            
            // send a message with those values
            ccxtWorker.postMessage([exchange,symbol, interval]);
            
            ccxtWorker.onmessage = function(event) {
                // every time the ccxtworker posts a message
                // this handler will be invoked
                handleReceivedData(event.data)
            };
          } else {
            document.getElementById("result").innerHTML = "Sorry! No Web Worker support.";
          }
        }
        
        function handleReceivedData(data) {
          var [symbol, last, baseVolume, timestamp, ourTimestamp] = data;
          var tableRef = document.getElementById('resultsTable');
          var tbody = document.getElementById('tbody');
          row = tbody.insertRow(0);
        
          cellSymbol = row.insertCell();
          cellSymbol.innerHTML = symbol;
        
          cellPrice = row.insertCell();
          cellPrice.innerHTML = last;

          cellPrice = row.insertCell();
          cellPrice.innerHTML = baseVolume;
        
          cellTimestamp = row.insertCell();
          cellTimestamp.innerHTML = timestamp;
        
          cellOurtimestamp = row.insertCell();
          cellOurtimestamp.innerHTML = ourTimestamp;
        
        }
        
        function stopWorker() {
          if (ccxtWorker !== undefined) {
            ccxtWorker.terminate();
            ccxtWorker = undefined;
          }
        }
    </script>
    </body>
</html>