<!DOCTYPE HTML>
<html>
<head>
    <title>CCXT Basic example for the browser</title>
    <script type="text/javascript" src="https://unpkg.com/ccxt"></script>
    <script>

        document.addEventListener ("DOMContentLoaded", function () {

            alert ('ccxt version ' + ccxt.version);

            const exchange = new ccxt.bitmex ({
                'proxyUrl': 'http://127.0.0.1:8080/',
            })

            const symbol = 'BTC/USDT'

            exchange.fetchTicker (symbol).then (ticker => {

                const text = [
                    exchange.id,
                    symbol,
                    JSON.stringify (ticker, undefined, '\n\t')
                ]

                document.getElementById ('content').innerHTML = text.join (' ')

            }).catch (e => {

                const text = [
                    e.constructor.name,
                    e.message,
                ]

                document.getElementById ('content').innerHTML = text.join (' ')

            })

        })
    </script>
</head>
<body>
<h1>Hello, CCXT!</h1>
<pre id="content"></pre>
</body>
</html>
