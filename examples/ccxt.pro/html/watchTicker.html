<!DOCTYPE HTML>
<html>
<head>
    <title>CCXT Basic example for the browser</title>
    <script type="text/javascript" src="https://unpkg.com/ccxt"></script>
    <script>

        document.addEventListener ("DOMContentLoaded", function () {

            const pollTickerContinuously = async function (exchange, symbol) {

                while (true) {

                    try {

                        const ticker = await exchange.watchTicker (symbol)

                        const text = [
                            exchange.id,
                            symbol,
                            JSON.stringify (ticker, undefined, '\n\t')
                        ]

                        document.getElementById ('content').innerHTML = text.join (' ');

                    } catch (e) {

                        const text = [
                            e.constructor.name,
                            e.message,
                        ]

                        document.getElementById ('content').innerHTML = text.join (' ');

                    }
                }
            }

            const exchange = new ccxt.pro.binance ({ enableRateLimit: true })
            const symbol = 'ETH/BTC'

            pollTickerContinuously (exchange, symbol)
        })
    </script>
</head>
<body>
<h1>Hello, CCXT!</h1>
<p>This example uses websockets to watch changes in price of ETH/BTC</p>
<pre id="content"></pre>
</body>
</html>
