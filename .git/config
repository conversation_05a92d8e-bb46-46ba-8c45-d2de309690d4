[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
[remote "origin"]
	url = https://<EMAIL>/serhii-zhydel/crypto-monitor.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	vscode-merge-base = origin/main
	vscode-merge-base = origin/main
	merge = refs/heads/main
[submodule "ccxt-source"]
	url = https://github.com/ccxt/ccxt.git
	active = true
