namespace ccxt;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

public partial class bitvavo : Exchange
{
    public override object describe()
    {
        return this.deepExtend(base.describe(), new Dictionary<string, object>() {
            { "id", "bitvavo" },
            { "name", "Bitvavo" },
            { "countries", new List<object>() {"NL"} },
            { "rateLimit", 60 },
            { "version", "v2" },
            { "certified", false },
            { "pro", true },
            { "has", new Dictionary<string, object>() {
                { "CORS", null },
                { "spot", true },
                { "margin", false },
                { "swap", false },
                { "future", false },
                { "option", false },
                { "addMargin", false },
                { "borrowCrossMargin", false },
                { "borrowIsolatedMargin", false },
                { "borrowMargin", false },
                { "cancelAllOrders", true },
                { "cancelOrder", true },
                { "closeAllPositions", false },
                { "closePosition", false },
                { "createOrder", true },
                { "createOrderWithTakeProfitAndStopLoss", false },
                { "createOrderWithTakeProfitAndStopLossWs", false },
                { "createPostOnlyOrder", false },
                { "createReduceOnlyOrder", false },
                { "createStopLimitOrder", true },
                { "createStopMarketOrder", true },
                { "createStopOrder", true },
                { "editOrder", true },
                { "fetchBalance", true },
                { "fetchBorrowInterest", false },
                { "fetchBorrowRate", false },
                { "fetchBorrowRateHistories", false },
                { "fetchBorrowRateHistory", false },
                { "fetchBorrowRates", false },
                { "fetchBorrowRatesPerSymbol", false },
                { "fetchCrossBorrowRate", false },
                { "fetchCrossBorrowRates", false },
                { "fetchCurrencies", true },
                { "fetchDepositAddress", true },
                { "fetchDepositAddresses", false },
                { "fetchDepositAddressesByNetwork", false },
                { "fetchDeposits", true },
                { "fetchDepositWithdrawFee", "emulated" },
                { "fetchDepositWithdrawFees", true },
                { "fetchFundingHistory", false },
                { "fetchFundingInterval", false },
                { "fetchFundingIntervals", false },
                { "fetchFundingRate", false },
                { "fetchFundingRateHistory", false },
                { "fetchFundingRates", false },
                { "fetchGreeks", false },
                { "fetchIndexOHLCV", false },
                { "fetchIsolatedBorrowRate", false },
                { "fetchIsolatedBorrowRates", false },
                { "fetchIsolatedPositions", false },
                { "fetchLeverage", false },
                { "fetchLeverages", false },
                { "fetchLeverageTiers", false },
                { "fetchLiquidations", false },
                { "fetchLongShortRatio", false },
                { "fetchLongShortRatioHistory", false },
                { "fetchMarginAdjustmentHistory", false },
                { "fetchMarginMode", false },
                { "fetchMarginModes", false },
                { "fetchMarketLeverageTiers", false },
                { "fetchMarkets", true },
                { "fetchMarkOHLCV", false },
                { "fetchMarkPrices", false },
                { "fetchMyLiquidations", false },
                { "fetchMySettlementHistory", false },
                { "fetchMyTrades", true },
                { "fetchOHLCV", true },
                { "fetchOpenInterest", false },
                { "fetchOpenInterestHistory", false },
                { "fetchOpenInterests", false },
                { "fetchOpenOrders", true },
                { "fetchOption", false },
                { "fetchOptionChain", false },
                { "fetchOrder", true },
                { "fetchOrderBook", true },
                { "fetchOrders", true },
                { "fetchPosition", false },
                { "fetchPositionHistory", false },
                { "fetchPositionMode", false },
                { "fetchPositions", false },
                { "fetchPositionsForSymbol", false },
                { "fetchPositionsHistory", false },
                { "fetchPositionsRisk", false },
                { "fetchPremiumIndexOHLCV", false },
                { "fetchSettlementHistory", false },
                { "fetchTicker", true },
                { "fetchTickers", true },
                { "fetchTime", true },
                { "fetchTrades", true },
                { "fetchTradingFee", false },
                { "fetchTradingFees", true },
                { "fetchTransfer", false },
                { "fetchTransfers", false },
                { "fetchVolatilityHistory", false },
                { "fetchWithdrawals", true },
                { "reduceMargin", false },
                { "repayCrossMargin", false },
                { "repayIsolatedMargin", false },
                { "repayMargin", false },
                { "setLeverage", false },
                { "setMargin", false },
                { "setMarginMode", false },
                { "setPositionMode", false },
                { "transfer", false },
                { "withdraw", true },
            } },
            { "timeframes", new Dictionary<string, object>() {
                { "1m", "1m" },
                { "5m", "5m" },
                { "15m", "15m" },
                { "30m", "30m" },
                { "1h", "1h" },
                { "2h", "2h" },
                { "4h", "4h" },
                { "6h", "6h" },
                { "8h", "8h" },
                { "12h", "12h" },
                { "1d", "1d" },
            } },
            { "urls", new Dictionary<string, object>() {
                { "logo", "https://github.com/user-attachments/assets/d213155c-8c71-4701-9bd5-45351febc2a8" },
                { "api", new Dictionary<string, object>() {
                    { "public", "https://api.bitvavo.com" },
                    { "private", "https://api.bitvavo.com" },
                } },
                { "www", "https://bitvavo.com/" },
                { "doc", "https://docs.bitvavo.com/" },
                { "fees", "https://bitvavo.com/en/fees" },
                { "referral", "https://bitvavo.com/?a=24F34952F7" },
            } },
            { "api", new Dictionary<string, object>() {
                { "public", new Dictionary<string, object>() {
                    { "get", new Dictionary<string, object>() {
                        { "time", 1 },
                        { "markets", 1 },
                        { "assets", 1 },
                        { "{market}/book", 1 },
                        { "{market}/trades", 5 },
                        { "{market}/candles", 1 },
                        { "ticker/price", 1 },
                        { "ticker/book", 1 },
                        { "ticker/24h", new Dictionary<string, object>() {
                            { "cost", 1 },
                            { "noMarket", 25 },
                        } },
                    } },
                } },
                { "private", new Dictionary<string, object>() {
                    { "get", new Dictionary<string, object>() {
                        { "account", 1 },
                        { "order", 1 },
                        { "orders", 5 },
                        { "ordersOpen", new Dictionary<string, object>() {
                            { "cost", 1 },
                            { "noMarket", 25 },
                        } },
                        { "trades", 5 },
                        { "balance", 5 },
                        { "deposit", 1 },
                        { "depositHistory", 5 },
                        { "withdrawalHistory", 5 },
                    } },
                    { "post", new Dictionary<string, object>() {
                        { "order", 1 },
                        { "withdrawal", 1 },
                    } },
                    { "put", new Dictionary<string, object>() {
                        { "order", 1 },
                    } },
                    { "delete", new Dictionary<string, object>() {
                        { "order", 1 },
                        { "orders", 1 },
                    } },
                } },
            } },
            { "fees", new Dictionary<string, object>() {
                { "trading", new Dictionary<string, object>() {
                    { "tierBased", true },
                    { "percentage", true },
                    { "taker", this.parseNumber("0.0025") },
                    { "maker", this.parseNumber("0.002") },
                    { "tiers", new Dictionary<string, object>() {
                        { "taker", new List<object>() {new List<object> {this.parseNumber("0"), this.parseNumber("0.0025")}, new List<object> {this.parseNumber("100000"), this.parseNumber("0.0020")}, new List<object> {this.parseNumber("250000"), this.parseNumber("0.0016")}, new List<object> {this.parseNumber("500000"), this.parseNumber("0.0012")}, new List<object> {this.parseNumber("1000000"), this.parseNumber("0.0010")}, new List<object> {this.parseNumber("2500000"), this.parseNumber("0.0008")}, new List<object> {this.parseNumber("5000000"), this.parseNumber("0.0006")}, new List<object> {this.parseNumber("10000000"), this.parseNumber("0.0005")}, new List<object> {this.parseNumber("25000000"), this.parseNumber("0.0004")}} },
                        { "maker", new List<object>() {new List<object> {this.parseNumber("0"), this.parseNumber("0.0015")}, new List<object> {this.parseNumber("100000"), this.parseNumber("0.0010")}, new List<object> {this.parseNumber("250000"), this.parseNumber("0.0008")}, new List<object> {this.parseNumber("500000"), this.parseNumber("0.0006")}, new List<object> {this.parseNumber("1000000"), this.parseNumber("0.0005")}, new List<object> {this.parseNumber("2500000"), this.parseNumber("0.0004")}, new List<object> {this.parseNumber("5000000"), this.parseNumber("0.0004")}, new List<object> {this.parseNumber("10000000"), this.parseNumber("0.0003")}, new List<object> {this.parseNumber("25000000"), this.parseNumber("0.0003")}} },
                    } },
                } },
            } },
            { "requiredCredentials", new Dictionary<string, object>() {
                { "apiKey", true },
                { "secret", true },
            } },
            { "features", new Dictionary<string, object>() {
                { "spot", new Dictionary<string, object>() {
                    { "sandbox", false },
                    { "createOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "triggerPrice", true },
                        { "triggerPriceType", null },
                        { "triggerDirection", null },
                        { "stopLossPrice", true },
                        { "takeProfitPrice", true },
                        { "attachedStopLossTakeProfit", null },
                        { "timeInForce", new Dictionary<string, object>() {
                            { "IOC", true },
                            { "FOK", true },
                            { "PO", true },
                            { "GTD", false },
                        } },
                        { "hedged", false },
                        { "trailing", false },
                        { "leverage", false },
                        { "marketBuyRequiresPrice", false },
                        { "marketBuyByCost", true },
                        { "selfTradePrevention", true },
                        { "iceberg", false },
                    } },
                    { "createOrders", null },
                    { "fetchMyTrades", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 1000 },
                        { "daysBack", 100000 },
                        { "untilDays", 100000 },
                        { "symbolRequired", true },
                    } },
                    { "fetchOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", true },
                    } },
                    { "fetchOpenOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", null },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", true },
                    } },
                    { "fetchOrders", new Dictionary<string, object>() {
                        { "marginMode", true },
                        { "limit", 1000 },
                        { "daysBack", 100000 },
                        { "untilDays", 100000 },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", true },
                    } },
                    { "fetchClosedOrders", null },
                    { "fetchOHLCV", new Dictionary<string, object>() {
                        { "limit", 1440 },
                    } },
                } },
                { "swap", new Dictionary<string, object>() {
                    { "linear", null },
                    { "inverse", null },
                } },
                { "future", new Dictionary<string, object>() {
                    { "linear", null },
                    { "inverse", null },
                } },
            } },
            { "exceptions", new Dictionary<string, object>() {
                { "exact", new Dictionary<string, object>() {
                    { "101", typeof(ExchangeError) },
                    { "102", typeof(BadRequest) },
                    { "103", typeof(RateLimitExceeded) },
                    { "104", typeof(RateLimitExceeded) },
                    { "105", typeof(PermissionDenied) },
                    { "107", typeof(ExchangeNotAvailable) },
                    { "108", typeof(ExchangeNotAvailable) },
                    { "109", typeof(ExchangeNotAvailable) },
                    { "110", typeof(BadRequest) },
                    { "200", typeof(BadRequest) },
                    { "201", typeof(BadRequest) },
                    { "202", typeof(BadRequest) },
                    { "203", typeof(BadSymbol) },
                    { "204", typeof(BadRequest) },
                    { "205", typeof(BadRequest) },
                    { "206", typeof(BadRequest) },
                    { "210", typeof(InvalidOrder) },
                    { "211", typeof(InvalidOrder) },
                    { "212", typeof(InvalidOrder) },
                    { "213", typeof(InvalidOrder) },
                    { "214", typeof(InvalidOrder) },
                    { "215", typeof(InvalidOrder) },
                    { "216", typeof(InsufficientFunds) },
                    { "217", typeof(InvalidOrder) },
                    { "230", typeof(ExchangeError) },
                    { "231", typeof(ExchangeError) },
                    { "232", typeof(BadRequest) },
                    { "233", typeof(InvalidOrder) },
                    { "234", typeof(InvalidOrder) },
                    { "235", typeof(ExchangeError) },
                    { "236", typeof(BadRequest) },
                    { "240", typeof(OrderNotFound) },
                    { "300", typeof(AuthenticationError) },
                    { "301", typeof(AuthenticationError) },
                    { "302", typeof(AuthenticationError) },
                    { "303", typeof(AuthenticationError) },
                    { "304", typeof(AuthenticationError) },
                    { "305", typeof(AuthenticationError) },
                    { "306", typeof(AuthenticationError) },
                    { "307", typeof(PermissionDenied) },
                    { "308", typeof(AuthenticationError) },
                    { "309", typeof(AuthenticationError) },
                    { "310", typeof(PermissionDenied) },
                    { "311", typeof(PermissionDenied) },
                    { "312", typeof(PermissionDenied) },
                    { "315", typeof(BadRequest) },
                    { "317", typeof(AccountSuspended) },
                    { "400", typeof(ExchangeError) },
                    { "401", typeof(ExchangeError) },
                    { "402", typeof(PermissionDenied) },
                    { "403", typeof(PermissionDenied) },
                    { "404", typeof(OnMaintenance) },
                    { "405", typeof(ExchangeError) },
                    { "406", typeof(BadRequest) },
                    { "407", typeof(ExchangeError) },
                    { "408", typeof(InsufficientFunds) },
                    { "409", typeof(InvalidAddress) },
                    { "410", typeof(ExchangeError) },
                    { "411", typeof(BadRequest) },
                    { "412", typeof(InvalidAddress) },
                    { "413", typeof(InvalidAddress) },
                    { "414", typeof(ExchangeError) },
                } },
                { "broad", new Dictionary<string, object>() {
                    { "start parameter is invalid", typeof(BadRequest) },
                    { "symbol parameter is invalid", typeof(BadSymbol) },
                    { "amount parameter is invalid", typeof(InvalidOrder) },
                    { "orderId parameter is invalid", typeof(InvalidOrder) },
                } },
            } },
            { "options", new Dictionary<string, object>() {
                { "currencyToPrecisionRoundingMode", TRUNCATE },
                { "BITVAVO-ACCESS-WINDOW", 10000 },
                { "networks", new Dictionary<string, object>() {
                    { "ERC20", "ETH" },
                    { "TRC20", "TRX" },
                } },
                { "operatorId", null },
                { "fiatCurrencies", new List<object>() {"EUR"} },
            } },
            { "precisionMode", SIGNIFICANT_DIGITS },
            { "commonCurrencies", new Dictionary<string, object>() {
                { "MIOTA", "IOTA" },
            } },
        });
    }

    public override object amountToPrecision(object symbol, object amount)
    {
        // https://docs.bitfinex.com/docs/introduction#amount-precision
        // The amount field allows up to 8 decimals.
        // Anything exceeding this will be rounded to the 8th decimal.
        return this.decimalToPrecision(amount, TRUNCATE, getValue(getValue(getValue(this.markets, symbol), "precision"), "amount"), DECIMAL_PLACES);
    }

    public override object priceToPrecision(object symbol, object price)
    {
        price = this.decimalToPrecision(price, ROUND, getValue(getValue(getValue(this.markets, symbol), "precision"), "price"), this.precisionMode);
        // https://docs.bitfinex.com/docs/introduction#price-precision
        // The precision level of all trading prices is based on significant figures.
        // All pairs on Bitfinex use up to 5 significant digits and up to 8 decimals (e.g. 1.2345, 123.45, 1234.5, 0.00012345).
        // Prices submit with a precision larger than 5 will be cut by the API.
        return this.decimalToPrecision(price, TRUNCATE, 8, DECIMAL_PLACES);
    }

    /**
     * @method
     * @name bitvavo#fetchTime
     * @description fetches the current integer timestamp in milliseconds from the exchange server
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {int} the current integer timestamp in milliseconds from the exchange server
     */
    public async override Task<object> fetchTime(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.publicGetTime(parameters);
        //
        //     { "time": 1590379519148 }
        //
        return this.safeInteger(response, "time");
    }

    /**
     * @method
     * @name bitvavo#fetchMarkets
     * @see https://docs.bitvavo.com/#tag/General/paths/~1markets/get
     * @description retrieves data on all markets for bitvavo
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} an array of objects representing market data
     */
    public async override Task<object> fetchMarkets(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.publicGetMarkets(parameters);
        //
        //     [
        //         {
        //             "market":"ADA-BTC",
        //             "status":"trading", // "trading" "halted" "auction"
        //             "base":"ADA",
        //             "quote":"BTC",
        //             "pricePrecision":5,
        //             "minOrderInBaseAsset":"100",
        //             "minOrderInQuoteAsset":"0.001",
        //             "orderTypes": [ "market", "limit" ]
        //         }
        //     ]
        //
        return this.parseMarkets(response);
    }

    public override object parseMarkets(object markets)
    {
        object currencies = this.currencies;
        object currenciesById = this.indexBy(currencies, "id");
        object result = new List<object>() {};
        object fees = this.fees;
        for (object i = 0; isLessThan(i, getArrayLength(markets)); postFixIncrement(ref i))
        {
            object market = getValue(markets, i);
            object id = this.safeString(market, "market");
            object baseId = this.safeString(market, "base");
            object quoteId = this.safeString(market, "quote");
            object bs = this.safeCurrencyCode(baseId);
            object quote = this.safeCurrencyCode(quoteId);
            object status = this.safeString(market, "status");
            object baseCurrency = this.safeValue(currenciesById, baseId);
            object basePrecision = this.safeInteger(baseCurrency, "precision");
            ((IList<object>)result).Add(this.safeMarketStructure(new Dictionary<string, object>() {
                { "id", id },
                { "symbol", add(add(bs, "/"), quote) },
                { "base", bs },
                { "quote", quote },
                { "settle", null },
                { "baseId", baseId },
                { "quoteId", quoteId },
                { "settleId", null },
                { "type", "spot" },
                { "spot", true },
                { "margin", false },
                { "swap", false },
                { "future", false },
                { "option", false },
                { "active", (isEqual(status, "trading")) },
                { "contract", false },
                { "linear", null },
                { "inverse", null },
                { "contractSize", null },
                { "expiry", null },
                { "expiryDatetime", null },
                { "strike", null },
                { "optionType", null },
                { "taker", getValue(getValue(fees, "trading"), "taker") },
                { "maker", getValue(getValue(fees, "trading"), "maker") },
                { "precision", new Dictionary<string, object>() {
                    { "amount", this.safeInteger(baseCurrency, "decimals", basePrecision) },
                    { "price", this.safeInteger(market, "pricePrecision") },
                } },
                { "limits", new Dictionary<string, object>() {
                    { "leverage", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                    { "amount", new Dictionary<string, object>() {
                        { "min", this.safeNumber(market, "minOrderInBaseAsset") },
                        { "max", null },
                    } },
                    { "price", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                    { "cost", new Dictionary<string, object>() {
                        { "min", this.safeNumber(market, "minOrderInQuoteAsset") },
                        { "max", null },
                    } },
                } },
                { "created", null },
                { "info", market },
            }));
        }
        return result;
    }

    /**
     * @method
     * @name bitvavo#fetchCurrencies
     * @see https://docs.bitvavo.com/#tag/General/paths/~1assets/get
     * @description fetches all available currencies on an exchange
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an associative dictionary of currencies
     */
    public async override Task<object> fetchCurrencies(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.publicGetAssets(parameters);
        //
        //     [
        //         {
        //             "symbol": "USDT",
        //             "displayTicker": "USDT",
        //             "name": "Tether",
        //             "slug": "tether",
        //             "popularity": -1,
        //             "decimals": 6,
        //             "depositFee": "0",
        //             "depositConfirmations": 64,
        //             "depositStatus": "OK",
        //             "withdrawalFee": "3.2",
        //             "withdrawalMinAmount": "3.2",
        //             "withdrawalStatus": "OK",
        //             "networks": [
        //               "ETH"
        //             ],
        //             "light": {
        //               "color": "#009393",
        //               "icon": { "hash": "4ad7c699", "svg": "https://...", "webp16": "https://...", "webp32": "https://...", "webp64": "https://...", "webp128": "https://...", "webp256": "https://...", "png16": "https://...", "png32": "https://...", "png64": "https://...", "png128": "https://...", "png256": "https://..."
        //               }
        //             },
        //             "dark": {
        //               "color": "#009393",
        //               "icon": { "hash": "4ad7c699", "svg": "https://...", "webp16": "https://...", "webp32": "https://...", "webp64": "https://...", "webp128": "https://...", "webp256": "https://...", "png16": "https://...", "png32": "https://...", "png64": "https://...", "png128": "https://...", "png256": "https://..."
        //               }
        //             },
        //             "visibility": "PUBLIC",
        //             "message": ""
        //         },
        //     ]
        //
        return this.parseCurrenciesCustom(response);
    }

    public virtual object parseCurrenciesCustom(object currencies)
    {
        //
        //     [
        //         {
        //             "symbol": "USDT",
        //             "displayTicker": "USDT",
        //             "name": "Tether",
        //             "slug": "tether",
        //             "popularity": -1,
        //             "decimals": 6,
        //             "depositFee": "0",
        //             "depositConfirmations": 64,
        //             "depositStatus": "OK",
        //             "withdrawalFee": "3.2",
        //             "withdrawalMinAmount": "3.2",
        //             "withdrawalStatus": "OK",
        //             "networks": [
        //               "ETH"
        //             ],
        //             "light": {
        //               "color": "#009393",
        //               "icon": { "hash": "4ad7c699", "svg": "https://...", "webp16": "https://...", "webp32": "https://...", "webp64": "https://...", "webp128": "https://...", "webp256": "https://...", "png16": "https://...", "png32": "https://...", "png64": "https://...", "png128": "https://...", "png256": "https://..."
        //               }
        //             },
        //             "dark": {
        //               "color": "#009393",
        //               "icon": { "hash": "4ad7c699", "svg": "https://...", "webp16": "https://...", "webp32": "https://...", "webp64": "https://...", "webp128": "https://...", "webp256": "https://...", "png16": "https://...", "png32": "https://...", "png64": "https://...", "png128": "https://...", "png256": "https://..."
        //               }
        //             },
        //             "visibility": "PUBLIC",
        //             "message": ""
        //         },
        //     ]
        //
        object fiatCurrencies = this.safeList(this.options, "fiatCurrencies", new List<object>() {});
        object result = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(currencies)); postFixIncrement(ref i))
        {
            object currency = getValue(currencies, i);
            object id = this.safeString(currency, "symbol");
            object code = this.safeCurrencyCode(id);
            object isFiat = this.inArray(code, fiatCurrencies);
            object networks = new Dictionary<string, object>() {};
            object networksArray = this.safeList(currency, "networks", new List<object>() {});
            object deposit = isEqual(this.safeString(currency, "depositStatus"), "OK");
            object withdrawal = isEqual(this.safeString(currency, "withdrawalStatus"), "OK");
            object active = isTrue(deposit) && isTrue(withdrawal);
            object withdrawFee = this.safeNumber(currency, "withdrawalFee");
            object precision = this.safeInteger(currency, "decimals", 8);
            object minWithdraw = this.safeNumber(currency, "withdrawalMinAmount");
            // btw, absolutely all of them have 1 network atm
            for (object j = 0; isLessThan(j, getArrayLength(networksArray)); postFixIncrement(ref j))
            {
                object networkId = getValue(networksArray, j);
                object networkCode = this.networkIdToCode(networkId);
                ((IDictionary<string,object>)networks)[(string)networkCode] = new Dictionary<string, object>() {
                    { "info", currency },
                    { "id", networkId },
                    { "network", networkCode },
                    { "active", active },
                    { "deposit", deposit },
                    { "withdraw", withdrawal },
                    { "fee", withdrawFee },
                    { "precision", precision },
                    { "limits", new Dictionary<string, object>() {
                        { "withdraw", new Dictionary<string, object>() {
                            { "min", minWithdraw },
                            { "max", null },
                        } },
                    } },
                };
            }
            ((IDictionary<string,object>)result)[(string)code] = this.safeCurrencyStructure(new Dictionary<string, object>() {
                { "info", currency },
                { "id", id },
                { "code", code },
                { "name", this.safeString(currency, "name") },
                { "active", active },
                { "deposit", deposit },
                { "withdraw", withdrawal },
                { "networks", networks },
                { "fee", withdrawFee },
                { "precision", precision },
                { "type", ((bool) isTrue(isFiat)) ? "fiat" : "crypto" },
                { "limits", new Dictionary<string, object>() {
                    { "amount", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                    { "deposit", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                    { "withdraw", new Dictionary<string, object>() {
                        { "min", minWithdraw },
                        { "max", null },
                    } },
                } },
            });
        }
        // set currencies here to avoid calling publicGetAssets twice
        this.currencies = this.mapToSafeMap(this.deepExtend(this.currencies, result));
        return result;
    }

    /**
     * @method
     * @name bitvavo#fetchTicker
     * @see https://docs.bitvavo.com/#tag/Market-Data/paths/~1ticker~124h/get
     * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
     * @param {string} symbol unified symbol of the market to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTicker(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
        };
        object response = await this.publicGetTicker24h(this.extend(request, parameters));
        //
        //     {
        //         "market":"ETH-BTC",
        //         "open":"0.022578",
        //         "high":"0.023019",
        //         "low":"0.022572",
        //         "last":"0.023019",
        //         "volume":"25.16366324",
        //         "volumeQuote":"0.57333305",
        //         "bid":"0.023039",
        //         "bidSize":"0.53500578",
        //         "ask":"0.023041",
        //         "askSize":"0.47859202",
        //         "timestamp":1590381666900
        //     }
        //
        return this.parseTicker(response, market);
    }

    public override object parseTicker(object ticker, object market = null)
    {
        //
        // fetchTicker
        //
        //     {
        //         "market":"ETH-BTC",
        //         "open":"0.022578",
        //         "high":"0.023019",
        //         "low":"0.022573",
        //         "last":"0.023019",
        //         "volume":"25.16366324",
        //         "volumeQuote":"0.57333305",
        //         "bid":"0.023039",
        //         "bidSize":"0.53500578",
        //         "ask":"0.023041",
        //         "askSize":"0.47859202",
        //         "timestamp":1590381666900
        //     }
        //
        object marketId = this.safeString(ticker, "market");
        object symbol = this.safeSymbol(marketId, market, "-");
        object timestamp = this.safeInteger(ticker, "timestamp");
        object last = this.safeString(ticker, "last");
        object baseVolume = this.safeString(ticker, "volume");
        object quoteVolume = this.safeString(ticker, "volumeQuote");
        object open = this.safeString(ticker, "open");
        return this.safeTicker(new Dictionary<string, object>() {
            { "symbol", symbol },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "high", this.safeString(ticker, "high") },
            { "low", this.safeString(ticker, "low") },
            { "bid", this.safeString(ticker, "bid") },
            { "bidVolume", this.safeString(ticker, "bidSize") },
            { "ask", this.safeString(ticker, "ask") },
            { "askVolume", this.safeString(ticker, "askSize") },
            { "vwap", null },
            { "open", open },
            { "close", last },
            { "last", last },
            { "previousClose", null },
            { "change", null },
            { "percentage", null },
            { "average", null },
            { "baseVolume", baseVolume },
            { "quoteVolume", quoteVolume },
            { "info", ticker },
        }, market);
    }

    /**
     * @method
     * @name bitvavo#fetchTickers
     * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
     * @param {string[]|undefined} symbols unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTickers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.publicGetTicker24h(parameters);
        //
        //     [
        //         {
        //             "market":"ADA-BTC",
        //             "open":"0.0000059595",
        //             "high":"0.0000059765",
        //             "low":"0.0000059595",
        //             "last":"0.0000059765",
        //             "volume":"2923.172",
        //             "volumeQuote":"0.01743483",
        //             "bid":"0.0000059515",
        //             "bidSize":"1117.630919",
        //             "ask":"0.0000059585",
        //             "askSize":"809.999739",
        //             "timestamp":1590382266324
        //         }
        //     ]
        //
        return this.parseTickers(response, symbols);
    }

    /**
     * @method
     * @name bitvavo#fetchTrades
     * @see https://docs.bitvavo.com/#tag/Market-Data/paths/~1{market}~1trades/get
     * @description get the list of most recent trades for a particular symbol
     * @param {string} symbol unified symbol of the market to fetch trades for
     * @param {int} [since] timestamp in ms of the earliest trade to fetch
     * @param {int} [limit] the maximum amount of trades to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] the latest time in ms to fetch entries for
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
     */
    public async override Task<object> fetchTrades(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchTrades", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDynamic("fetchTrades", symbol, since, limit, parameters);
        }
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
        };
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = mathMin(limit, 1000);
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["start"] = since;
        }
        var requestparametersVariable = this.handleUntilOption("end", request, parameters);
        request = ((IList<object>)requestparametersVariable)[0];
        parameters = ((IList<object>)requestparametersVariable)[1];
        object response = await this.publicGetMarketTrades(this.extend(request, parameters));
        //
        //     [
        //         {
        //             "id":"94154c98-6e8b-4e33-92a8-74e33fc05650",
        //             "timestamp":1590382761859,
        //             "amount":"0.06026079",
        //             "price":"8095.3",
        //             "side":"buy"
        //         }
        //     ]
        //
        return this.parseTrades(response, market, since, limit);
    }

    public override object parseTrade(object trade, object market = null)
    {
        //
        // fetchTrades (public)
        //
        //     {
        //         "id":"94154c98-6e8b-4e33-92a8-74e33fc05650",
        //         "timestamp":1590382761859,
        //         "amount":"0.06026079",
        //         "price":"8095.3",
        //         "side":"buy"
        //     }
        //
        // createOrder, fetchOpenOrders, fetchOrders, editOrder (private)
        //
        //     {
        //         "id":"b0c86aa5-6ed3-4a2d-ba3a-be9a964220f4",
        //         "timestamp":1590505649245,
        //         "amount":"0.249825",
        //         "price":"183.49",
        //         "taker":true,
        //         "fee":"0.12038925",
        //         "feeCurrency":"EUR",
        //         "settled":true
        //     }
        //
        // fetchMyTrades (private)
        //
        //     {
        //         "id":"b0c86aa5-6ed3-4a2d-ba3a-be9a964220f4",
        //         "orderId":"af76d6ce-9f7c-4006-b715-bb5d430652d0",
        //         "timestamp":1590505649245,
        //         "market":"ETH-EUR",
        //         "side":"sell",
        //         "amount":"0.249825",
        //         "price":"183.49",
        //         "taker":true,
        //         "fee":"0.12038925",
        //         "feeCurrency":"EUR",
        //         "settled":true
        //     }
        //
        // watchMyTrades (private)
        //
        //     {
        //         "event": "fill",
        //         "timestamp": 1590964470132,
        //         "market": "ETH-EUR",
        //         "orderId": "85d082e1-eda4-4209-9580-248281a29a9a",
        //         "fillId": "861d2da5-aa93-475c-8d9a-dce431bd4211",
        //         "side": "sell",
        //         "amount": "0.1",
        //         "price": "211.46",
        //         "taker": true,
        //         "fee": "0.056",
        //         "feeCurrency": "EUR"
        //     }
        //
        object priceString = this.safeString(trade, "price");
        object amountString = this.safeString(trade, "amount");
        object timestamp = this.safeInteger(trade, "timestamp");
        object side = this.safeString(trade, "side");
        object id = this.safeString2(trade, "id", "fillId");
        object marketId = this.safeString(trade, "market");
        object symbol = this.safeSymbol(marketId, market, "-");
        object taker = this.safeValue(trade, "taker");
        object takerOrMaker = null;
        if (isTrue(!isEqual(taker, null)))
        {
            takerOrMaker = ((bool) isTrue(taker)) ? "taker" : "maker";
        }
        object feeCostString = this.safeString(trade, "fee");
        object fee = null;
        if (isTrue(!isEqual(feeCostString, null)))
        {
            object feeCurrencyId = this.safeString(trade, "feeCurrency");
            object feeCurrencyCode = this.safeCurrencyCode(feeCurrencyId);
            fee = new Dictionary<string, object>() {
                { "cost", feeCostString },
                { "currency", feeCurrencyCode },
            };
        }
        object orderId = this.safeString(trade, "orderId");
        return this.safeTrade(new Dictionary<string, object>() {
            { "info", trade },
            { "id", id },
            { "symbol", symbol },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "order", orderId },
            { "type", null },
            { "side", side },
            { "takerOrMaker", takerOrMaker },
            { "price", priceString },
            { "amount", amountString },
            { "cost", null },
            { "fee", fee },
        }, market);
    }

    /**
     * @method
     * @name bitvavo#fetchTradingFees
     * @see https://docs.bitvavo.com/#tag/Account/paths/~1account/get
     * @description fetch the trading fees for multiple markets
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure} indexed by market symbols
     */
    public async override Task<object> fetchTradingFees(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.privateGetAccount(parameters);
        //
        //     {
        //         "fees": {
        //           "taker": "0.0025",
        //           "maker": "0.0015",
        //           "volume": "10000.00"
        //         }
        //     }
        //
        return this.parseTradingFees(response);
    }

    public virtual object parseTradingFees(object fees, object market = null)
    {
        //
        //     {
        //         "fees": {
        //           "taker": "0.0025",
        //           "maker": "0.0015",
        //           "volume": "10000.00"
        //         }
        //     }
        //
        object feesValue = this.safeValue(fees, "fees");
        object maker = this.safeNumber(feesValue, "maker");
        object taker = this.safeNumber(feesValue, "taker");
        object result = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(this.symbols)); postFixIncrement(ref i))
        {
            object symbol = getValue(this.symbols, i);
            ((IDictionary<string,object>)result)[(string)symbol] = new Dictionary<string, object>() {
                { "info", fees },
                { "symbol", symbol },
                { "maker", maker },
                { "taker", taker },
                { "percentage", true },
                { "tierBased", true },
            };
        }
        return result;
    }

    /**
     * @method
     * @name bitvavo#fetchOrderBook
     * @see https://docs.bitvavo.com/#tag/Market-Data/paths/~1{market}~1book/get
     * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @param {string} symbol unified symbol of the market to fetch the order book for
     * @param {int} [limit] the maximum amount of order book entries to return
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    public async override Task<object> fetchOrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
        };
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["depth"] = limit;
        }
        object response = await this.publicGetMarketBook(this.extend(request, parameters));
        //
        //     {
        //         "market":"BTC-EUR",
        //         "nonce":35883831,
        //         "bids":[
        //             ["8097.4","0.6229099"],
        //             ["8097.2","0.64151283"],
        //             ["8097.1","0.24966294"],
        //         ],
        //         "asks":[
        //             ["8097.5","1.36916911"],
        //             ["8098.8","0.33462248"],
        //             ["8099.3","1.12908646"],
        //         ]
        //     }
        //
        object orderbook = this.parseOrderBook(response, getValue(market, "symbol"));
        ((IDictionary<string,object>)orderbook)["nonce"] = this.safeInteger(response, "nonce");
        return orderbook;
    }

    public override object parseOHLCV(object ohlcv, object market = null)
    {
        //
        //     [
        //         1590383700000,
        //         "8088.5",
        //         "8088.5",
        //         "8088.5",
        //         "8088.5",
        //         "0.04788623"
        //     ]
        //
        return new List<object> {this.safeInteger(ohlcv, 0), this.safeNumber(ohlcv, 1), this.safeNumber(ohlcv, 2), this.safeNumber(ohlcv, 3), this.safeNumber(ohlcv, 4), this.safeNumber(ohlcv, 5)};
    }

    public virtual object fetchOHLCVRequest(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
            { "interval", this.safeString(this.timeframes, timeframe, timeframe) },
        };
        if (isTrue(!isEqual(since, null)))
        {
            // https://github.com/ccxt/ccxt/issues/9227
            object duration = this.parseTimeframe(timeframe);
            ((IDictionary<string,object>)request)["start"] = since;
            if (isTrue(isEqual(limit, null)))
            {
                limit = 1440;
            } else
            {
                limit = mathMin(limit, 1440);
            }
            ((IDictionary<string,object>)request)["end"] = this.sum(since, multiply(multiply(limit, duration), 1000));
        }
        var requestparametersVariable = this.handleUntilOption("end", request, parameters);
        request = ((IList<object>)requestparametersVariable)[0];
        parameters = ((IList<object>)requestparametersVariable)[1];
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit; // default 1440, max 1440
        }
        return this.extend(request, parameters);
    }

    /**
     * @method
     * @name bitvavo#fetchOHLCV
     * @see https://docs.bitvavo.com/#tag/Market-Data/paths/~1{market}~1candles/get
     * @description fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
     * @param {string} symbol unified symbol of the market to fetch OHLCV data for
     * @param {string} timeframe the length of time each candle represents
     * @param {int} [since] timestamp in ms of the earliest candle to fetch
     * @param {int} [limit] the maximum amount of candles to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] the latest time in ms to fetch entries for
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
     */
    public async override Task<object> fetchOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchOHLCV", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDeterministic("fetchOHLCV", symbol, since, limit, timeframe, parameters, 1440);
        }
        object request = this.fetchOHLCVRequest(symbol, timeframe, since, limit, parameters);
        object response = await this.publicGetMarketCandles(request);
        //
        //     [
        //         [1590383700000,"8088.5","8088.5","8088.5","8088.5","0.04788623"],
        //         [1590383580000,"8091.3","8091.5","8091.3","8091.5","0.04931221"],
        //         [1590383520000,"8090.3","8092.7","8090.3","8092.5","0.04001286"],
        //     ]
        //
        return this.parseOHLCVs(response, market, timeframe, since, limit);
    }

    public override object parseBalance(object response)
    {
        object result = new Dictionary<string, object>() {
            { "info", response },
            { "timestamp", null },
            { "datetime", null },
        };
        for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
        {
            object balance = getValue(response, i);
            object currencyId = this.safeString(balance, "symbol");
            object code = this.safeCurrencyCode(currencyId);
            object account = this.account();
            ((IDictionary<string,object>)account)["free"] = this.safeString(balance, "available");
            ((IDictionary<string,object>)account)["used"] = this.safeString(balance, "inOrder");
            ((IDictionary<string,object>)result)[(string)code] = account;
        }
        return this.safeBalance(result);
    }

    /**
     * @method
     * @name bitvavo#fetchBalance
     * @see https://docs.bitvavo.com/#tag/Account/paths/~1balance/get
     * @description query for balance and get the amount of funds available for trading or funds locked in orders
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
     */
    public async override Task<object> fetchBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.privateGetBalance(parameters);
        //
        //     [
        //         {
        //             "symbol": "BTC",
        //             "available": "1.********",
        //             "inOrder": "0.********"
        //         }
        //     ]
        //
        return this.parseBalance(response);
    }

    /**
     * @method
     * @name bitvavo#fetchDepositAddress
     * @description fetch the deposit address for a currency associated with this account
     * @param {string} code unified currency code
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
     */
    public async override Task<object> fetchDepositAddress(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(currency, "id") },
        };
        object response = await this.privateGetDeposit(this.extend(request, parameters));
        //
        //     {
        //         "address": "******************************************",
        //         "paymentId": "********"
        //     }
        //
        object address = this.safeString(response, "address");
        object tag = this.safeString(response, "paymentId");
        this.checkAddress(address);
        return new Dictionary<string, object>() {
            { "info", response },
            { "currency", code },
            { "network", null },
            { "address", address },
            { "tag", tag },
        };
    }

    public virtual object createOrderRequest(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
            { "side", side },
            { "orderType", type },
        };
        object isMarketOrder = isTrue(isTrue((isEqual(type, "market"))) || isTrue((isEqual(type, "stopLoss")))) || isTrue((isEqual(type, "takeProfit")));
        object isLimitOrder = isTrue(isTrue((isEqual(type, "limit"))) || isTrue((isEqual(type, "stopLossLimit")))) || isTrue((isEqual(type, "takeProfitLimit")));
        object timeInForce = this.safeString(parameters, "timeInForce");
        object triggerPrice = this.safeStringN(parameters, new List<object>() {"triggerPrice", "stopPrice", "triggerAmount"});
        object postOnly = this.isPostOnly(isMarketOrder, false, parameters);
        object stopLossPrice = this.safeValue(parameters, "stopLossPrice"); // trigger when price crosses from above to below this value
        object takeProfitPrice = this.safeValue(parameters, "takeProfitPrice"); // trigger when price crosses from below to above this value
        parameters = this.omit(parameters, new List<object>() {"timeInForce", "triggerPrice", "stopPrice", "stopLossPrice", "takeProfitPrice"});
        if (isTrue(isMarketOrder))
        {
            object cost = null;
            if (isTrue(!isEqual(price, null)))
            {
                object priceString = this.numberToString(price);
                object amountString = this.numberToString(amount);
                object quoteAmount = Precise.stringMul(amountString, priceString);
                cost = this.parseNumber(quoteAmount);
            } else
            {
                cost = this.safeNumber(parameters, "cost");
            }
            if (isTrue(!isEqual(cost, null)))
            {
                object precision = getValue(this.currency(getValue(market, "quote")), "precision");
                ((IDictionary<string,object>)request)["amountQuote"] = this.decimalToPrecision(cost, TRUNCATE, precision, this.precisionMode);
            } else
            {
                ((IDictionary<string,object>)request)["amount"] = this.amountToPrecision(symbol, amount);
            }
            parameters = this.omit(parameters, new List<object>() {"cost"});
        } else if (isTrue(isLimitOrder))
        {
            ((IDictionary<string,object>)request)["price"] = this.priceToPrecision(symbol, price);
            ((IDictionary<string,object>)request)["amount"] = this.amountToPrecision(symbol, amount);
        }
        object isTakeProfit = isTrue(isTrue((!isEqual(takeProfitPrice, null))) || isTrue((isEqual(type, "takeProfit")))) || isTrue((isEqual(type, "takeProfitLimit")));
        object isStopLoss = isTrue(isTrue(isTrue((!isEqual(stopLossPrice, null))) || isTrue(isTrue((!isEqual(triggerPrice, null))) && isTrue((!isTrue(isTakeProfit))))) || isTrue((isEqual(type, "stopLoss")))) || isTrue((isEqual(type, "stopLossLimit")));
        if (isTrue(isStopLoss))
        {
            if (isTrue(!isEqual(stopLossPrice, null)))
            {
                triggerPrice = stopLossPrice;
            }
            ((IDictionary<string,object>)request)["orderType"] = ((bool) isTrue(isMarketOrder)) ? "stopLoss" : "stopLossLimit";
        } else if (isTrue(isTakeProfit))
        {
            if (isTrue(!isEqual(takeProfitPrice, null)))
            {
                triggerPrice = takeProfitPrice;
            }
            ((IDictionary<string,object>)request)["orderType"] = ((bool) isTrue(isMarketOrder)) ? "takeProfit" : "takeProfitLimit";
        }
        if (isTrue(!isEqual(triggerPrice, null)))
        {
            ((IDictionary<string,object>)request)["triggerAmount"] = this.priceToPrecision(symbol, triggerPrice);
            ((IDictionary<string,object>)request)["triggerType"] = "price";
            ((IDictionary<string,object>)request)["triggerReference"] = "lastTrade"; // 'bestBid', 'bestAsk', 'midPrice'
        }
        if (isTrue(isTrue((!isEqual(timeInForce, null))) && isTrue((!isEqual(timeInForce, "PO")))))
        {
            ((IDictionary<string,object>)request)["timeInForce"] = timeInForce;
        }
        if (isTrue(postOnly))
        {
            ((IDictionary<string,object>)request)["postOnly"] = true;
        }
        object operatorId = null;
        var operatorIdparametersVariable = this.handleOptionAndParams(parameters, "createOrder", "operatorId");
        operatorId = ((IList<object>)operatorIdparametersVariable)[0];
        parameters = ((IList<object>)operatorIdparametersVariable)[1];
        if (isTrue(!isEqual(operatorId, null)))
        {
            ((IDictionary<string,object>)request)["operatorId"] = this.parseToInt(operatorId);
        } else
        {
            throw new ArgumentsRequired ((string)add(this.id, " createOrder() requires an operatorId in params or options, eg: exchange.options['operatorId'] = 1234567890")) ;
        }
        return this.extend(request, parameters);
    }

    /**
     * @method
     * @name bitvavo#createOrder
     * @description create a trade order
     * @see https://docs.bitvavo.com/#tag/Trading-endpoints/paths/~1order/post
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {string} type 'market' or 'limit'
     * @param {string} side 'buy' or 'sell'
     * @param {float} amount how much of currency you want to trade in units of base currency
     * @param {float} price the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
     * @param {object} [params] extra parameters specific to the bitvavo api endpoint
     * @param {string} [params.timeInForce] "GTC", "IOC", or "PO"
     * @param {float} [params.stopPrice] Alias for triggerPrice
     * @param {float} [params.triggerPrice] The price at which a trigger order is triggered at
     * @param {bool} [params.postOnly] If true, the order will only be posted to the order book and not executed immediately
     * @param {float} [params.stopLossPrice] The price at which a stop loss order is triggered at
     * @param {float} [params.takeProfitPrice] The price at which a take profit order is triggered at
     * @param {string} [params.triggerType] "price"
     * @param {string} [params.triggerReference] "lastTrade", "bestBid", "bestAsk", "midPrice" Only for stop orders: Use this to determine which parameter will trigger the order
     * @param {string} [params.selfTradePrevention] "decrementAndCancel", "cancelOldest", "cancelNewest", "cancelBoth"
     * @param {bool} [params.disableMarketProtection] don't cancel if the next fill price is 10% worse than the best fill price
     * @param {bool} [params.responseRequired] Set this to 'false' when only an acknowledgement of success or failure is required, this is faster.
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createOrder(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = this.createOrderRequest(symbol, type, side, amount, price, parameters);
        object response = await this.privatePostOrder(request);
        //
        //      {
        //          "orderId":"dec6a640-5b4c-45bc-8d22-3b41c6716630",
        //          "market":"DOGE-EUR",
        //          "created":1654789135146,
        //          "updated":1654789135153,
        //          "status":"new",
        //          "side":"buy",
        //          "orderType":"stopLossLimit",
        //          "amount":"200",
        //          "amountRemaining":"200",
        //          "price":"0.07471",
        //          "triggerPrice":"0.0747",
        //          "triggerAmount":"0.0747",
        //          "triggerType":"price",
        //          "triggerReference":"lastTrade",
        //          "onHold":"14.98",
        //          "onHoldCurrency":"EUR",
        //          "filledAmount":"0",
        //          "filledAmountQuote":"0",
        //          "feePaid":"0",
        //          "feeCurrency":"EUR",
        //          "fills":[ // filled with market orders only
        //             {
        //                 "id":"b0c86aa5-6ed3-4a2d-ba3a-be9a964220f4",
        //                 "timestamp":1590505649245,
        //                 "amount":"0.249825",
        //                 "price":"183.49",
        //                 "taker":true,
        //                 "fee":"0.12038925",
        //                 "feeCurrency":"EUR",
        //                 "settled":true
        //             }
        //          ],
        //          "selfTradePrevention":"decrementAndCancel",
        //          "visible":true,
        //          "timeInForce":"GTC",
        //          "postOnly":false
        //      }
        //
        return this.parseOrder(response, market);
    }

    public virtual object editOrderRequest(object id, object symbol, object type, object side, object amount = null, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object request = new Dictionary<string, object>() {};
        object market = this.market(symbol);
        object amountRemaining = this.safeNumber(parameters, "amountRemaining");
        object triggerPrice = this.safeStringN(parameters, new List<object>() {"triggerPrice", "stopPrice", "triggerAmount"});
        parameters = this.omit(parameters, new List<object>() {"amountRemaining", "triggerPrice", "stopPrice", "triggerAmount"});
        if (isTrue(!isEqual(price, null)))
        {
            ((IDictionary<string,object>)request)["price"] = this.priceToPrecision(symbol, price);
        }
        if (isTrue(!isEqual(amount, null)))
        {
            ((IDictionary<string,object>)request)["amount"] = this.amountToPrecision(symbol, amount);
        }
        if (isTrue(!isEqual(amountRemaining, null)))
        {
            ((IDictionary<string,object>)request)["amountRemaining"] = this.amountToPrecision(symbol, amountRemaining);
        }
        if (isTrue(!isEqual(triggerPrice, null)))
        {
            ((IDictionary<string,object>)request)["triggerAmount"] = this.priceToPrecision(symbol, triggerPrice);
        }
        request = this.extend(request, parameters);
        if (isTrue(this.isEmpty(request)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " editOrder() requires an amount argument, or a price argument, or non-empty params")) ;
        }
        object clientOrderId = this.safeString(parameters, "clientOrderId");
        if (isTrue(isEqual(clientOrderId, null)))
        {
            ((IDictionary<string,object>)request)["orderId"] = id;
        }
        object operatorId = null;
        var operatorIdparametersVariable = this.handleOptionAndParams(parameters, "editOrder", "operatorId");
        operatorId = ((IList<object>)operatorIdparametersVariable)[0];
        parameters = ((IList<object>)operatorIdparametersVariable)[1];
        if (isTrue(!isEqual(operatorId, null)))
        {
            ((IDictionary<string,object>)request)["operatorId"] = this.parseToInt(operatorId);
        } else
        {
            throw new ArgumentsRequired ((string)add(this.id, " editOrder() requires an operatorId in params or options, eg: exchange.options['operatorId'] = 1234567890")) ;
        }
        ((IDictionary<string,object>)request)["market"] = getValue(market, "id");
        return request;
    }

    /**
     * @method
     * @name bitvavo#editOrder
     * @description edit a trade order
     * @see https://docs.bitvavo.com/#tag/Orders/paths/~1order/put
     * @param {string} id cancel order id
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {string} type 'market' or 'limit'
     * @param {string} side 'buy' or 'sell'
     * @param {float} [amount] how much of currency you want to trade in units of base currency
     * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
     * @param {object} [params] extra parameters specific to the bitvavo api endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> editOrder(object id, object symbol, object type, object side, object amount = null, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = this.editOrderRequest(id, symbol, type, side, amount, price, parameters);
        object response = await this.privatePutOrder(request);
        return this.parseOrder(response, market);
    }

    public virtual object cancelOrderRequest(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " cancelOrder() requires a symbol argument")) ;
        }
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
        };
        object clientOrderId = this.safeString(parameters, "clientOrderId");
        if (isTrue(isEqual(clientOrderId, null)))
        {
            ((IDictionary<string,object>)request)["orderId"] = id;
        }
        object operatorId = null;
        var operatorIdparametersVariable = this.handleOptionAndParams(parameters, "cancelOrder", "operatorId");
        operatorId = ((IList<object>)operatorIdparametersVariable)[0];
        parameters = ((IList<object>)operatorIdparametersVariable)[1];
        if (isTrue(!isEqual(operatorId, null)))
        {
            ((IDictionary<string,object>)request)["operatorId"] = this.parseToInt(operatorId);
        } else
        {
            throw new ArgumentsRequired ((string)add(this.id, " cancelOrder() requires an operatorId in params or options, eg: exchange.options['operatorId'] = 1234567890")) ;
        }
        return this.extend(request, parameters);
    }

    /**
     * @method
     * @name bitvavo#cancelOrder
     * @see https://docs.bitvavo.com/#tag/Orders/paths/~1order/delete
     * @description cancels an open order
     * @see https://docs.bitvavo.com/#tag/Trading-endpoints/paths/~1order/delete
     * @param {string} id order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = this.cancelOrderRequest(id, symbol, parameters);
        object response = await this.privateDeleteOrder(request);
        //
        //     {
        //         "orderId": "2e7ce7fc-44e2-4d80-a4a7-d079c4750b61"
        //     }
        //
        return this.parseOrder(response, market);
    }

    /**
     * @method
     * @name bitvavo#cancelAllOrders
     * @see https://docs.bitvavo.com/#tag/Orders/paths/~1orders/delete
     * @description cancel all open orders
     * @param {string} symbol unified market symbol, only orders in the market of this symbol are cancelled when symbol is not undefined
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelAllOrders(object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["market"] = getValue(market, "id");
        }
        object operatorId = null;
        var operatorIdparametersVariable = this.handleOptionAndParams(parameters, "cancelAllOrders", "operatorId");
        operatorId = ((IList<object>)operatorIdparametersVariable)[0];
        parameters = ((IList<object>)operatorIdparametersVariable)[1];
        if (isTrue(!isEqual(operatorId, null)))
        {
            ((IDictionary<string,object>)request)["operatorId"] = this.parseToInt(operatorId);
        } else
        {
            throw new ArgumentsRequired ((string)add(this.id, " canceAllOrders() requires an operatorId in params or options, eg: exchange.options['operatorId'] = 1234567890")) ;
        }
        object response = await this.privateDeleteOrders(this.extend(request, parameters));
        //
        //     [
        //         {
        //             "orderId": "1be6d0df-d5dc-4b53-a250-3376f3b393e6"
        //         }
        //     ]
        //
        return this.parseOrders(response, market);
    }

    /**
     * @method
     * @name bitvavo#fetchOrder
     * @description fetches information on an order made by the user
     * @see https://docs.bitvavo.com/#tag/Trading-endpoints/paths/~1order/get
     * @param {string} id the order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchOrder() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
        };
        object clientOrderId = this.safeString(parameters, "clientOrderId");
        if (isTrue(isEqual(clientOrderId, null)))
        {
            ((IDictionary<string,object>)request)["orderId"] = id;
        }
        object response = await this.privateGetOrder(this.extend(request, parameters));
        //
        //     {
        //         "orderId":"af76d6ce-9f7c-4006-b715-bb5d430652d0",
        //         "market":"ETH-EUR",
        //         "created":1590505649241,
        //         "updated":1590505649241,
        //         "status":"filled",
        //         "side":"sell",
        //         "orderType":"market",
        //         "amount":"0.249825",
        //         "amountRemaining":"0",
        //         "onHold":"0",
        //         "onHoldCurrency":"ETH",
        //         "filledAmount":"0.249825",
        //         "filledAmountQuote":"45.84038925",
        //         "feePaid":"0.12038925",
        //         "feeCurrency":"EUR",
        //         "fills":[
        //             {
        //                 "id":"b0c86aa5-6ed3-4a2d-ba3a-be9a964220f4",
        //                 "timestamp":1590505649245,
        //                 "amount":"0.249825",
        //                 "price":"183.49",
        //                 "taker":true,
        //                 "fee":"0.12038925",
        //                 "feeCurrency":"EUR",
        //                 "settled":true
        //             }
        //         ],
        //         "selfTradePrevention":"decrementAndCancel",
        //         "visible":false,
        //         "disableMarketProtection":false
        //     }
        //
        return this.parseOrder(response, market);
    }

    public virtual object fetchOrdersRequest(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
        };
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["start"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit; // default 500, max 1000
        }
        var requestparametersVariable = this.handleUntilOption("end", request, parameters);
        request = ((IList<object>)requestparametersVariable)[0];
        parameters = ((IList<object>)requestparametersVariable)[1];
        return this.extend(request, parameters);
    }

    /**
     * @method
     * @name bitvavo#fetchOrders
     * @see https://docs.bitvavo.com/#tag/Trading-endpoints/paths/~1orders/get
     * @description fetches information on multiple orders made by the user
     * @param {string} symbol unified market symbol of the market orders were made in
     * @param {int} [since] the earliest time in ms to fetch orders for
     * @param {int} [limit] the maximum number of order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @param {int} [params.until] the latest time in ms to fetch entries for
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchOrders() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchOrders", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDynamic("fetchOrders", symbol, since, limit, parameters);
        }
        object market = this.market(symbol);
        object request = this.fetchOrdersRequest(symbol, since, limit, parameters);
        object response = await this.privateGetOrders(request);
        //
        //     [
        //         {
        //             "orderId":"af76d6ce-9f7c-4006-b715-bb5d430652d0",
        //             "market":"ETH-EUR",
        //             "created":1590505649241,
        //             "updated":1590505649241,
        //             "status":"filled",
        //             "side":"sell",
        //             "orderType":"market",
        //             "amount":"0.249825",
        //             "amountRemaining":"0",
        //             "onHold":"0",
        //             "onHoldCurrency":"ETH",
        //             "filledAmount":"0.249825",
        //             "filledAmountQuote":"45.84038925",
        //             "feePaid":"0.12038925",
        //             "feeCurrency":"EUR",
        //             "fills":[
        //                 {
        //                     "id":"b0c86aa5-6ed3-4a2d-ba3a-be9a964220f4",
        //                     "timestamp":1590505649245,
        //                     "amount":"0.249825",
        //                     "price":"183.49",
        //                     "taker":true,
        //                     "fee":"0.12038925",
        //                     "feeCurrency":"EUR",
        //                     "settled":true
        //                 }
        //             ],
        //             "selfTradePrevention":"decrementAndCancel",
        //             "visible":false,
        //             "disableMarketProtection":false
        //         }
        //     ]
        //
        return this.parseOrders(response, market, since, limit);
    }

    /**
     * @method
     * @name bitvavo#fetchOpenOrders
     * @see https://docs.bitvavo.com/#tag/Trading-endpoints/paths/~1ordersOpen/get
     * @description fetch all unfilled currently open orders
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch open orders for
     * @param {int} [limit] the maximum number of  open orders structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOpenOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["market"] = getValue(market, "id");
        }
        object response = await this.privateGetOrdersOpen(this.extend(request, parameters));
        //
        //     [
        //         {
        //             "orderId":"af76d6ce-9f7c-4006-b715-bb5d430652d0",
        //             "market":"ETH-EUR",
        //             "created":1590505649241,
        //             "updated":1590505649241,
        //             "status":"filled",
        //             "side":"sell",
        //             "orderType":"market",
        //             "amount":"0.249825",
        //             "amountRemaining":"0",
        //             "onHold":"0",
        //             "onHoldCurrency":"ETH",
        //             "filledAmount":"0.249825",
        //             "filledAmountQuote":"45.84038925",
        //             "feePaid":"0.12038925",
        //             "feeCurrency":"EUR",
        //             "fills":[
        //                 {
        //                     "id":"b0c86aa5-6ed3-4a2d-ba3a-be9a964220f4",
        //                     "timestamp":1590505649245,
        //                     "amount":"0.249825",
        //                     "price":"183.49",
        //                     "taker":true,
        //                     "fee":"0.12038925",
        //                     "feeCurrency":"EUR",
        //                     "settled":true
        //                 }
        //             ],
        //             "selfTradePrevention":"decrementAndCancel",
        //             "visible":false,
        //             "disableMarketProtection":false
        //         }
        //     ]
        //
        return this.parseOrders(response, market, since, limit);
    }

    public virtual object parseOrderStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "new", "open" },
            { "canceled", "canceled" },
            { "canceledAuction", "canceled" },
            { "canceledSelfTradePrevention", "canceled" },
            { "canceledIOC", "canceled" },
            { "canceledFOK", "canceled" },
            { "canceledMarketProtection", "canceled" },
            { "canceledPostOnly", "canceled" },
            { "filled", "closed" },
            { "partiallyFilled", "open" },
            { "expired", "canceled" },
            { "rejected", "canceled" },
            { "awaitingTrigger", "open" },
        };
        return this.safeString(statuses, status, status);
    }

    public override object parseOrder(object order, object market = null)
    {
        //
        // cancelOrder, cancelAllOrders
        //
        //     {
        //         "orderId": "2e7ce7fc-44e2-4d80-a4a7-d079c4750b61"
        //     }
        //
        // createOrder, fetchOrder, fetchOpenOrders, fetchOrders, editOrder
        //
        //     {
        //         "orderId":"af76d6ce-9f7c-4006-b715-bb5d430652d0",
        //         "market":"ETH-EUR",
        //         "created":1590505649241,
        //         "updated":1590505649241,
        //         "status":"filled",
        //         "side":"sell",
        //         "orderType":"market",
        //         "amount":"0.249825",
        //         "amountRemaining":"0",
        //         "price": "183.49", // limit orders only
        //         "onHold":"0",
        //         "onHoldCurrency":"ETH",
        //         "filledAmount":"0.249825",
        //         "filledAmountQuote":"45.84038925",
        //         "feePaid":"0.12038925",
        //         "feeCurrency":"EUR",
        //         "fills":[
        //             {
        //                 "id":"b0c86aa5-6ed3-4a2d-ba3a-be9a964220f4",
        //                 "timestamp":1590505649245,
        //                 "amount":"0.249825",
        //                 "price":"183.49",
        //                 "taker":true,
        //                 "fee":"0.12038925",
        //                 "feeCurrency":"EUR",
        //                 "settled":true
        //             }
        //         ],
        //         "selfTradePrevention":"decrementAndCancel",
        //         "visible":false,
        //         "disableMarketProtection":false
        //         "timeInForce": "GTC",
        //         "postOnly": true,
        //     }
        //
        object id = this.safeString(order, "orderId");
        object timestamp = this.safeInteger(order, "created");
        object marketId = this.safeString(order, "market");
        market = this.safeMarket(marketId, market, "-");
        object symbol = getValue(market, "symbol");
        object status = this.parseOrderStatus(this.safeString(order, "status"));
        object side = this.safeString(order, "side");
        object type = this.safeString(order, "orderType");
        object price = this.safeString(order, "price");
        object amount = this.safeString(order, "amount");
        object remaining = this.safeString(order, "amountRemaining");
        object filled = this.safeString(order, "filledAmount");
        object cost = this.safeString(order, "filledAmountQuote");
        if (isTrue(isEqual(cost, null)))
        {
            object amountQuote = this.safeString(order, "amountQuote");
            object amountQuoteRemaining = this.safeString(order, "amountQuoteRemaining");
            cost = Precise.stringSub(amountQuote, amountQuoteRemaining);
        }
        object fee = null;
        object feeCost = this.safeNumber(order, "feePaid");
        if (isTrue(!isEqual(feeCost, null)))
        {
            object feeCurrencyId = this.safeString(order, "feeCurrency");
            object feeCurrencyCode = this.safeCurrencyCode(feeCurrencyId);
            fee = new Dictionary<string, object>() {
                { "cost", feeCost },
                { "currency", feeCurrencyCode },
            };
        }
        object rawTrades = this.safeValue(order, "fills", new List<object>() {});
        object timeInForce = this.safeString(order, "timeInForce");
        object postOnly = this.safeValue(order, "postOnly");
        // https://github.com/ccxt/ccxt/issues/8489
        return this.safeOrder(new Dictionary<string, object>() {
            { "info", order },
            { "id", id },
            { "clientOrderId", null },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "lastTradeTimestamp", null },
            { "symbol", symbol },
            { "type", type },
            { "timeInForce", timeInForce },
            { "postOnly", postOnly },
            { "side", side },
            { "price", price },
            { "triggerPrice", this.safeNumber(order, "triggerPrice") },
            { "amount", amount },
            { "cost", cost },
            { "average", null },
            { "filled", filled },
            { "remaining", remaining },
            { "status", status },
            { "fee", fee },
            { "trades", rawTrades },
        }, market);
    }

    public virtual object fetchMyTradesRequest(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
        };
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["start"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit; // default 500, max 1000
        }
        var requestparametersVariable = this.handleUntilOption("end", request, parameters);
        request = ((IList<object>)requestparametersVariable)[0];
        parameters = ((IList<object>)requestparametersVariable)[1];
        return this.extend(request, parameters);
    }

    /**
     * @method
     * @name bitvavo#fetchMyTrades
     * @see https://docs.bitvavo.com/#tag/Trading-endpoints/paths/~1trades/get
     * @description fetch all trades made by the user
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch trades for
     * @param {int} [limit] the maximum number of trades structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] the latest time in ms to fetch entries for
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
     */
    public async override Task<object> fetchMyTrades(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchMyTrades() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchMyTrades", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDynamic("fetchMyTrades", symbol, since, limit, parameters);
        }
        object market = this.market(symbol);
        object request = this.fetchMyTradesRequest(symbol, since, limit, parameters);
        object response = await this.privateGetTrades(request);
        //
        //     [
        //         {
        //             "id":"b0c86aa5-6ed3-4a2d-ba3a-be9a964220f4",
        //             "orderId":"af76d6ce-9f7c-4006-b715-bb5d430652d0",
        //             "timestamp":1590505649245,
        //             "market":"ETH-EUR",
        //             "side":"sell",
        //             "amount":"0.249825",
        //             "price":"183.49",
        //             "taker":true,
        //             "fee":"0.12038925",
        //             "feeCurrency":"EUR",
        //             "settled":true
        //         }
        //     ]
        //
        return this.parseTrades(response, market, since, limit);
    }

    public virtual object withdrawRequest(object code, object amount, object address, object tag = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(currency, "id") },
            { "amount", this.currencyToPrecision(code, amount) },
            { "address", address },
        };
        if (isTrue(!isEqual(tag, null)))
        {
            ((IDictionary<string,object>)request)["paymentId"] = tag;
        }
        return this.extend(request, parameters);
    }

    /**
     * @method
     * @name bitvavo#withdraw
     * @description make a withdrawal
     * @param {string} code unified currency code
     * @param {float} amount the amount to withdraw
     * @param {string} address the address to withdraw to
     * @param {string} tag
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> withdraw(object code, object amount, object address, object tag = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        var tagparametersVariable = this.handleWithdrawTagAndParams(tag, parameters);
        tag = ((IList<object>)tagparametersVariable)[0];
        parameters = ((IList<object>)tagparametersVariable)[1];
        this.checkAddress(address);
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = this.withdrawRequest(code, amount, address, tag, parameters);
        object response = await this.privatePostWithdrawal(request);
        //
        //     {
        //         "success": true,
        //         "symbol": "BTC",
        //         "amount": "1.5"
        //     }
        //
        return this.parseTransaction(response, currency);
    }

    public virtual object fetchWithdrawalsRequest(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object request = new Dictionary<string, object>() {};
        object currency = null;
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
            ((IDictionary<string,object>)request)["symbol"] = getValue(currency, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["start"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit; // default 500, max 1000
        }
        return this.extend(request, parameters);
    }

    /**
     * @method
     * @name bitvavo#fetchWithdrawals
     * @see https://docs.bitvavo.com/#tag/Account/paths/~1withdrawalHistory/get
     * @description fetch all withdrawals made from an account
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch withdrawals for
     * @param {int} [limit] the maximum number of withdrawals structures to retrieve
     * @param {object} [params] extra parameters specific to the bitvavo api endpoint
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchWithdrawals(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = this.fetchWithdrawalsRequest(code, since, limit, parameters);
        object currency = null;
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
        }
        object response = await this.privateGetWithdrawalHistory(request);
        //
        //     [
        //         {
        //             "timestamp":1590531212000,
        //             "symbol":"ETH",
        //             "amount":"0.091",
        //             "fee":"0.009",
        //             "status":"awaiting_bitvavo_inspection",
        //             "address":"******************************************",
        //             "paymentId": "********",
        //             "txId": "927b3ea50c5bb52c6854152d305dfa1e27fc01d10464cf10825d96d69d235eb3",
        //         }
        //     ]
        //
        return this.parseTransactions(response, currency, since, limit, new Dictionary<string, object>() {
            { "type", "withdrawal" },
        });
    }

    public virtual object fetchDepositsRequest(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object request = new Dictionary<string, object>() {};
        object currency = null;
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
            ((IDictionary<string,object>)request)["symbol"] = getValue(currency, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["start"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit; // default 500, max 1000
        }
        return this.extend(request, parameters);
    }

    /**
     * @method
     * @name bitvavo#fetchDeposits
     * @see https://docs.bitvavo.com/#tag/Account/paths/~1depositHistory/get
     * @description fetch all deposits made to an account
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch deposits for
     * @param {int} [limit] the maximum number of deposits structures to retrieve
     * @param {object} [params] extra parameters specific to the bitvavo api endpoint
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchDeposits(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = this.fetchDepositsRequest(code, since, limit, parameters);
        object currency = null;
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
        }
        object response = await this.privateGetDepositHistory(request);
        //
        //     [
        //         {
        //             "timestamp":1590492401000,
        //             "symbol":"ETH",
        //             "amount":"0.249825",
        //             "fee":"0",
        //             "status":"completed",
        //             "txId":"0x5167b473fd37811f9ef22364c3d54726a859ef9d98934b3a1e11d7baa8d2c2e2"
        //         }
        //     ]
        //
        return this.parseTransactions(response, currency, since, limit, new Dictionary<string, object>() {
            { "type", "deposit" },
        });
    }

    public virtual object parseTransactionStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "awaiting_processing", "pending" },
            { "awaiting_email_confirmation", "pending" },
            { "awaiting_bitvavo_inspection", "pending" },
            { "approved", "pending" },
            { "sending", "pending" },
            { "in_mempool", "pending" },
            { "processed", "pending" },
            { "completed", "ok" },
            { "canceled", "canceled" },
        };
        return this.safeString(statuses, status, status);
    }

    public override object parseTransaction(object transaction, object currency = null)
    {
        //
        // withdraw
        //
        //     {
        //         "success": true,
        //         "symbol": "BTC",
        //         "amount": "1.5"
        //     }
        //
        // fetchWithdrawals
        //
        //     {
        //         "timestamp": 1542967486256,
        //         "symbol": "BTC",
        //         "amount": "0.99994",
        //         "address": "BitcoinAddress",
        //         "paymentId": "********",
        //         "txId": "927b3ea50c5bb52c6854152d305dfa1e27fc01d10464cf10825d96d69d235eb3",
        //         "fee": "0.00006",
        //         "status": "awaiting_processing"
        //     }
        //
        // fetchDeposits
        //
        //     {
        //         "timestamp":1590492401000,
        //         "symbol":"ETH",
        //         "amount":"0.249825",
        //         "fee":"0",
        //         "status":"completed",
        //         "txId":"0x5167b473fd37811f9ef22364c3d54726a859ef9d98934b3a1e11d7baa8d2c2e2"
        //     }
        //
        object id = null;
        object timestamp = this.safeInteger(transaction, "timestamp");
        object currencyId = this.safeString(transaction, "symbol");
        object code = this.safeCurrencyCode(currencyId, currency);
        object status = this.parseTransactionStatus(this.safeString(transaction, "status"));
        object amount = this.safeNumber(transaction, "amount");
        object address = this.safeString(transaction, "address");
        object txid = this.safeString(transaction, "txId");
        object fee = null;
        object feeCost = this.safeNumber(transaction, "fee");
        if (isTrue(!isEqual(feeCost, null)))
        {
            fee = new Dictionary<string, object>() {
                { "cost", feeCost },
                { "currency", code },
            };
        }
        object type = null;
        if (isTrue(isTrue((inOp(transaction, "success"))) || isTrue((inOp(transaction, "address")))))
        {
            type = "withdrawal";
        } else
        {
            type = "deposit";
        }
        object tag = this.safeString(transaction, "paymentId");
        return new Dictionary<string, object>() {
            { "info", transaction },
            { "id", id },
            { "txid", txid },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "addressFrom", null },
            { "address", address },
            { "addressTo", address },
            { "tagFrom", null },
            { "tag", tag },
            { "tagTo", tag },
            { "type", type },
            { "amount", amount },
            { "currency", code },
            { "status", status },
            { "updated", null },
            { "fee", fee },
            { "network", null },
            { "comment", null },
            { "internal", null },
        };
    }

    public override object parseDepositWithdrawFee(object fee, object currency = null)
    {
        //
        //   {
        //       "symbol": "1INCH",
        //       "name": "1inch",
        //       "decimals": 8,
        //       "depositFee": "0",
        //       "depositConfirmations": 64,
        //       "depositStatus": "OK",
        //       "withdrawalFee": "6.1",
        //       "withdrawalMinAmount": "6.1",
        //       "withdrawalStatus": "OK",
        //       "networks": [
        //         "ETH"
        //       ],
        //       "message": ""
        //   }
        //
        object result = new Dictionary<string, object>() {
            { "info", fee },
            { "withdraw", new Dictionary<string, object>() {
                { "fee", this.safeNumber(fee, "withdrawalFee") },
                { "percentage", false },
            } },
            { "deposit", new Dictionary<string, object>() {
                { "fee", this.safeNumber(fee, "depositFee") },
                { "percentage", false },
            } },
            { "networks", new Dictionary<string, object>() {} },
        };
        object networks = this.safeValue(fee, "networks");
        object networkId = this.safeValue(networks, 0); // Bitvavo currently only supports one network per currency
        object currencyCode = this.safeString(currency, "code");
        if (isTrue(isEqual(networkId, "Mainnet")))
        {
            networkId = currencyCode;
        }
        object networkCode = this.networkIdToCode(networkId, currencyCode);
        ((IDictionary<string,object>)getValue(result, "networks"))[(string)networkCode] = new Dictionary<string, object>() {
            { "deposit", getValue(result, "deposit") },
            { "withdraw", getValue(result, "withdraw") },
        };
        return result;
    }

    /**
     * @method
     * @name bitvavo#fetchDepositWithdrawFees
     * @description fetch deposit and withdraw fees
     * @see https://docs.bitvavo.com/#tag/General/paths/~1assets/get
     * @param {string[]|undefined} codes list of unified currency codes
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a list of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure}
     */
    public async override Task<object> fetchDepositWithdrawFees(object codes = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.publicGetAssets(parameters);
        //
        //   [
        //       {
        //           "symbol": "1INCH",
        //           "name": "1inch",
        //           "decimals": 8,
        //           "depositFee": "0",
        //           "depositConfirmations": 64,
        //           "depositStatus": "OK",
        //           "withdrawalFee": "6.1",
        //           "withdrawalMinAmount": "6.1",
        //           "withdrawalStatus": "OK",
        //           "networks": [
        //             "ETH"
        //           ],
        //           "message": ""
        //       },
        //   ]
        //
        return this.parseDepositWithdrawFees(response, codes, "symbol");
    }

    public override object sign(object path, object api = null, object method = null, object parameters = null, object headers = null, object body = null)
    {
        api ??= "public";
        method ??= "GET";
        parameters ??= new Dictionary<string, object>();
        object query = this.omit(parameters, this.extractParams(path));
        object url = add(add(add("/", this.version), "/"), this.implodeParams(path, parameters));
        object getOrDelete = isTrue((isEqual(method, "GET"))) || isTrue((isEqual(method, "DELETE")));
        if (isTrue(getOrDelete))
        {
            if (isTrue(getArrayLength(new List<object>(((IDictionary<string,object>)query).Keys))))
            {
                url = add(url, add("?", this.urlencode(query)));
            }
        }
        if (isTrue(isEqual(api, "private")))
        {
            this.checkRequiredCredentials();
            object payload = "";
            if (!isTrue(getOrDelete))
            {
                if (isTrue(getArrayLength(new List<object>(((IDictionary<string,object>)query).Keys))))
                {
                    body = this.json(query);
                    payload = body;
                }
            }
            object timestamp = ((object)this.milliseconds()).ToString();
            object auth = add(add(add(timestamp, method), url), payload);
            object signature = this.hmac(this.encode(auth), this.encode(this.secret), sha256);
            object accessWindow = this.safeString(this.options, "BITVAVO-ACCESS-WINDOW", "10000");
            headers = new Dictionary<string, object>() {
                { "BITVAVO-ACCESS-KEY", this.apiKey },
                { "BITVAVO-ACCESS-SIGNATURE", signature },
                { "BITVAVO-ACCESS-TIMESTAMP", timestamp },
                { "BITVAVO-ACCESS-WINDOW", accessWindow },
            };
            if (!isTrue(getOrDelete))
            {
                ((IDictionary<string,object>)headers)["Content-Type"] = "application/json";
            }
        }
        url = add(getValue(getValue(this.urls, "api"), api), url);
        return new Dictionary<string, object>() {
            { "url", url },
            { "method", method },
            { "body", body },
            { "headers", headers },
        };
    }

    public override object handleErrors(object httpCode, object reason, object url, object method, object headers, object body, object response, object requestHeaders, object requestBody)
    {
        if (isTrue(isEqual(response, null)))
        {
            return null;  // fallback to default error handler
        }
        //
        //     {"errorCode":308,"error":"The signature length is invalid (HMAC-SHA256 should return a 64 length hexadecimal string)."}
        //     {"errorCode":203,"error":"symbol parameter is required."}
        //     {"errorCode":205,"error":"symbol parameter is invalid."}
        //
        object errorCode = this.safeString(response, "errorCode");
        object error = this.safeString(response, "error");
        if (isTrue(!isEqual(errorCode, null)))
        {
            object feedback = add(add(this.id, " "), body);
            this.throwBroadlyMatchedException(getValue(this.exceptions, "broad"), error, feedback);
            this.throwExactlyMatchedException(getValue(this.exceptions, "exact"), errorCode, feedback);
            throw new ExchangeError ((string)feedback) ;
        }
        return null;
    }

    public override object calculateRateLimiterCost(object api, object method, object path, object parameters, object config = null)
    {
        config ??= new Dictionary<string, object>();
        if (isTrue(isTrue((inOp(config, "noMarket"))) && !isTrue((inOp(parameters, "market")))))
        {
            return getValue(config, "noMarket");
        }
        return this.safeValue(config, "cost", 1);
    }
}
