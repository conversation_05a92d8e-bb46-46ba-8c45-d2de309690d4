namespace ccxt.pro;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code


public partial class onetrading { public onetrading(object args = null) : base(args) { } }
public partial class onetrading : ccxt.onetrading
{
    public override object describe()
    {
        return this.deepExtend(base.describe(), new Dictionary<string, object>() {
            { "has", new Dictionary<string, object>() {
                { "ws", true },
                { "watchBalance", true },
                { "watchTicker", true },
                { "watchTickers", true },
                { "watchTrades", false },
                { "watchTradesForSymbols", false },
                { "watchMyTrades", true },
                { "watchOrders", true },
                { "watchOrderBook", true },
                { "watchOHLCV", true },
            } },
            { "urls", new Dictionary<string, object>() {
                { "api", new Dictionary<string, object>() {
                    { "ws", "wss://streams.onetrading.com/" },
                } },
            } },
            { "options", new Dictionary<string, object>() {
                { "bp_remaining_quota", 200 },
                { "timeframes", new Dictionary<string, object>() {
                    { "1m", new Dictionary<string, object>() {
                        { "unit", "MINUTES" },
                        { "period", 1 },
                    } },
                    { "5m", new Dictionary<string, object>() {
                        { "unit", "MINUTES" },
                        { "period", 5 },
                    } },
                    { "15m", new Dictionary<string, object>() {
                        { "unit", "MINUTES" },
                        { "period", 15 },
                    } },
                    { "30m", new Dictionary<string, object>() {
                        { "unit", "MINUTES" },
                        { "period", 30 },
                    } },
                    { "1h", new Dictionary<string, object>() {
                        { "unit", "HOURS" },
                        { "period", 1 },
                    } },
                    { "4h", new Dictionary<string, object>() {
                        { "unit", "HOURS" },
                        { "period", 4 },
                    } },
                    { "1d", new Dictionary<string, object>() {
                        { "unit", "DAYS" },
                        { "period", 1 },
                    } },
                    { "1w", new Dictionary<string, object>() {
                        { "unit", "WEEKS" },
                        { "period", 1 },
                    } },
                    { "1M", new Dictionary<string, object>() {
                        { "unit", "MONTHS" },
                        { "period", 1 },
                    } },
                } },
            } },
            { "streaming", new Dictionary<string, object>() {} },
            { "exceptions", new Dictionary<string, object>() {} },
        });
    }

    /**
     * @method
     * @name onetrading#watchBalance
     * @see https://developers.bitpanda.com/exchange/#account-history-channel
     * @description watch balance and get the amount of funds available for trading or funds locked in orders
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
     */
    public async override Task<object> watchBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.authenticate(parameters);
        object url = getValue(getValue(this.urls, "api"), "ws");
        object messageHash = "balance";
        object subscribeHash = "ACCOUNT_HISTORY";
        object bpRemainingQuota = this.safeInteger(this.options, "bp_remaining_quota", 200);
        object subscribe = new Dictionary<string, object>() {
            { "type", "SUBSCRIBE" },
            { "bp_remaining_quota", bpRemainingQuota },
            { "channels", new List<object>() {new Dictionary<string, object>() {
    { "name", "ACCOUNT_HISTORY" },
}} },
        };
        object request = this.deepExtend(subscribe, parameters);
        return await this.watch(url, messageHash, request, subscribeHash, request);
    }

    public virtual void handleBalanceSnapshot(WebSocketClient client, object message)
    {
        //
        // snapshot
        //     {
        //         "account_id": "b355abb8-aaae-4fae-903c-c60ff74723c6",
        //         "type": "BALANCES_SNAPSHOT",
        //         "channel_name": "ACCOUNT_HISTORY",
        //         "time": "2019-04-01T13:39:17.155Z",
        //         "balances": [{
        //                 "account_id": "b355abb8-aaae-4fae-903c-c60ff74723c6",
        //                 "currency_code": "BTC",
        //                 "change": "0.5",
        //                 "available": "10.0",
        //                 "locked": "1.1234567",
        //                 "sequence": 1,
        //                 "time": "2019-04-01T13:39:17.155Z"
        //             },
        //             {
        //                 "account_id": "b355abb8-aaae-4fae-903c-c60ff74723c6",
        //                 "currency_code": "ETH",
        //                 "change": "0.5",
        //                 "available": "10.0",
        //                 "locked": "1.1234567",
        //                 "sequence": 2,
        //                 "time": "2019-04-01T13:39:17.155Z"
        //             }
        //         ]
        //     }
        //
        this.balance = this.parseBalance(message);
        object messageHash = "balance";
        callDynamically(client as WebSocketClient, "resolve", new object[] {this.balance, messageHash});
    }

    /**
     * @method
     * @name onetrading#watchTicker
     * @see https://developers.bitpanda.com/exchange/#market-ticker-channel
     * @description watches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
     * @param {string} symbol unified symbol of the market to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> watchTicker(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        symbol = getValue(market, "symbol");
        object subscriptionHash = "MARKET_TICKER";
        object messageHash = add("ticker.", symbol);
        object request = new Dictionary<string, object>() {
            { "type", "SUBSCRIBE" },
            { "channels", new List<object>() {new Dictionary<string, object>() {
    { "name", "MARKET_TICKER" },
    { "price_points_mode", "INLINE" },
}} },
        };
        return await this.watchMany(messageHash, request, subscriptionHash, new List<object>() {symbol}, parameters);
    }

    /**
     * @method
     * @name onetrading#watchTickers
     * @see https://developers.bitpanda.com/exchange/#market-ticker-channel
     * @description watches price tickers, a statistical calculation with the information for all markets or those specified.
     * @param {string} symbols unified symbols of the markets to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an array of [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> watchTickers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        symbols = this.marketSymbols(symbols);
        if (isTrue(isEqual(symbols, null)))
        {
            symbols = new List<object>() {};
        }
        object subscriptionHash = "MARKET_TICKER";
        object messageHash = "tickers";
        object request = new Dictionary<string, object>() {
            { "type", "SUBSCRIBE" },
            { "channels", new List<object>() {new Dictionary<string, object>() {
    { "name", "MARKET_TICKER" },
    { "price_points_mode", "INLINE" },
}} },
        };
        object tickers = await this.watchMany(messageHash, request, subscriptionHash, symbols, parameters);
        return this.filterByArray(tickers, "symbol", symbols);
    }

    public virtual void handleTicker(WebSocketClient client, object message)
    {
        //
        //     {
        //         "ticker_updates": [{
        //             "instrument": "ETH_BTC",
        //             "last_price": "0.053752",
        //             "price_change": "0.000623",
        //             "price_change_percentage": "1.17",
        //             "high": "0.055",
        //             "low": "0.052662",
        //             "volume": "6.3821593247"
        //         }],
        //         "channel_name": "MARKET_TICKER",
        //         "type": "MARKET_TICKER_UPDATES",
        //         "time": "2022-06-23T16:41:00.004162Z"
        //     }
        //
        object tickers = this.safeValue(message, "ticker_updates", new List<object>() {});
        object datetime = this.safeString(message, "time");
        for (object i = 0; isLessThan(i, getArrayLength(tickers)); postFixIncrement(ref i))
        {
            object ticker = getValue(tickers, i);
            object marketId = this.safeString(ticker, "instrument");
            object symbol = this.safeSymbol(marketId);
            ((IDictionary<string,object>)this.tickers)[(string)symbol] = this.parseWSTicker(ticker);
            object timestamp = this.parse8601(datetime);
            ((IDictionary<string,object>)getValue(this.tickers, symbol))["timestamp"] = timestamp;
            ((IDictionary<string,object>)getValue(this.tickers, symbol))["datetime"] = this.iso8601(timestamp);
            callDynamically(client as WebSocketClient, "resolve", new object[] {getValue(this.tickers, symbol), add("ticker.", symbol)});
        }
        callDynamically(client as WebSocketClient, "resolve", new object[] {this.tickers, "tickers"});
    }

    public virtual object parseWSTicker(object ticker, object market = null)
    {
        //
        //     {
        //         "instrument": "ETH_BTC",
        //         "last_price": "0.053752",
        //         "price_change": "-0.000623",
        //         "price_change_percentage": "-1.17",
        //         "high": "0.055",
        //         "low": "0.052662",
        //         "volume": "6.3821593247"
        //     }
        //
        object marketId = this.safeString(ticker, "instrument");
        return this.safeTicker(new Dictionary<string, object>() {
            { "symbol", this.safeSymbol(marketId, market) },
            { "timestamp", null },
            { "datetime", null },
            { "high", this.safeString(ticker, "high") },
            { "low", this.safeString(ticker, "low") },
            { "bid", null },
            { "bidVolume", null },
            { "ask", null },
            { "askVolume", null },
            { "vwap", null },
            { "open", null },
            { "close", this.safeString(ticker, "last_price") },
            { "last", this.safeString(ticker, "last_price") },
            { "previousClose", null },
            { "change", this.safeString(ticker, "price_change") },
            { "percentage", this.safeString(ticker, "price_change_percentage") },
            { "average", null },
            { "baseVolume", null },
            { "quoteVolume", this.safeNumber(ticker, "volume") },
            { "info", ticker },
        }, market);
    }

    /**
     * @method
     * @name onetrading#watchMyTrades
     * @see https://developers.bitpanda.com/exchange/#account-history-channel
     * @description get the list of trades associated with the user
     * @param {string} symbol unified symbol of the market to fetch trades for. Use 'any' to watch all trades
     * @param {int} [since] timestamp in ms of the earliest trade to fetch
     * @param {int} [limit] the maximum amount of trades to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
     */
    public async override Task<object> watchMyTrades(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object messageHash = "myTrades";
        if (isTrue(!isEqual(symbol, null)))
        {
            object market = this.market(symbol);
            symbol = getValue(market, "symbol");
            messageHash = add(messageHash, add(":", symbol));
        }
        await this.authenticate(parameters);
        object url = getValue(getValue(this.urls, "api"), "ws");
        object subscribeHash = "ACCOUNT_HISTORY";
        object bpRemainingQuota = this.safeInteger(this.options, "bp_remaining_quota", 200);
        object subscribe = new Dictionary<string, object>() {
            { "type", "SUBSCRIBE" },
            { "bp_remaining_quota", bpRemainingQuota },
            { "channels", new List<object>() {new Dictionary<string, object>() {
    { "name", "ACCOUNT_HISTORY" },
}} },
        };
        object request = this.deepExtend(subscribe, parameters);
        object trades = await this.watch(url, messageHash, request, subscribeHash, request);
        if (isTrue(this.newUpdates))
        {
            limit = callDynamically(trades, "getLimit", new object[] {symbol, limit});
        }
        trades = this.filterBySymbolSinceLimit(trades, symbol, since, limit);
        object numTrades = getArrayLength(trades);
        if (isTrue(isEqual(numTrades, 0)))
        {
            return await this.watchMyTrades(symbol, since, limit, parameters);
        }
        return trades;
    }

    /**
     * @method
     * @name onetrading#watchOrderBook
     * @see https://developers.bitpanda.com/exchange/#market-ticker-channel
     * @description watches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @param {string} symbol unified symbol of the market to fetch the order book for
     * @param {int} [limit] the maximum amount of order book entries to return
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    public async override Task<object> watchOrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        symbol = getValue(market, "symbol");
        object messageHash = add("book:", symbol);
        object subscriptionHash = "ORDER_BOOK";
        object depth = 0;
        if (isTrue(!isEqual(limit, null)))
        {
            depth = limit;
        }
        object request = new Dictionary<string, object>() {
            { "type", "SUBSCRIBE" },
            { "channels", new List<object>() {new Dictionary<string, object>() {
    { "name", "ORDER_BOOK" },
    { "depth", depth },
}} },
        };
        object orderbook = await this.watchMany(messageHash, request, subscriptionHash, new List<object>() {symbol}, parameters);
        return (orderbook as IOrderBook).limit();
    }

    public virtual void handleOrderBook(WebSocketClient client, object message)
    {
        //
        //  snapshot
        //     {
        //         "instrument_code": "ETH_BTC",
        //         "bids": [
        //             ['0.053595', "4.5352"],
        //             ...
        //         ],
        //         "asks": [
        //             ['0.055455', "0.2821"],
        //             ...
        //         ],
        //         "channel_name": "ORDER_BOOK",
        //         "type": "ORDER_BOOK_SNAPSHOT",
        //         "time": "2022-06-23T15:38:02.196282Z"
        //     }
        //
        //  update
        //     {
        //         "instrument_code": "ETH_BTC",
        //         "changes": [
        //             ["BUY", '0.053593', "8.0587"]
        //         ],
        //         "channel_name": "ORDER_BOOK",
        //         "type": "ORDER_BOOK_UPDATE",
        //         "time": "2022-06-23T15:38:02.751301Z"
        //     }
        //
        object type = this.safeString(message, "type");
        object marketId = this.safeString(message, "instrument_code");
        object symbol = this.safeSymbol(marketId);
        object dateTime = this.safeString(message, "time");
        object timestamp = this.parse8601(dateTime);
        object channel = add("book:", symbol);
        object orderbook = this.safeValue(this.orderbooks, symbol);
        if (isTrue(isEqual(orderbook, null)))
        {
            orderbook = this.orderBook(new Dictionary<string, object>() {});
        }
        if (isTrue(isEqual(type, "ORDER_BOOK_SNAPSHOT")))
        {
            object snapshot = this.parseOrderBook(message, symbol, timestamp, "bids", "asks");
            (orderbook as IOrderBook).reset(snapshot);
        } else if (isTrue(isEqual(type, "ORDER_BOOK_UPDATE")))
        {
            object changes = this.safeValue(message, "changes", new List<object>() {});
            this.handleDeltas(orderbook, changes);
        } else
        {
            throw new NotSupported ((string)add(add(this.id, " watchOrderBook() did not recognize message type "), type)) ;
        }
        ((IDictionary<string,object>)orderbook)["nonce"] = timestamp;
        ((IDictionary<string,object>)orderbook)["timestamp"] = timestamp;
        ((IDictionary<string,object>)orderbook)["datetime"] = this.iso8601(timestamp);
        ((IDictionary<string,object>)this.orderbooks)[(string)symbol] = orderbook;
        callDynamically(client as WebSocketClient, "resolve", new object[] {orderbook, channel});
    }

    public override void handleDelta(object orderbook, object delta)
    {
        //
        //   [ 'BUY', "0.053595", "0" ]
        //
        object bidAsk = this.parseBidAsk(delta, 1, 2);
        object type = this.safeString(delta, 0);
        if (isTrue(isEqual(type, "BUY")))
        {
            object bids = getValue(orderbook, "bids");
            (bids as IOrderBookSide).storeArray(bidAsk);
        } else if (isTrue(isEqual(type, "SELL")))
        {
            object asks = getValue(orderbook, "asks");
            (asks as IOrderBookSide).storeArray(bidAsk);
        } else
        {
            throw new NotSupported ((string)add(add(this.id, " watchOrderBook () received unknown change type "), this.json(delta))) ;
        }
    }

    public override void handleDeltas(object orderbook, object deltas)
    {
        //
        //    [
        //       [ 'BUY', "0.053593", "0" ],
        //       [ 'SELL', "0.053698", "0" ]
        //    ]
        //
        for (object i = 0; isLessThan(i, getArrayLength(deltas)); postFixIncrement(ref i))
        {
            this.handleDelta(orderbook, getValue(deltas, i));
        }
    }

    /**
     * @method
     * @name onetrading#watchOrders
     * @see https://developers.bitpanda.com/exchange/#account-history-channel
     * @description watches information on multiple orders made by the user
     * @param {string} symbol unified market symbol of the market orders were made in
     * @param {int} [since] the earliest time in ms to fetch orders for
     * @param {int} [limit] the maximum number of order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.channel] can listen to orders using ACCOUNT_HISTORY or TRADING
     * @returns {object[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> watchOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object messageHash = "orders";
        if (isTrue(!isEqual(symbol, null)))
        {
            object market = this.market(symbol);
            symbol = getValue(market, "symbol");
            messageHash = add(messageHash, add(":", symbol));
        }
        await this.authenticate(parameters);
        object url = getValue(getValue(this.urls, "api"), "ws");
        object subscribeHash = this.safeString(parameters, "channel", "ACCOUNT_HISTORY");
        object bpRemainingQuota = this.safeInteger(this.options, "bp_remaining_quota", 200);
        object subscribe = new Dictionary<string, object>() {
            { "type", "SUBSCRIBE" },
            { "bp_remaining_quota", bpRemainingQuota },
            { "channels", new List<object>() {new Dictionary<string, object>() {
    { "name", subscribeHash },
}} },
        };
        object request = this.deepExtend(subscribe, parameters);
        object orders = await this.watch(url, messageHash, request, subscribeHash, request);
        if (isTrue(this.newUpdates))
        {
            limit = callDynamically(orders, "getLimit", new object[] {symbol, limit});
        }
        orders = this.filterBySymbolSinceLimit(orders, symbol, since, limit);
        object numOrders = getArrayLength(orders);
        if (isTrue(isEqual(numOrders, 0)))
        {
            return await this.watchOrders(symbol, since, limit, parameters);
        }
        return orders;
    }

    public virtual void handleTrading(WebSocketClient client, object message)
    {
        //
        //     {
        //         "order_book_sequence": 892925263,
        //         "side": "BUY",
        //         "amount": "0.00046",
        //         "trade_id": "d67b9b69-ab76-480f-9ba3-b33582202836",
        //         "matched_as": "TAKER",
        //         "matched_amount": "0.00046",
        //         "matched_price": "22231.08",
        //         "instrument_code": "BTC_EUR",
        //         "order_id": "7b39f316-0a71-4bfd-adda-3062e6f0bd37",
        //         "remaining": "0.0",
        //         "channel_name": "TRADING",
        //         "type": "FILL",
        //         "time": "2022-07-21T12:41:22.883341Z"
        //     }
        //
        //     {
        //         "status": "CANCELLED",
        //         "order_book_sequence": 892928424,
        //         "amount": "0.0003",
        //         "side": "SELL",
        //         "price": "50338.65",
        //         "instrument_code": "BTC_EUR",
        //         "order_id": "b3994a08-a9e8-4a79-a08b-33e3480382df",
        //         "remaining": "0.0003",
        //         "channel_name": "TRADING",
        //         "type": "DONE",
        //         "time": "2022-07-21T12:44:24.267000Z"
        //     }
        //
        //     {
        //         "order_book_sequence": 892934476,
        //         "side": "SELL",
        //         "amount": "0.00051",
        //         "price": "22349.02",
        //         "instrument_code": "BTC_EUR",
        //         "order_id": "1c6c585c-ec3d-4b94-9292-6c3d04a31dc8",
        //         "remaining": "0.00051",
        //         "channel_name": "TRADING",
        //         "type": "BOOKED",
        //         "time": "2022-07-21T12:50:10.093000Z"
        //     }
        //
        if (isTrue(isEqual(this.orders, null)))
        {
            object limit = this.safeInteger(this.options, "ordersLimit", 1000);
            this.orders = new ArrayCacheBySymbolById(limit);
        }
        object order = this.parseTradingOrder(message);
        object orders = this.orders;
        callDynamically(orders, "append", new object[] {order});
        callDynamically(client as WebSocketClient, "resolve", new object[] {this.orders, add("orders:", getValue(order, "symbol"))});
        callDynamically(client as WebSocketClient, "resolve", new object[] {this.orders, "orders"});
    }

    public virtual object parseTradingOrder(object order, object market = null)
    {
        //
        //     {
        //         "order_book_sequence": 892925263,
        //         "side": "BUY",
        //         "amount": "0.00046",
        //         "trade_id": "d67b9b69-ab76-480f-9ba3-b33582202836",
        //         "matched_as": "TAKER",
        //         "matched_amount": "0.00046",
        //         "matched_price": "22231.08",
        //         "instrument_code": "BTC_EUR",
        //         "order_id": "7b39f316-0a71-4bfd-adda-3062e6f0bd37",
        //         "remaining": "0.0",
        //         "channel_name": "TRADING",
        //         "type": "FILL",
        //         "time": "2022-07-21T12:41:22.883341Z"
        //     }
        //
        //     {
        //         "status": "CANCELLED",
        //         "order_book_sequence": 892928424,
        //         "amount": "0.0003",
        //         "side": "SELL",
        //         "price": "50338.65",
        //         "instrument_code": "BTC_EUR",
        //         "order_id": "b3994a08-a9e8-4a79-a08b-33e3480382df",
        //         "remaining": "0.0003",
        //         "channel_name": "TRADING",
        //         "type": "DONE",
        //         "time": "2022-07-21T12:44:24.267000Z"
        //     }
        //
        //     {
        //         "order_book_sequence": 892934476,
        //         "side": "SELL",
        //         "amount": "0.00051",
        //         "price": "22349.02",
        //         "instrument_code": "BTC_EUR",
        //         "order_id": "1c6c585c-ec3d-4b94-9292-6c3d04a31dc8",
        //         "remaining": "0.00051",
        //         "channel_name": "TRADING",
        //         "type": "BOOKED",
        //         "time": "2022-07-21T12:50:10.093000Z"
        //     }
        //
        //     {
        //         "type":"UPDATE",
        //         "channel_name": "TRADING",
        //         "instrument_code": "BTC_EUR",
        //         "order_id": "1e842f13-762a-4745-9f3b-07f1b43e7058",
        //         "client_id": "d75fb03b-b599-49e9-b926-3f0b6d103206",
        //         "time": "2020-01-11T01:01:01.999Z",
        //         "remaining": "1.23456",
        //         "order_book_sequence": 42,
        //         "status": "APPLIED",
        //         "amount": "1.35756",
        //         "amount_delta": "0.123",
        //         "modification_id": "cc0eed67-aecc-4fb4-a625-ff3890ceb4cc"
        //     }
        //  tracked
        //     {
        //         "type": "STOP_TRACKED",
        //         "channel_name": "TRADING",
        //         "instrument_code": "BTC_EUR",
        //         "order_id": "1e842f13-762a-4745-9f3b-07f1b43e7058",
        //         "client_id": "d75fb03b-b599-49e9-b926-3f0b6d103206",
        //         "time": "2020-01-11T01:01:01.999Z",
        //         "remaining": "1.23456",
        //         "order_book_sequence": 42,
        //         "trigger_price": "12345.67",
        //         "current_price": "11111.11"
        //     }
        //
        //     {
        //         "type": "STOP_TRIGGERED",
        //         "channel_name": "TRADING",
        //         "instrument_code": "BTC_EUR",
        //         "order_id": "1e842f13-762a-4745-9f3b-07f1b43e7058",
        //         "client_id": "d75fb03b-b599-49e9-b926-3f0b6d103206",
        //         "time": "2020-01-11T01:01:01.999Z",
        //         "remaining": "1.23456",
        //         "order_book_sequence": 42,
        //         "price": "13333.33"
        //     }
        //
        object datetime = this.safeString(order, "time");
        object marketId = this.safeString(order, "instrument_code");
        object symbol = this.safeSymbol(marketId, market, "_");
        return this.safeOrder(new Dictionary<string, object>() {
            { "id", this.safeString(order, "order_id") },
            { "clientOrderId", this.safeString(order, "client_id") },
            { "info", order },
            { "timestamp", this.parse8601(datetime) },
            { "datetime", datetime },
            { "lastTradeTimestamp", null },
            { "symbol", symbol },
            { "type", null },
            { "timeInForce", null },
            { "postOnly", null },
            { "side", this.safeStringLower(order, "side") },
            { "price", this.safeNumber2(order, "price", "matched_price") },
            { "stopPrice", this.safeNumber(order, "trigger_price") },
            { "amount", this.safeNumber(order, "amount") },
            { "cost", null },
            { "average", null },
            { "filled", null },
            { "remaining", this.safeString(order, "remaining") },
            { "status", this.parseTradingOrderStatus(this.safeString(order, "status")) },
            { "fee", null },
            { "trades", null },
        }, market);
    }

    public virtual object parseTradingOrderStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "CANCELLED", "canceled" },
            { "SELF_TRADE", "rejected" },
            { "FILLED_FULLY", "closed" },
            { "INSUFFICIENT_FUNDS", "rejected" },
            { "INSUFFICIENT_LIQUIDITY", "rejected" },
            { "TIME_TO_MARKET_EXCEEDED", "rejected" },
            { "LAST_PRICE_UNKNOWN", "rejected" },
        };
        return this.safeString(statuses, status, status);
    }

    public virtual void handleOrders(WebSocketClient client, object message)
    {
        //
        //  snapshot
        //     {
        //         "account_id": "4920221a-48dc-423e-b336-bb65baccc7bd",
        //         "orders": [{
        //             "order": {
        //                 "order_id": "30e2de8f-9a34-472f-bcf8-3af4b7757626",
        //                 "account_holder": "49202c1a-48dc-423e-b336-bb65baccc7bd",
        //                 "account_id": "49202c1a-48dc-423e-b336-bb65baccc7bd",
        //                 "instrument_code": "BTC_EUR",
        //                 "time": "2022-06-28T06:10:02.587345Z",
        //                 "side": "SELL",
        //                 "price": "19645.48",
        //                 "amount": "0.00052",
        //                 "filled_amount": "0.00052",
        //                 "type": "MARKET",
        //                 "sequence": **********,
        //                 "status": "FILLED_FULLY",
        //                 "average_price": "19645.48",
        //                 "is_post_only": false,
        //                 "order_book_sequence": *********,
        //                 "time_last_updated": "2022-06-28T06:10:02.766983Z",
        //                 "update_modification_sequence": *********
        //             },
        //             "trades": [{
        //                 "fee": {
        //                     "fee_amount": "0.********",
        //                     "fee_currency": "EUR",
        //                     "fee_percentage": "0.15",
        //                     "fee_group_id": "default",
        //                     "fee_type": "TAKER",
        //                     "running_trading_volume": "0.0",
        //                     "collection_type": "STANDARD"
        //                 },
        //                 "trade": {
        //                     "trade_id": "d83e302e-0b3a-4269-aa7d-ecf007cbe577",
        //                     "order_id": "30e2de8f-9a34-472f-bcf8-3af4b7757626",
        //                     "account_holder": "49203c1a-48dc-423e-b336-bb65baccc7bd",
        //                     "account_id": "4920221a-48dc-423e-b336-bb65baccc7bd",
        //                     "amount": "0.00052",
        //                     "side": "SELL",
        //                     "instrument_code": "BTC_EUR",
        //                     "price": "19645.48",
        //                     "time": "2022-06-28T06:10:02.693246Z",
        //                     "price_tick_sequence": 0,
        //                     "sequence": **********
        //                 }
        //             }]
        //         }],
        //         "channel_name": "ACCOUNT_HISTORY",
        //         "type": "INACTIVE_ORDERS_SNAPSHOT",
        //         "time": "2022-06-28T06:11:52.469242Z"
        //     }
        //
        //
        if (isTrue(isEqual(this.orders, null)))
        {
            object limit = this.safeInteger(this.options, "ordersLimit", 1000);
            this.orders = new ArrayCacheBySymbolById(limit);
        }
        if (isTrue(isEqual(this.myTrades, null)))
        {
            object limit = this.safeInteger(this.options, "tradesLimit", 1000);
            this.myTrades = new ArrayCacheBySymbolById(limit);
        }
        object rawOrders = this.safeValue(message, "orders", new List<object>() {});
        object rawOrdersLength = getArrayLength(rawOrders);
        if (isTrue(isEqual(rawOrdersLength, 0)))
        {
            return;
        }
        object orders = this.orders;
        for (object i = 0; isLessThan(i, getArrayLength(rawOrders)); postFixIncrement(ref i))
        {
            object order = this.parseOrder(getValue(rawOrders, i));
            object symbol = this.safeString(order, "symbol", "");
            callDynamically(orders, "append", new object[] {order});
            callDynamically(client as WebSocketClient, "resolve", new object[] {this.orders, add("orders:", symbol)});
            object rawTrades = this.safeValue(getValue(rawOrders, i), "trades", new List<object>() {});
            for (object ii = 0; isLessThan(ii, getArrayLength(rawTrades)); postFixIncrement(ref ii))
            {
                object trade = this.parseTrade(getValue(rawTrades, ii));
                symbol = this.safeString(trade, "symbol", symbol);
                callDynamically(this.myTrades, "append", new object[] {trade});
                callDynamically(client as WebSocketClient, "resolve", new object[] {this.myTrades, add("myTrades:", symbol)});
            }
        }
        callDynamically(client as WebSocketClient, "resolve", new object[] {this.orders, "orders"});
        callDynamically(client as WebSocketClient, "resolve", new object[] {this.myTrades, "myTrades"});
    }

    public virtual void handleAccountUpdate(WebSocketClient client, object message)
    {
        //
        // order created
        //     {
        //         "account_id": "49302c1a-48dc-423e-b336-bb65baccc7bd",
        //         "sequence": **********,
        //         "update": {
        //             "type": "ORDER_CREATED",
        //             "activity": "TRADING",
        //             "account_holder": "43202c1a-48dc-423e-b336-bb65baccc7bd",
        //             "account_id": "49202c1a-48dc-423e-b336-bb65baccc7bd",
        //             "order_id": "8893fd69-5ebd-496b-aaa4-269b4c18aa77",
        //             "time": "2022-06-29T04:33:29.661257Z",
        //             "order": {
        //                 "time_in_force": "GOOD_TILL_CANCELLED",
        //                 "is_post_only": false,
        //                 "order_id": "8892fd69-5ebd-496b-aaa4-269b4c18aa77",
        //                 "account_holder": "43202c1a-48dc-423e-b336-bb65baccc7bd",
        //                 "account_id": "49302c1a-48dc-423e-b336-bb65baccc7bd",
        //                 "instrument_code": "BTC_EUR",
        //                 "time": "2022-06-29T04:33:29.656896Z",
        //                 "side": "SELL",
        //                 "price": "50338.65",
        //                 "amount": "0.00021",
        //                 "filled_amount": "0.0",
        //                 "type": "LIMIT"
        //             },
        //             "locked": {
        //                 "currency_code": "BTC",
        //                 "amount": "0.00021",
        //                 "new_available": "0.00017",
        //                 "new_locked": "0.00021"
        //             },
        //             "id": "26e9c36a-b231-4bb0-a686-aa915a2fc9e6",
        //             "sequence": **********
        //         },
        //         "channel_name": "ACCOUNT_HISTORY",
        //         "type": "ACCOUNT_UPDATE",
        //         "time": "2022-06-29T04:33:29.684517Z"
        //     }
        //
        //  order rejected
        //     {
        //         "account_id": "49302c1a-48dc-423e-b336-bb65baccc7bd",
        //         "sequence": **********,
        //         "update": {
        //             "id": "d3fe6025-5b27-4df6-a957-98b8d131cb9d",
        //             "type": "ORDER_REJECTED",
        //             "activity": "TRADING",
        //             "account_id": "b355abb8-aaae-4fae-903c-c60ff74723c6",
        //             "sequence": 0,
        //             "timestamp": "2018-08-01T13:39:15.590Z",
        //             "reason": "INSUFFICIENT_FUNDS",
        //             "order_id": "6f991342-da2c-45c6-8830-8bf519cfc8cc",
        //             "client_id": "fb497387-8223-4111-87dc-66a86f98a7cf",
        //             "unlocked": {
        //                 "currency_code": "BTC",
        //                 "amount": "1.5",
        //                 "new_locked": "2.0",
        //                 "new_available": "1.5"
        //             }
        //         }
        //     }
        //
        //  order closed
        //     {
        //         "account_id": "49202c1a-48dc-423e-b336-bb65baccc7bd",
        //         "sequence": **********,
        //         "update": {
        //             "type": "ORDER_CLOSED",
        //             "activity": "TRADING",
        //             "account_holder": "49202c1a-48dc-423e-b336-bb65baccc7bd",
        //             "account_id": "49202c1a-48dc-423e-b336-bb65baccc7bd",
        //             "time": "2022-06-29T04:43:57.169616Z",
        //             "order_id": "8892fd69-5ebd-496b-aaa4-269b4c18aa77",
        //             "unlocked": {
        //                 "currency_code": "BTC",
        //                 "amount": "0.00021",
        //                 "new_available": "0.00038",
        //                 "new_locked": "0.0"
        //             },
        //             "order_book_sequence": *********,
        //             "id": "26c5e1d7-65ba-4a11-a661-14c0130ff484",
        //             "sequence": **********
        //         },
        //         "channel_name": "ACCOUNT_HISTORY",
        //         "type": "ACCOUNT_UPDATE",
        //         "time": "2022-06-29T04:43:57.182153Z"
        //     }
        //
        //  trade settled
        //     {
        //         "account_id": "49202c1a-48dc-423e-b336-bb65baccc7bd",
        //         "sequence": **********,
        //         "update": {
        //             "type": "TRADE_SETTLED",
        //             "activity": "TRADING",
        //             "account_holder": "49202c1a-48dc-423e-b336-bb65baccc7bd",
        //             "account_id": "49202c1a-48dc-423e-b336-bb65baccc7bd",
        //             "time": "2022-06-29T04:46:12.933091Z",
        //             "order_id": "ad19951a-b616-401d-a062-8d0609f038a4",
        //             "order_book_sequence": *********,
        //             "filled_amount": "0.00052",
        //             "order": {
        //                 "amount": "0.00052",
        //                 "filled_amount": "0.00052"
        //             },
        //             "trade": {
        //                 "trade_id": "21039eb9-2df0-4227-be2d-0ea9b691ac66",
        //                 "order_id": "ad19951a-b616-401d-a062-8d0609f038a4",
        //                 "account_holder": "49202c1a-48dc-423e-b336-bb65baccc7bd",
        //                 "account_id": "49202c1a-48dc-423e-b336-bb65baccc7bd",
        //                 "amount": "0.00052",
        //                 "side": "BUY",
        //                 "instrument_code": "BTC_EUR",
        //                 "price": "19309.29",
        //                 "time": "2022-06-29T04:46:12.870581Z",
        //                 "price_tick_sequence": 0
        //             },
        //             "fee": {
        //                 "fee_amount": "0.********",
        //                 "fee_currency": "BTC",
        //                 "fee_percentage": "0.15",
        //                 "fee_group_id": "default",
        //                 "fee_type": "TAKER",
        //                 "running_trading_volume": "0.00052",
        //                 "collection_type": "STANDARD"
        //             },
        //             "spent": {
        //                 "currency_code": "EUR",
        //                 "amount": "10.0408308",
        //                 "new_available": "0.0",
        //                 "new_locked": "0.********"
        //             },
        //             "credited": {
        //                 "currency_code": "BTC",
        //                 "amount": "0.00051922",
        //                 "new_available": "0.00089922",
        //                 "new_locked": "0.0"
        //             },
        //             "unlocked": {
        //                 "currency_code": "EUR",
        //                 "amount": "0.0",
        //                 "new_available": "0.0",
        //                 "new_locked": "0.********"
        //             },
        //             "id": "22b40199-2508-4176-8a14-d4785c933444",
        //             "sequence": **********
        //         },
        //         "channel_name": "ACCOUNT_HISTORY",
        //         "type": "ACCOUNT_UPDATE",
        //         "time": "2022-06-29T04:46:12.941837Z"
        //     }
        //
        //  Trade Settled with BEST fee collection enabled
        //     {
        //         "account_id": "49302c1a-48dc-423e-b336-bb65baccc7bd",
        //         "sequence": **********,
        //         "update": {
        //             "id": "70e00504-d892-456f-9aae-4da7acb36aac",
        //             "sequence": 361792,
        //             "order_book_sequence": 123456,
        //             "type": "TRADE_SETTLED",
        //             "activity": "TRADING",
        //             "account_id": "379a12c0-4560-11e9-82fe-2b25c6f7d123",
        //             "time": "2019-10-22T12:09:55.731Z",
        //             "order_id": "9fcdd91c-7f6e-45f4-9956-61cddba55de5",
        //             "client_id": "fb497387-8223-4111-87dc-66a86f98a7cf",
        //             "order": {
        //                 "amount": "0.5",
        //                 "filled_amount": "0.5"
        //             },
        //             "trade": {
        //                 "trade_id": "a828b63e-b2cb-48f0-8d99-8fc22cf98e08",
        //                 "order_id": "9fcdd91c-7f6e-45f4-9956-61cddba55de5",
        //                 "account_id": "379a12c0-4560-11e9-82fe-2b25c6f7d123",
        //                 "amount": "0.5",
        //                 "side": "BUY",
        //                 "instrument_code": "BTC_EUR",
        //                 "price": "7451.6",
        //                 "time": "2019-10-22T12:09:55.667Z"
        //             },
        //             "fee": {
        //                 "fee_amount": "23.28625",
        //                 "fee_currency": "BEST",
        //                 "fee_percentage": "0.075",
        //                 "fee_group_id": "default",
        //                 "fee_type": "TAKER",
        //                 "running_trading_volume": "0.10058",
        //                 "collection_type": "BEST",
        //                 "applied_best_eur_rate": "1.04402"
        //             },
        //             "spent": {
        //                 "currency_code": "EUR",
        //                 "amount": "3725.8",
        //                 "new_available": "********.*************",
        //                 "new_locked": "2354.882"
        //             },
        //             "spent_on_fees": {
        //                 "currency_code": "BEST",
        //                 "amount": "23.28625",
        //                 "new_available": "9157.31375",
        //                 "new_locked": "0.0"
        //             },
        //             "credited": {
        //                 "currency_code": "BTC",
        //                 "amount": "0.5",
        //                 "new_available": "5839.***********",
        //                 "new_locked": "0.0"
        //             },
        //             "unlocked": {
        //                 "currency_code": "EUR",
        //                 "amount": "0.15",
        //                 "new_available": "********.*************",
        //                 "new_locked": "2354.882"
        //             }
        //         }
        //         "channel_name": "ACCOUNT_HISTORY",
        //         "type": "ACCOUNT_UPDATE",
        //         "time": "2022-06-29T05:18:51.760338Z"
        //     }
        //
        if (isTrue(isEqual(this.orders, null)))
        {
            object limit = this.safeInteger(this.options, "ordersLimit", 1000);
            this.orders = new ArrayCacheBySymbolById(limit);
        }
        if (isTrue(isEqual(this.myTrades, null)))
        {
            object limit = this.safeInteger(this.options, "tradesLimit", 1000);
            this.myTrades = new ArrayCacheBySymbolById(limit);
        }
        object symbol = null;
        object orders = this.orders;
        object update = this.safeValue(message, "update", new Dictionary<string, object>() {});
        object updateType = this.safeString(update, "type");
        if (isTrue(isTrue(isTrue(isEqual(updateType, "ORDER_REJECTED")) || isTrue(isEqual(updateType, "ORDER_CLOSED"))) || isTrue(isEqual(updateType, "STOP_ORDER_TRIGGERED"))))
        {
            object orderId = this.safeString(update, "order_id");
            object datetime = this.safeString2(update, "time", "timestamp");
            object previousOrderArray = this.filterByArray(this.orders, "id", orderId, false);
            object previousOrder = this.safeValue(previousOrderArray, 0, new Dictionary<string, object>() {});
            symbol = getValue(previousOrder, "symbol");
            object filled = this.safeString(update, "filled_amount");
            object status = this.parseWsOrderStatus(updateType);
            if (isTrue(isTrue(isEqual(updateType, "ORDER_CLOSED")) && isTrue(Precise.stringEq(filled, "0"))))
            {
                status = "canceled";
            }
            object orderObject = new Dictionary<string, object>() {
                { "id", orderId },
                { "symbol", symbol },
                { "status", status },
                { "timestamp", this.parse8601(datetime) },
                { "datetime", datetime },
            };
            callDynamically(orders, "append", new object[] {orderObject});
        } else
        {
            object parsed = this.parseOrder(update);
            symbol = this.safeString(parsed, "symbol", "");
            callDynamically(orders, "append", new object[] {parsed});
        }
        callDynamically(client as WebSocketClient, "resolve", new object[] {this.orders, add("orders:", symbol)});
        callDynamically(client as WebSocketClient, "resolve", new object[] {this.orders, "orders"});
        // update balance
        object balanceKeys = new List<object>() {"locked", "unlocked", "spent", "spent_on_fees", "credited", "deducted"};
        for (object i = 0; isLessThan(i, getArrayLength(balanceKeys)); postFixIncrement(ref i))
        {
            object newBalance = this.safeValue(update, getValue(balanceKeys, i));
            if (isTrue(!isEqual(newBalance, null)))
            {
                this.updateBalance(newBalance);
            }
        }
        callDynamically(client as WebSocketClient, "resolve", new object[] {this.balance, "balance"});
        // update trades
        if (isTrue(isEqual(updateType, "TRADE_SETTLED")))
        {
            object parsed = this.parseTrade(update);
            symbol = this.safeString(parsed, "symbol", "");
            object myTrades = this.myTrades;
            callDynamically(myTrades, "append", new object[] {parsed});
            callDynamically(client as WebSocketClient, "resolve", new object[] {this.myTrades, add("myTrades:", symbol)});
            callDynamically(client as WebSocketClient, "resolve", new object[] {this.myTrades, "myTrades"});
        }
    }

    public virtual object parseWsOrderStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "ORDER_REJECTED", "rejected" },
            { "ORDER_CLOSED", "closed" },
            { "STOP_ORDER_TRIGGERED", "triggered" },
        };
        return this.safeString(statuses, status, status);
    }

    public virtual void updateBalance(object balance)
    {
        //
        //     {
        //         "currency_code": "EUR",
        //         "amount": "0.0",
        //         "new_available": "0.0",
        //         "new_locked": "0.********"
        //     }
        //
        object currencyId = this.safeString(balance, "currency_code");
        object code = this.safeCurrencyCode(currencyId);
        object account = this.account();
        ((IDictionary<string,object>)account)["free"] = this.safeString(balance, "new_available");
        ((IDictionary<string,object>)account)["used"] = this.safeString(balance, "new_locked");
        ((IDictionary<string,object>)this.balance)[(string)code] = account;
        this.balance = this.safeBalance(this.balance);
    }

    /**
     * @method
     * @name onetrading#watchOHLCV
     * @see https://developers.bitpanda.com/exchange/#candlesticks-channel
     * @description watches historical candlestick data containing the open, high, low, and close price, and the volume of a market
     * @param {string} symbol unified symbol of the market to fetch OHLCV data for
     * @param {string} timeframe the length of time each candle represents
     * @param {int} [since] timestamp in ms of the earliest candle to fetch
     * @param {int} [limit] the maximum amount of candles to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
     */
    public async override Task<object> watchOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        symbol = getValue(market, "symbol");
        object marketId = getValue(market, "id");
        object url = getValue(getValue(this.urls, "api"), "ws");
        object timeframes = this.safeValue(this.options, "timeframes", new Dictionary<string, object>() {});
        object timeframeId = this.safeValue(timeframes, timeframe);
        if (isTrue(isEqual(timeframeId, null)))
        {
            throw new NotSupported ((string)add(this.id, " this interval is not supported, please provide one of the supported timeframes")) ;
        }
        object messageHash = add(add(add("ohlcv.", symbol), "."), timeframe);
        object subscriptionHash = "CANDLESTICKS";
        var client = this.safeValue(this.clients, url);
        object type = "SUBSCRIBE";
        object subscription = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(client as WebSocketClient, null)))
        {
            subscription = this.safeValue(((WebSocketClient)client).subscriptions, subscriptionHash);
            if (isTrue(!isEqual(subscription, null)))
            {
                object ohlcvMarket = this.safeValue(subscription, marketId, new Dictionary<string, object>() {});
                object marketSubscribed = this.safeBool(ohlcvMarket, timeframe, false);
                if (!isTrue(marketSubscribed))
                {
                    type = "UPDATE_SUBSCRIPTION";
                    ((IDictionary<string,object>)((WebSocketClient)client).subscriptions)[(string)subscriptionHash] = null;
                }
            } else
            {
                subscription = new Dictionary<string, object>() {};
            }
        }
        object subscriptionMarketId = this.safeValue(subscription, marketId);
        if (isTrue(isEqual(subscriptionMarketId, null)))
        {
            ((IDictionary<string,object>)subscription)[(string)marketId] = new Dictionary<string, object>() {};
        }
        ((IDictionary<string,object>)getValue(subscription, marketId))[(string)timeframe] = true;
        object properties = new List<object>() {};
        object marketIds = new List<object>(((IDictionary<string,object>)subscription).Keys);
        for (object i = 0; isLessThan(i, getArrayLength(marketIds)); postFixIncrement(ref i))
        {
            object marketIdtimeframes = new List<object>(((IDictionary<string,object>)getValue(subscription, getValue(marketIds, i))).Keys);
            for (object ii = 0; isLessThan(ii, getArrayLength(marketIdtimeframes)); postFixIncrement(ref ii))
            {
                object marketTimeframeId = this.safeValue(timeframes, timeframe);
                object property = new Dictionary<string, object>() {
                    { "instrument_code", getValue(marketIds, i) },
                    { "time_granularity", marketTimeframeId },
                };
                ((IList<object>)properties).Add(property);
            }
        }
        object request = new Dictionary<string, object>() {
            { "type", type },
            { "channels", new List<object>() {new Dictionary<string, object>() {
    { "name", "CANDLESTICKS" },
    { "properties", properties },
}} },
        };
        object ohlcv = await this.watch(url, messageHash, this.deepExtend(request, parameters), subscriptionHash, subscription);
        if (isTrue(this.newUpdates))
        {
            limit = callDynamically(ohlcv, "getLimit", new object[] {symbol, limit});
        }
        return this.filterBySinceLimit(ohlcv, since, limit, 0, true);
    }

    public virtual void handleOHLCV(WebSocketClient client, object message)
    {
        //
        //  snapshot
        //     {
        //         "instrument_code": "BTC_EUR",
        //         "granularity": { unit: "MONTHS", period: 1 },
        //         "high": "29750.81",
        //         "low": "16764.59",
        //         "open": "29556.02",
        //         "close": "20164.55",
        //         "volume": "107518944.610659",
        //         "last_sequence": 2275507,
        //         "channel_name": "CANDLESTICKS",
        //         "type": "CANDLESTICK_SNAPSHOT",
        //         "time": "2022-06-30T23:59:59.999000Z"
        //     }
        //
        //  update
        //     {
        //         "instrument_code": "BTC_EUR",
        //         "granularity": {
        //             "unit": "MINUTES",
        //             "period": 1
        //         },
        //         "high": "20164.16",
        //         "low": "20164.16",
        //         "open": "20164.16",
        //         "close": "20164.16",
        //         "volume": "3645.2768448",
        //         "last_sequence": 2275511,
        //         "channel_name": "CANDLESTICKS",
        //         "type": "CANDLESTICK",
        //         "time": "2022-06-24T21:20:59.999000Z"
        //     }
        //
        object marketId = this.safeString(message, "instrument_code");
        object symbol = this.safeSymbol(marketId);
        object dateTime = this.safeString(message, "time");
        object timeframeId = this.safeValue(message, "granularity");
        object timeframes = this.safeValue(this.options, "timeframes", new Dictionary<string, object>() {});
        object timeframe = this.findTimeframe(timeframeId, timeframes);
        object channel = add(add(add("ohlcv.", symbol), "."), timeframe);
        object parsed = new List<object> {this.parse8601(dateTime), this.safeNumber(message, "open"), this.safeNumber(message, "high"), this.safeNumber(message, "low"), this.safeNumber(message, "close"), this.safeNumber(message, "volume")};
        ((IDictionary<string,object>)this.ohlcvs)[(string)symbol] = this.safeValue(this.ohlcvs, symbol, new Dictionary<string, object>() {});
        object stored = this.safeValue(getValue(this.ohlcvs, symbol), timeframe);
        if (isTrue(isEqual(stored, null)))
        {
            object limit = this.safeInteger(this.options, "OHLCVLimit", 1000);
            stored = new ArrayCacheByTimestamp(limit);
        }
        callDynamically(stored, "append", new object[] {parsed});
        ((IDictionary<string,object>)getValue(this.ohlcvs, symbol))[(string)timeframe] = stored;
        callDynamically(client as WebSocketClient, "resolve", new object[] {stored, channel});
    }

    public override object findTimeframe(object timeframe, object timeframes = null)
    {
        timeframes = isTrue(timeframes) || isTrue(this.timeframes);
        object keys = new List<object>(((IDictionary<string,object>)timeframes).Keys);
        for (object i = 0; isLessThan(i, getArrayLength(keys)); postFixIncrement(ref i))
        {
            object key = getValue(keys, i);
            if (isTrue(isTrue(isEqual(getValue(getValue(timeframes, key), "unit"), getValue(timeframe, "unit"))) && isTrue(isEqual(getValue(getValue(timeframes, key), "period"), getValue(timeframe, "period")))))
            {
                return key;
            }
        }
        return null;
    }

    public virtual object handleSubscriptions(WebSocketClient client, object message)
    {
        //
        //     {
        //         "channels": [{
        //             "instrument_codes": [Array],
        //             "depth": 0,
        //             "name": "ORDER_BOOK"
        //         }],
        //         "type": "SUBSCRIPTIONS",
        //         "time": "2022-06-23T15:36:26.948282Z"
        //     }
        //
        return message;
    }

    public virtual object handleHeartbeat(WebSocketClient client, object message)
    {
        //
        //     {
        //         "subscription": "SYSTEM",
        //         "channel_name": "SYSTEM",
        //         "type": "HEARTBEAT",
        //         "time": "2022-06-23T16:31:49.170224Z"
        //     }
        //
        return message;
    }

    public virtual object handleErrorMessage(WebSocketClient client, object message)
    {
        throw new ExchangeError ((string)add(add(this.id, " "), this.json(message))) ;
    }

    public override void handleMessage(WebSocketClient client, object message)
    {
        object error = this.safeValue(message, "error");
        if (isTrue(!isEqual(error, null)))
        {
            this.handleErrorMessage(client as WebSocketClient, message);
            return;
        }
        object type = this.safeValue(message, "type");
        object handlers = new Dictionary<string, object>() {
            { "ORDER_BOOK_UPDATE", this.handleOrderBook },
            { "ORDER_BOOK_SNAPSHOT", this.handleOrderBook },
            { "ACTIVE_ORDERS_SNAPSHOT", this.handleOrders },
            { "INACTIVE_ORDERS_SNAPSHOT", this.handleOrders },
            { "ACCOUNT_UPDATE", this.handleAccountUpdate },
            { "BALANCES_SNAPSHOT", this.handleBalanceSnapshot },
            { "SUBSCRIPTIONS", this.handleSubscriptions },
            { "SUBSCRIPTION_UPDATED", this.handleSubscriptions },
            { "PRICE_TICK", this.handleTicker },
            { "PRICE_TICK_HISTORY", this.handleSubscriptions },
            { "HEARTBEAT", this.handleHeartbeat },
            { "MARKET_TICKER_UPDATES", this.handleTicker },
            { "PRICE_POINT_UPDATES", this.handlePricePointUpdates },
            { "CANDLESTICK_SNAPSHOT", this.handleOHLCV },
            { "CANDLESTICK", this.handleOHLCV },
            { "AUTHENTICATED", this.handleAuthenticationMessage },
            { "FILL", this.handleTrading },
            { "DONE", this.handleTrading },
            { "BOOKED", this.handleTrading },
            { "UPDATE", this.handleTrading },
            { "TRACKED", this.handleTrading },
            { "TRIGGERED", this.handleTrading },
            { "STOP_TRACKED", this.handleTrading },
            { "STOP_TRIGGERED", this.handleTrading },
        };
        object handler = this.safeValue(handlers, type);
        if (isTrue(!isEqual(handler, null)))
        {
            DynamicInvoker.InvokeMethod(handler, new object[] { client, message});
        }
    }

    public virtual object handlePricePointUpdates(WebSocketClient client, object message)
    {
        //
        //     {
        //         "channel_name": "MARKET_TICKER",
        //         "type": "PRICE_POINT_UPDATES",
        //         "time": "2019-03-01T10:59:59.999Z",
        //         "price_updates": [{
        //                 "instrument": "BTC_EUR",
        //                 "prices": [{
        //                         "time": "2019-03-01T08:59:59.999Z",
        //                         "close_price": "3580.6"
        //                     },
        //                     ...
        //                 ]
        //             },
        //             ...
        //         ]
        //     }
        //
        return message;
    }

    public virtual object handleAuthenticationMessage(WebSocketClient client, object message)
    {
        //
        //    {
        //        "channel_name": "SYSTEM",
        //        "type": "AUTHENTICATED",
        //        "time": "2022-06-24T20:45:25.447488Z"
        //    }
        //
        var future = this.safeValue((client as WebSocketClient).futures, "authenticated");
        if (isTrue(!isEqual(future, null)))
        {
            (future as Future).resolve(true);
        }
        return message;
    }

    public async virtual Task<object> watchMany(object messageHash, object request, object subscriptionHash, object symbols = null, object parameters = null)
    {
        symbols ??= new List<object>();
        parameters ??= new Dictionary<string, object>();
        object marketIds = new List<object>() {};
        object numSymbols = getArrayLength(symbols);
        if (isTrue(isEqual(numSymbols, 0)))
        {
            marketIds = new List<object>(((IDictionary<string,object>)this.markets_by_id).Keys);
        } else
        {
            marketIds = this.marketIds(symbols);
        }
        object url = getValue(getValue(this.urls, "api"), "ws");
        var client = this.safeValue(this.clients, url);
        object type = "SUBSCRIBE";
        object subscription = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(client as WebSocketClient, null)))
        {
            subscription = this.safeValue(((WebSocketClient)client).subscriptions, subscriptionHash);
            if (isTrue(!isEqual(subscription, null)))
            {
                for (object i = 0; isLessThan(i, getArrayLength(marketIds)); postFixIncrement(ref i))
                {
                    object marketId = getValue(marketIds, i);
                    object marketSubscribed = this.safeBool(subscription, marketId, false);
                    if (!isTrue(marketSubscribed))
                    {
                        type = "UPDATE_SUBSCRIPTION";
                        ((IDictionary<string,object>)((WebSocketClient)client).subscriptions)[(string)subscriptionHash] = null;
                    }
                }
            } else
            {
                subscription = new Dictionary<string, object>() {};
            }
        }
        for (object i = 0; isLessThan(i, getArrayLength(marketIds)); postFixIncrement(ref i))
        {
            object marketId = getValue(marketIds, i);
            ((IDictionary<string,object>)subscription)[(string)marketId] = true;
        }
        ((IDictionary<string,object>)request)["type"] = type;
        ((IDictionary<string,object>)getValue(getValue(request, "channels"), 0))["instrument_codes"] = new List<object>(((IDictionary<string,object>)subscription).Keys);
        return await this.watch(url, messageHash, this.deepExtend(request, parameters), subscriptionHash, subscription);
    }

    public async virtual Task<object> authenticate(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object url = getValue(getValue(this.urls, "api"), "ws");
        var client = this.client(url);
        object messageHash = "authenticated";
        var future = client.future("authenticated");
        object authenticated = this.safeValue(((WebSocketClient)client).subscriptions, messageHash);
        if (isTrue(isEqual(authenticated, null)))
        {
            this.checkRequiredCredentials();
            object request = new Dictionary<string, object>() {
                { "type", "AUTHENTICATE" },
                { "api_token", this.apiKey },
            };
            this.watch(url, messageHash, this.extend(request, parameters), messageHash);
        }
        return await (future as Exchange.Future);
    }
}
