namespace ccxt;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

public partial class kucoinfutures : kucoin
{
    public override object describe()
    {
        return this.deepExtend(base.describe(), new Dictionary<string, object>() {
            { "id", "kucoinfutures" },
            { "name", "KuCoin Futures" },
            { "countries", new List<object>() {"SC"} },
            { "rateLimit", 75 },
            { "version", "v1" },
            { "certified", true },
            { "pro", true },
            { "comment", "Platform 2.0" },
            { "quoteJsonNumbers", false },
            { "has", new Dictionary<string, object>() {
                { "CORS", null },
                { "spot", false },
                { "margin", false },
                { "swap", true },
                { "future", true },
                { "option", false },
                { "addMargin", true },
                { "cancelAllOrders", true },
                { "cancelOrder", true },
                { "cancelOrders", true },
                { "closeAllPositions", false },
                { "closePosition", true },
                { "closePositions", false },
                { "createDepositAddress", true },
                { "createOrder", true },
                { "createOrders", true },
                { "createOrderWithTakeProfitAndStopLoss", true },
                { "createReduceOnlyOrder", true },
                { "createStopLimitOrder", true },
                { "createStopLossOrder", true },
                { "createStopMarketOrder", true },
                { "createStopOrder", true },
                { "createTakeProfitOrder", true },
                { "createTriggerOrder", true },
                { "fetchAccounts", true },
                { "fetchBalance", true },
                { "fetchBidsAsks", true },
                { "fetchBorrowRateHistories", false },
                { "fetchBorrowRateHistory", false },
                { "fetchClosedOrders", true },
                { "fetchCrossBorrowRate", false },
                { "fetchCrossBorrowRates", false },
                { "fetchCurrencies", false },
                { "fetchDepositAddress", true },
                { "fetchDepositAddresses", false },
                { "fetchDepositAddressesByNetwork", false },
                { "fetchDeposits", true },
                { "fetchDepositWithdrawFee", false },
                { "fetchDepositWithdrawFees", false },
                { "fetchFundingHistory", true },
                { "fetchFundingInterval", true },
                { "fetchFundingIntervals", false },
                { "fetchFundingRate", true },
                { "fetchFundingRateHistory", true },
                { "fetchIndexOHLCV", false },
                { "fetchIsolatedBorrowRate", false },
                { "fetchIsolatedBorrowRates", false },
                { "fetchL3OrderBook", true },
                { "fetchLedger", true },
                { "fetchLeverage", true },
                { "fetchLeverageTiers", false },
                { "fetchMarginAdjustmentHistory", false },
                { "fetchMarginMode", true },
                { "fetchMarketLeverageTiers", true },
                { "fetchMarkets", true },
                { "fetchMarkOHLCV", false },
                { "fetchMarkPrice", true },
                { "fetchMyTrades", true },
                { "fetchOHLCV", true },
                { "fetchOpenOrders", true },
                { "fetchOrder", true },
                { "fetchOrderBook", true },
                { "fetchPosition", true },
                { "fetchPositionHistory", false },
                { "fetchPositionMode", false },
                { "fetchPositions", true },
                { "fetchPositionsHistory", true },
                { "fetchPremiumIndexOHLCV", false },
                { "fetchStatus", true },
                { "fetchTicker", true },
                { "fetchTickers", true },
                { "fetchTime", true },
                { "fetchTrades", true },
                { "fetchTradingFee", true },
                { "fetchTransactionFee", false },
                { "fetchWithdrawals", true },
                { "setLeverage", false },
                { "setMarginMode", true },
                { "transfer", true },
                { "withdraw", null },
            } },
            { "urls", new Dictionary<string, object>() {
                { "logo", "https://user-images.githubusercontent.com/1294454/147508995-9e35030a-d046-43a1-a006-6fabd981b554.jpg" },
                { "doc", new List<object>() {"https://docs.kucoin.com/futures", "https://docs.kucoin.com"} },
                { "www", "https://futures.kucoin.com/" },
                { "referral", "https://futures.kucoin.com/?rcode=E5wkqe" },
                { "api", new Dictionary<string, object>() {
                    { "public", "https://openapi-v2.kucoin.com" },
                    { "private", "https://openapi-v2.kucoin.com" },
                    { "futuresPrivate", "https://api-futures.kucoin.com" },
                    { "futuresPublic", "https://api-futures.kucoin.com" },
                    { "webExchange", "https://futures.kucoin.com/_api/web-front" },
                } },
            } },
            { "requiredCredentials", new Dictionary<string, object>() {
                { "apiKey", true },
                { "secret", true },
                { "password", true },
            } },
            { "api", new Dictionary<string, object>() {
                { "futuresPublic", new Dictionary<string, object>() {
                    { "get", new Dictionary<string, object>() {
                        { "contracts/active", 1 },
                        { "contracts/{symbol}", 1 },
                        { "contracts/risk-limit/{symbol}", 1 },
                        { "ticker", 1 },
                        { "allTickers", 1 },
                        { "level2/snapshot", 1.33 },
                        { "level2/depth{limit}", 1 },
                        { "level2/message/query", 1 },
                        { "level3/message/query", 1 },
                        { "level3/snapshot", 1 },
                        { "trade/history", 1 },
                        { "interest/query", 1 },
                        { "index/query", 1 },
                        { "mark-price/{symbol}/current", 1 },
                        { "premium/query", 1 },
                        { "funding-rate/{symbol}/current", 1 },
                        { "timestamp", 1 },
                        { "status", 1 },
                        { "kline/query", 1 },
                    } },
                    { "post", new Dictionary<string, object>() {
                        { "bullet-public", 1 },
                    } },
                } },
                { "futuresPrivate", new Dictionary<string, object>() {
                    { "get", new Dictionary<string, object>() {
                        { "account-overview", 1.33 },
                        { "transaction-history", 4.44 },
                        { "deposit-address", 1 },
                        { "deposit-list", 1 },
                        { "withdrawals/quotas", 1 },
                        { "withdrawal-list", 1 },
                        { "transfer-list", 1 },
                        { "orders", 1.33 },
                        { "stopOrders", 1 },
                        { "recentDoneOrders", 1 },
                        { "orders/{orderId}", 1 },
                        { "orders/byClientOid", 1 },
                        { "fills", 4.44 },
                        { "recentFills", 4.44 },
                        { "openOrderStatistics", 1 },
                        { "position", 1 },
                        { "positions", 4.44 },
                        { "funding-history", 4.44 },
                        { "sub/api-key", 1 },
                        { "trade-statistics", 1 },
                        { "trade-fees", 1 },
                        { "history-positions", 1 },
                        { "getMaxOpenSize", 1 },
                        { "getCrossUserLeverage", 1 },
                        { "position/getMarginMode", 1 },
                    } },
                    { "post", new Dictionary<string, object>() {
                        { "withdrawals", 1 },
                        { "transfer-out", 1 },
                        { "transfer-in", 1 },
                        { "orders", 1.33 },
                        { "st-orders", 1.33 },
                        { "orders/test", 1.33 },
                        { "position/margin/auto-deposit-status", 1 },
                        { "position/margin/deposit-margin", 1 },
                        { "position/risk-limit-level/change", 1 },
                        { "bullet-private", 1 },
                        { "sub/api-key", 1 },
                        { "sub/api-key/update", 1 },
                        { "changeCrossUserLeverage", 1 },
                        { "position/changeMarginMode", 1 },
                    } },
                    { "delete", new Dictionary<string, object>() {
                        { "withdrawals/{withdrawalId}", 1 },
                        { "cancel/transfer-out", 1 },
                        { "orders/{orderId}", 1 },
                        { "orders", 4.44 },
                        { "stopOrders", 1 },
                        { "sub/api-key", 1 },
                        { "orders/client-order/{clientOid}", 1 },
                        { "orders/multi-cancel", 20 },
                    } },
                } },
                { "webExchange", new Dictionary<string, object>() {
                    { "get", new Dictionary<string, object>() {
                        { "contract/{symbol}/funding-rates", 1 },
                    } },
                } },
            } },
            { "precisionMode", TICK_SIZE },
            { "exceptions", new Dictionary<string, object>() {
                { "exact", new Dictionary<string, object>() {
                    { "400", typeof(BadRequest) },
                    { "401", typeof(AuthenticationError) },
                    { "403", typeof(NotSupported) },
                    { "404", typeof(NotSupported) },
                    { "405", typeof(NotSupported) },
                    { "415", typeof(BadRequest) },
                    { "429", typeof(RateLimitExceeded) },
                    { "500", typeof(ExchangeNotAvailable) },
                    { "503", typeof(ExchangeNotAvailable) },
                    { "100001", typeof(OrderNotFound) },
                    { "100004", typeof(BadRequest) },
                    { "101030", typeof(PermissionDenied) },
                    { "200004", typeof(InsufficientFunds) },
                    { "230003", typeof(InsufficientFunds) },
                    { "260100", typeof(InsufficientFunds) },
                    { "300003", typeof(InsufficientFunds) },
                    { "300012", typeof(InvalidOrder) },
                    { "400001", typeof(AuthenticationError) },
                    { "400002", typeof(InvalidNonce) },
                    { "400003", typeof(AuthenticationError) },
                    { "400004", typeof(AuthenticationError) },
                    { "400005", typeof(AuthenticationError) },
                    { "400006", typeof(AuthenticationError) },
                    { "400007", typeof(AuthenticationError) },
                    { "404000", typeof(NotSupported) },
                    { "400100", typeof(BadRequest) },
                    { "411100", typeof(AccountSuspended) },
                    { "500000", typeof(ExchangeNotAvailable) },
                    { "300009", typeof(InvalidOrder) },
                    { "330008", typeof(InsufficientFunds) },
                } },
                { "broad", new Dictionary<string, object>() {
                    { "Position does not exist", typeof(OrderNotFound) },
                } },
            } },
            { "fees", new Dictionary<string, object>() {
                { "trading", new Dictionary<string, object>() {
                    { "tierBased", true },
                    { "percentage", true },
                    { "taker", this.parseNumber("0.0006") },
                    { "maker", this.parseNumber("0.0002") },
                    { "tiers", new Dictionary<string, object>() {
                        { "taker", new List<object>() {new List<object> {this.parseNumber("0"), this.parseNumber("0.0006")}, new List<object> {this.parseNumber("50"), this.parseNumber("0.0006")}, new List<object> {this.parseNumber("200"), this.parseNumber("0.0006")}, new List<object> {this.parseNumber("500"), this.parseNumber("0.0005")}, new List<object> {this.parseNumber("1000"), this.parseNumber("0.0004")}, new List<object> {this.parseNumber("2000"), this.parseNumber("0.0004")}, new List<object> {this.parseNumber("4000"), this.parseNumber("0.00038")}, new List<object> {this.parseNumber("8000"), this.parseNumber("0.00035")}, new List<object> {this.parseNumber("15000"), this.parseNumber("0.00032")}, new List<object> {this.parseNumber("25000"), this.parseNumber("0.0003")}, new List<object> {this.parseNumber("40000"), this.parseNumber("0.0003")}, new List<object> {this.parseNumber("60000"), this.parseNumber("0.0003")}, new List<object> {this.parseNumber("80000"), this.parseNumber("0.0003")}} },
                        { "maker", new List<object>() {new List<object> {this.parseNumber("0"), this.parseNumber("0.02")}, new List<object> {this.parseNumber("50"), this.parseNumber("0.015")}, new List<object> {this.parseNumber("200"), this.parseNumber("0.01")}, new List<object> {this.parseNumber("500"), this.parseNumber("0.01")}, new List<object> {this.parseNumber("1000"), this.parseNumber("0.01")}, new List<object> {this.parseNumber("2000"), this.parseNumber("0")}, new List<object> {this.parseNumber("4000"), this.parseNumber("0")}, new List<object> {this.parseNumber("8000"), this.parseNumber("0")}, new List<object> {this.parseNumber("15000"), this.parseNumber("-0.003")}, new List<object> {this.parseNumber("25000"), this.parseNumber("-0.006")}, new List<object> {this.parseNumber("40000"), this.parseNumber("-0.009")}, new List<object> {this.parseNumber("60000"), this.parseNumber("-0.012")}, new List<object> {this.parseNumber("80000"), this.parseNumber("-0.015")}} },
                    } },
                } },
                { "funding", new Dictionary<string, object>() {
                    { "tierBased", false },
                    { "percentage", false },
                    { "withdraw", new Dictionary<string, object>() {} },
                    { "deposit", new Dictionary<string, object>() {} },
                } },
            } },
            { "commonCurrencies", new Dictionary<string, object>() {
                { "HOT", "HOTNOW" },
                { "EDGE", "DADI" },
                { "WAX", "WAXP" },
                { "TRY", "Trias" },
                { "VAI", "VAIOT" },
                { "XBT", "BTC" },
            } },
            { "timeframes", new Dictionary<string, object>() {
                { "1m", 1 },
                { "3m", null },
                { "5m", 5 },
                { "15m", 15 },
                { "30m", 30 },
                { "1h", 60 },
                { "2h", 120 },
                { "4h", 240 },
                { "6h", null },
                { "8h", 480 },
                { "12h", 720 },
                { "1d", 1440 },
                { "1w", 10080 },
            } },
            { "options", new Dictionary<string, object>() {
                { "version", "v1" },
                { "symbolSeparator", "-" },
                { "defaultType", "swap" },
                { "code", "USDT" },
                { "marginModes", new Dictionary<string, object>() {} },
                { "marginTypes", new Dictionary<string, object>() {} },
                { "versions", new Dictionary<string, object>() {
                    { "futuresPrivate", new Dictionary<string, object>() {
                        { "GET", new Dictionary<string, object>() {
                            { "getMaxOpenSize", "v2" },
                            { "getCrossUserLeverage", "v2" },
                            { "position/getMarginMode", "v2" },
                        } },
                        { "POST", new Dictionary<string, object>() {
                            { "transfer-out", "v2" },
                            { "changeCrossUserLeverage", "v2" },
                            { "position/changeMarginMode", "v2" },
                        } },
                    } },
                    { "futuresPublic", new Dictionary<string, object>() {
                        { "GET", new Dictionary<string, object>() {
                            { "level3/snapshot", "v2" },
                        } },
                    } },
                } },
                { "networks", new Dictionary<string, object>() {
                    { "OMNI", "omni" },
                    { "ERC20", "eth" },
                    { "TRC20", "trx" },
                } },
            } },
            { "features", new Dictionary<string, object>() {
                { "spot", null },
                { "forDerivs", new Dictionary<string, object>() {
                    { "sandbox", false },
                    { "createOrder", new Dictionary<string, object>() {
                        { "marginMode", true },
                        { "triggerPrice", true },
                        { "triggerPriceType", new Dictionary<string, object>() {
                            { "last", true },
                            { "mark", true },
                            { "index", true },
                        } },
                        { "triggerDirection", true },
                        { "stopLossPrice", true },
                        { "takeProfitPrice", true },
                        { "attachedStopLossTakeProfit", new Dictionary<string, object>() {
                            { "triggerPriceType", null },
                            { "price", true },
                        } },
                        { "timeInForce", new Dictionary<string, object>() {
                            { "IOC", true },
                            { "FOK", false },
                            { "PO", true },
                            { "GTD", false },
                        } },
                        { "hedged", false },
                        { "trailing", false },
                        { "leverage", true },
                        { "marketBuyByCost", true },
                        { "marketBuyRequiresPrice", false },
                        { "selfTradePrevention", true },
                        { "iceberg", true },
                    } },
                    { "createOrders", new Dictionary<string, object>() {
                        { "max", 20 },
                    } },
                    { "fetchMyTrades", new Dictionary<string, object>() {
                        { "marginMode", true },
                        { "limit", 1000 },
                        { "daysBack", null },
                        { "untilDays", 7 },
                        { "symbolRequired", false },
                    } },
                    { "fetchOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchOpenOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 1000 },
                        { "trigger", true },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchOrders", null },
                    { "fetchClosedOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 1000 },
                        { "daysBack", null },
                        { "daysBackCanceled", null },
                        { "untilDays", null },
                        { "trigger", true },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchOHLCV", new Dictionary<string, object>() {
                        { "limit", 500 },
                    } },
                } },
                { "swap", new Dictionary<string, object>() {
                    { "linear", new Dictionary<string, object>() {
                        { "extends", "forDerivs" },
                    } },
                    { "inverse", new Dictionary<string, object>() {
                        { "extends", "forDerivs" },
                    } },
                } },
                { "future", new Dictionary<string, object>() {
                    { "linear", new Dictionary<string, object>() {
                        { "extends", "forDerivs" },
                    } },
                    { "inverse", new Dictionary<string, object>() {
                        { "extends", "forDerivs" },
                    } },
                } },
            } },
        });
    }

    /**
     * @method
     * @name kucoinfutures#fetchStatus
     * @description the latest known information on the availability of the exchange API
     * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-service-status
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [status structure]{@link https://docs.ccxt.com/#/?id=exchange-status-structure}
     */
    public async override Task<object> fetchStatus(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.futuresPublicGetStatus(parameters);
        //
        //     {
        //         "code":"200000",
        //         "data":{
        //             "status": "open", // open, close, cancelonly
        //             "msg": "upgrade match engine" // remark for operation when status not open
        //         }
        //     }
        //
        object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        object status = this.safeString(data, "status");
        return new Dictionary<string, object>() {
            { "status", ((bool) isTrue((isEqual(status, "open")))) ? "ok" : "maintenance" },
            { "updated", null },
            { "eta", null },
            { "url", null },
            { "info", response },
        };
    }

    /**
     * @method
     * @name kucoinfutures#fetchMarkets
     * @description retrieves data on all markets for kucoinfutures
     * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-symbols-list
     * @param {object} [params] extra parameters specific to the exchange api endpoint
     * @returns {object[]} an array of objects representing market data
     */
    public async override Task<object> fetchMarkets(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.futuresPublicGetContractsActive(parameters);
        //
        //    {
        //        "code": "200000",
        //        "data": {
        //            "symbol": "ETHUSDTM",
        //            "rootSymbol": "USDT",
        //            "type": "FFWCSX",
        //            "firstOpenDate": 1591086000000,
        //            "expireDate": null,
        //            "settleDate": null,
        //            "baseCurrency": "ETH",
        //            "quoteCurrency": "USDT",
        //            "settleCurrency": "USDT",
        //            "maxOrderQty": 1000000,
        //            "maxPrice": 1000000.********00,
        //            "lotSize": 1,
        //            "tickSize": 0.05,
        //            "indexPriceTickSize": 0.01,
        //            "multiplier": 0.01,
        //            "initialMargin": 0.01,
        //            "maintainMargin": 0.005,
        //            "maxRiskLimit": 1000000,
        //            "minRiskLimit": 1000000,
        //            "riskStep": 500000,
        //            "makerFeeRate": 0.00020,
        //            "takerFeeRate": 0.00060,
        //            "takerFixFee": 0.********00,
        //            "makerFixFee": 0.********00,
        //            "settlementFee": null,
        //            "isDeleverage": true,
        //            "isQuanto": true,
        //            "isInverse": false,
        //            "markMethod": "FairPrice",
        //            "fairMethod": "FundingRate",
        //            "fundingBaseSymbol": ".ETHINT8H",
        //            "fundingQuoteSymbol": ".USDTINT8H",
        //            "fundingRateSymbol": ".ETHUSDTMFPI8H",
        //            "indexSymbol": ".KETHUSDT",
        //            "settlementSymbol": "",
        //            "status": "Open",
        //            "fundingFeeRate": 0.000535,
        //            "predictedFundingFeeRate": 0.002197,
        //            "openInterest": "8724443",
        //            "turnoverOf24h": 341156641.03354263,
        //            "volumeOf24h": 74833.54000000,
        //            "markPrice": 4534.07,
        //            "indexPrice":4531.92,
        //            "lastTradePrice": 4545.45********,
        //            "nextFundingRateTime": 25481884,
        //            "maxLeverage": 100,
        //            "sourceExchanges":  [ "huobi", "Okex", "Binance", "Kucoin", "Poloniex", "Hitbtc" ],
        //            "premiumsSymbol1M": ".ETHUSDTMPI",
        //            "premiumsSymbol8H": ".ETHUSDTMPI8H",
        //            "fundingBaseSymbol1M": ".ETHINT",
        //            "fundingQuoteSymbol1M": ".USDTINT",
        //            "lowPrice": 4456.90,
        //            "highPrice":  4674.25,
        //            "priceChgPct": 0.0046,
        //            "priceChg": 21.15
        //        }
        //    }
        //
        object result = new List<object>() {};
        object data = this.safeList(response, "data", new List<object>() {});
        for (object i = 0; isLessThan(i, getArrayLength(data)); postFixIncrement(ref i))
        {
            object market = getValue(data, i);
            object id = this.safeString(market, "symbol");
            object expiry = this.safeInteger(market, "expireDate");
            object future = ((bool) isTrue(expiry)) ? true : false;
            object swap = !isTrue(future);
            object baseId = this.safeString(market, "baseCurrency");
            object quoteId = this.safeString(market, "quoteCurrency");
            object settleId = this.safeString(market, "settleCurrency");
            object bs = this.safeCurrencyCode(baseId);
            object quote = this.safeCurrencyCode(quoteId);
            object settle = this.safeCurrencyCode(settleId);
            object symbol = add(add(add(add(bs, "/"), quote), ":"), settle);
            object type = "swap";
            if (isTrue(future))
            {
                symbol = add(add(symbol, "-"), this.yymmdd(expiry, ""));
                type = "future";
            }
            object inverse = this.safeValue(market, "isInverse");
            object status = this.safeString(market, "status");
            object multiplier = this.safeString(market, "multiplier");
            object tickSize = this.safeNumber(market, "tickSize");
            object lotSize = this.safeNumber(market, "lotSize");
            object limitAmountMin = lotSize;
            if (isTrue(isEqual(limitAmountMin, null)))
            {
                limitAmountMin = this.safeNumber(market, "baseMinSize");
            }
            object limitAmountMax = this.safeNumber(market, "maxOrderQty");
            if (isTrue(isEqual(limitAmountMax, null)))
            {
                limitAmountMax = this.safeNumber(market, "baseMaxSize");
            }
            object limitPriceMax = this.safeNumber(market, "maxPrice");
            if (isTrue(isEqual(limitPriceMax, null)))
            {
                object baseMinSizeString = this.safeString(market, "baseMinSize");
                object quoteMaxSizeString = this.safeString(market, "quoteMaxSize");
                limitPriceMax = this.parseNumber(Precise.stringDiv(quoteMaxSizeString, baseMinSizeString));
            }
            ((IList<object>)result).Add(new Dictionary<string, object>() {
                { "id", id },
                { "symbol", symbol },
                { "base", bs },
                { "quote", quote },
                { "settle", settle },
                { "baseId", baseId },
                { "quoteId", quoteId },
                { "settleId", settleId },
                { "type", type },
                { "spot", false },
                { "margin", false },
                { "swap", swap },
                { "future", future },
                { "option", false },
                { "active", (isEqual(status, "Open")) },
                { "contract", true },
                { "linear", !isTrue(inverse) },
                { "inverse", inverse },
                { "taker", this.safeNumber(market, "takerFeeRate") },
                { "maker", this.safeNumber(market, "makerFeeRate") },
                { "contractSize", this.parseNumber(Precise.stringAbs(multiplier)) },
                { "expiry", expiry },
                { "expiryDatetime", this.iso8601(expiry) },
                { "strike", null },
                { "optionType", null },
                { "precision", new Dictionary<string, object>() {
                    { "amount", lotSize },
                    { "price", tickSize },
                } },
                { "limits", new Dictionary<string, object>() {
                    { "leverage", new Dictionary<string, object>() {
                        { "min", this.parseNumber("1") },
                        { "max", this.safeNumber(market, "maxLeverage") },
                    } },
                    { "amount", new Dictionary<string, object>() {
                        { "min", limitAmountMin },
                        { "max", limitAmountMax },
                    } },
                    { "price", new Dictionary<string, object>() {
                        { "min", tickSize },
                        { "max", limitPriceMax },
                    } },
                    { "cost", new Dictionary<string, object>() {
                        { "min", this.safeNumber(market, "quoteMinSize") },
                        { "max", this.safeNumber(market, "quoteMaxSize") },
                    } },
                } },
                { "created", this.safeInteger(market, "firstOpenDate") },
                { "info", market },
            });
        }
        return result;
    }

    /**
     * @method
     * @name kucoinfutures#fetchTime
     * @description fetches the current integer timestamp in milliseconds from the exchange server
     * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-server-time
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {int} the current integer timestamp in milliseconds from the exchange server
     */
    public async override Task<object> fetchTime(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.futuresPublicGetTimestamp(parameters);
        //
        //    {
        //        "code": "200000",
        //        "data": 1637385119302,
        //    }
        //
        return this.safeInteger(response, "data");
    }

    /**
     * @method
     * @name kucoinfutures#fetchOHLCV
     * @description fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
     * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-klines
     * @param {string} symbol unified symbol of the market to fetch OHLCV data for
     * @param {string} timeframe the length of time each candle represents
     * @param {int} [since] timestamp in ms of the earliest candle to fetch
     * @param {int} [limit] the maximum amount of candles to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
     */
    public async override Task<object> fetchOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchOHLCV", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDeterministic("fetchOHLCV", symbol, since, limit, timeframe, parameters, 200);
        }
        object market = this.market(symbol);
        object marketId = getValue(market, "id");
        object parsedTimeframe = this.safeInteger(this.timeframes, timeframe);
        object request = new Dictionary<string, object>() {
            { "symbol", marketId },
        };
        if (isTrue(!isEqual(parsedTimeframe, null)))
        {
            ((IDictionary<string,object>)request)["granularity"] = parsedTimeframe;
        } else
        {
            ((IDictionary<string,object>)request)["granularity"] = timeframe;
        }
        object duration = multiply(this.parseTimeframe(timeframe), 1000);
        object endAt = this.milliseconds();
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["from"] = since;
            if (isTrue(isEqual(limit, null)))
            {
                limit = this.safeInteger(this.options, "fetchOHLCVLimit", 200);
            }
            endAt = this.sum(since, multiply(limit, duration));
        } else if (isTrue(!isEqual(limit, null)))
        {
            since = subtract(endAt, multiply(limit, duration));
            ((IDictionary<string,object>)request)["from"] = since;
        }
        ((IDictionary<string,object>)request)["to"] = endAt;
        object response = await this.futuresPublicGetKlineQuery(this.extend(request, parameters));
        //
        //    {
        //        "code": "200000",
        //        "data": [
        //            [1636459200000, 4779.3, 4792.1, 4768.7, 4770.3, 78051],
        //            [1636460100000, 4770.25, 4778.55, 4757.55, 4777.25, 80164],
        //            [1636461000000, 4777.25, 4791.45, 4774.5, 4791.3, 51555]
        //        ]
        //    }
        //
        object data = this.safeList(response, "data", new List<object>() {});
        return this.parseOHLCVs(data, market, timeframe, since, limit);
    }

    public override object parseOHLCV(object ohlcv, object market = null)
    {
        //
        //    [
        //        "1545904980000",          // Start time of the candle cycle
        //        "0.058",                  // opening price
        //        "0.049",                  // closing price
        //        "0.058",                  // highest price
        //        "0.049",                  // lowest price
        //        "0.018",                  // base volume
        //        "0.000945",               // quote volume
        //    ]
        //
        return new List<object> {this.safeInteger(ohlcv, 0), this.safeNumber(ohlcv, 1), this.safeNumber(ohlcv, 2), this.safeNumber(ohlcv, 3), this.safeNumber(ohlcv, 4), this.safeNumber(ohlcv, 5)};
    }

    /**
     * @method
     * @name kucoinfutures#fetchDepositAddress
     * @description fetch the deposit address for a currency associated with this account
     * @see https://www.kucoin.com/docs/rest/funding/deposit/get-deposit-address
     * @param {string} code unified currency code
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
     */
    public async override Task<object> fetchDepositAddress(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object currencyId = getValue(currency, "id");
        object request = new Dictionary<string, object>() {
            { "currency", currencyId },
        };
        object response = await this.futuresPrivateGetDepositAddress(this.extend(request, parameters));
        //
        //    {
        //        "code": "200000",
        //        "data": {
        //            "address": "0x78d3ad1c0aa1bf068e19c94a2d7b16c9c0fcd8b1",//Deposit address
        //            "memo": null//Address tag. If the returned value is null, it means that the requested token has no memo. If you are to transfer funds from another platform to KuCoin Futures and if the token to be //transferred has memo(tag), you need to fill in the memo to ensure the transferred funds will be sent //to the address you specified.
        //        }
        //    }
        //
        object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        object address = this.safeString(data, "address");
        if (isTrue(!isEqual(currencyId, "NIM")))
        {
            // contains spaces
            this.checkAddress(address);
        }
        return new Dictionary<string, object>() {
            { "info", response },
            { "currency", currencyId },
            { "network", this.safeString(data, "chain") },
            { "address", address },
            { "tag", this.safeString(data, "memo") },
        };
    }

    /**
     * @method
     * @name kucoinfutures#fetchOrderBook
     * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-part-order-book-level-2
     * @param {string} symbol unified symbol of the market to fetch the order book for
     * @param {int} [limit] the maximum amount of order book entries to return
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    public async override Task<object> fetchOrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object level = this.safeNumber(parameters, "level");
        if (isTrue(isTrue(!isEqual(level, 2)) && isTrue(!isEqual(level, null))))
        {
            throw new BadRequest ((string)add(this.id, " fetchOrderBook() can only return level 2")) ;
        }
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        if (isTrue(!isEqual(limit, null)))
        {
            if (isTrue(isTrue((isEqual(limit, 20))) || isTrue((isEqual(limit, 100)))))
            {
                ((IDictionary<string,object>)request)["limit"] = limit;
            } else
            {
                throw new BadRequest ((string)add(this.id, " fetchOrderBook() limit argument must be 20 or 100")) ;
            }
        } else
        {
            ((IDictionary<string,object>)request)["limit"] = 20;
        }
        object response = await this.futuresPublicGetLevel2DepthLimit(this.extend(request, parameters));
        //
        //     {
        //         "code": "200000",
        //         "data": {
        //           "symbol": "XBTUSDM",      //Symbol
        //           "sequence": 100,          //Ticker sequence number
        //           "asks": [
        //                 ["5000.0", 1000],   //Price, quantity
        //                 ["6000.0", 1983]    //Price, quantity
        //           ],
        //           "bids": [
        //                 ["3200.0", 800],    //Price, quantity
        //                 ["3100.0", 100]     //Price, quantity
        //           ],
        //           "ts": 1604643655040584408  // timestamp
        //         }
        //     }
        //
        object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        object timestamp = this.parseToInt(divide(this.safeInteger(data, "ts"), 1000000));
        object orderbook = this.parseOrderBook(data, getValue(market, "symbol"), timestamp, "bids", "asks", 0, 1);
        ((IDictionary<string,object>)orderbook)["nonce"] = this.safeInteger(data, "sequence");
        return orderbook;
    }

    /**
     * @method
     * @name kucoinfutures#fetchTicker
     * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
     * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-ticker
     * @param {string} symbol unified symbol of the market to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTicker(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object response = await this.futuresPublicGetTicker(this.extend(request, parameters));
        //
        //    {
        //        "code": "200000",
        //        "data": {
        //            "sequence": 1638444978558,
        //            "symbol": "ETHUSDTM",
        //            "side": "sell",
        //            "size": 4,
        //            "price": "4229.35",
        //            "bestBidSize": 2160,
        //            "bestBidPrice": "4229.0",
        //            "bestAskPrice": "4229.05",
        //            "tradeId": "61aaa8b777a0c43055fe4851",
        //            "ts": 1638574296209786785,
        //            "bestAskSize": 36,
        //        }
        //    }
        //
        return this.parseTicker(getValue(response, "data"), market);
    }

    /**
     * @method
     * @name kucoinfutures#fetchMarkPrice
     * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
     * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-current-mark-price
     * @param {string} symbol unified symbol of the market to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchMarkPrice(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object response = await this.futuresPublicGetMarkPriceSymbolCurrent(this.extend(request, parameters));
        //
        return this.parseTicker(getValue(response, "data"), market);
    }

    /**
     * @method
     * @name kucoinfutures#fetchTickers
     * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
     * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-symbols-list
     * @param {string[]} [symbols] unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.method] the method to use, futuresPublicGetAllTickers or futuresPublicGetContractsActive
     * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTickers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        symbols = this.marketSymbols(symbols);
        object method = null;
        var methodparametersVariable = this.handleOptionAndParams(parameters, "fetchTickers", "method", "futuresPublicGetContractsActive");
        method = ((IList<object>)methodparametersVariable)[0];
        parameters = ((IList<object>)methodparametersVariable)[1];
        object response = null;
        if (isTrue(isEqual(method, "futuresPublicGetAllTickers")))
        {
            response = await this.futuresPublicGetAllTickers(parameters);
        } else
        {
            response = await this.futuresPublicGetContractsActive(parameters);
        }
        //
        //    {
        //        "code": "200000",
        //        "data": {
        //            "symbol": "ETHUSDTM",
        //            "rootSymbol": "USDT",
        //            "type": "FFWCSX",
        //            "firstOpenDate": 1591086000000,
        //            "expireDate": null,
        //            "settleDate": null,
        //            "baseCurrency": "ETH",
        //            "quoteCurrency": "USDT",
        //            "settleCurrency": "USDT",
        //            "maxOrderQty": 1000000,
        //            "maxPrice": 1000000.********00,
        //            "lotSize": 1,
        //            "tickSize": 0.05,
        //            "indexPriceTickSize": 0.01,
        //            "multiplier": 0.01,
        //            "initialMargin": 0.01,
        //            "maintainMargin": 0.005,
        //            "maxRiskLimit": 1000000,
        //            "minRiskLimit": 1000000,
        //            "riskStep": 500000,
        //            "makerFeeRate": 0.00020,
        //            "takerFeeRate": 0.00060,
        //            "takerFixFee": 0.********00,
        //            "makerFixFee": 0.********00,
        //            "settlementFee": null,
        //            "isDeleverage": true,
        //            "isQuanto": true,
        //            "isInverse": false,
        //            "markMethod": "FairPrice",
        //            "fairMethod": "FundingRate",
        //            "fundingBaseSymbol": ".ETHINT8H",
        //            "fundingQuoteSymbol": ".USDTINT8H",
        //            "fundingRateSymbol": ".ETHUSDTMFPI8H",
        //            "indexSymbol": ".KETHUSDT",
        //            "settlementSymbol": "",
        //            "status": "Open",
        //            "fundingFeeRate": 0.000535,
        //            "predictedFundingFeeRate": 0.002197,
        //            "openInterest": "8724443",
        //            "turnoverOf24h": 341156641.03354263,
        //            "volumeOf24h": 74833.54000000,
        //            "markPrice": 4534.07,
        //            "indexPrice":4531.92,
        //            "lastTradePrice": 4545.45********,
        //            "nextFundingRateTime": 25481884,
        //            "maxLeverage": 100,
        //            "sourceExchanges":  [ "huobi", "Okex", "Binance", "Kucoin", "Poloniex", "Hitbtc" ],
        //            "premiumsSymbol1M": ".ETHUSDTMPI",
        //            "premiumsSymbol8H": ".ETHUSDTMPI8H",
        //            "fundingBaseSymbol1M": ".ETHINT",
        //            "fundingQuoteSymbol1M": ".USDTINT",
        //            "lowPrice": 4456.90,
        //            "highPrice":  4674.25,
        //            "priceChgPct": 0.0046,
        //            "priceChg": 21.15
        //        }
        //    }
        //
        object data = this.safeList(response, "data");
        object tickers = this.parseTickers(data, symbols);
        return this.filterByArrayTickers(tickers, "symbol", symbols);
    }

    public override object parseTicker(object ticker, object market = null)
    {
        //
        //     {
        //         "symbol": "LTCUSDTM",
        //         "granularity": 1000,
        //         "timePoint": 1727967339000,
        //         "value": 62.37, mark price
        //         "indexPrice": 62.37
        //      }
        //
        //     {
        //         "code": "200000",
        //         "data": {
        //             "sequence":  1629930362547,
        //             "symbol": "ETHUSDTM",
        //             "side": "buy",
        //             "size":  130,
        //             "price": "4724.7",
        //             "bestBidSize":  5,
        //             "bestBidPrice": "4724.6",
        //             "bestAskPrice": "4724.65",
        //             "tradeId": "618d2a5a77a0c4431d2335f4",
        //             "ts":  1636641371963227600,
        //             "bestAskSize":  1789
        //          }
        //     }
        //
        // from fetchTickers
        //
        // {
        //     symbol: "XBTUSDTM",
        //     rootSymbol: "USDT",
        //     type: "FFWCSX",
        //     firstOpenDate: 1585555200000,
        //     expireDate: null,
        //     settleDate: null,
        //     baseCurrency: "XBT",
        //     quoteCurrency: "USDT",
        //     settleCurrency: "USDT",
        //     maxOrderQty: 1000000,
        //     maxPrice: 1000000,
        //     lotSize: 1,
        //     tickSize: 0.1,
        //     indexPriceTickSize: 0.01,
        //     multiplier: 0.001,
        //     initialMargin: 0.008,
        //     maintainMargin: 0.004,
        //     maxRiskLimit: 100000,
        //     minRiskLimit: 100000,
        //     riskStep: 50000,
        //     makerFeeRate: 0.0002,
        //     takerFeeRate: 0.0006,
        //     takerFixFee: 0,
        //     makerFixFee: 0,
        //     settlementFee: null,
        //     isDeleverage: true,
        //     isQuanto: true,
        //     isInverse: false,
        //     markMethod: "FairPrice",
        //     fairMethod: "FundingRate",
        //     fundingBaseSymbol: ".XBTINT8H",
        //     fundingQuoteSymbol: ".USDTINT8H",
        //     fundingRateSymbol: ".XBTUSDTMFPI8H",
        //     indexSymbol: ".KXBTUSDT",
        //     settlementSymbol: "",
        //     status: "Open",
        //     fundingFeeRate: 0.000297,
        //     predictedFundingFeeRate: 0.000327,
        //     fundingRateGranularity: 28800000,
        //     openInterest: "8033200",
        //     turnoverOf24h: 659795309.2524643,
        //     volumeOf24h: 9998.54,
        //     markPrice: 67193.51,
        //     indexPrice: 67184.81,
        //     lastTradePrice: 67191.8,
        //     nextFundingRateTime: 20022985,
        //     maxLeverage: 125,
        //     premiumsSymbol1M: ".XBTUSDTMPI",
        //     premiumsSymbol8H: ".XBTUSDTMPI8H",
        //     fundingBaseSymbol1M: ".XBTINT",
        //     fundingQuoteSymbol1M: ".USDTINT",
        //     lowPrice: 64041.6,
        //     highPrice: 67737.3,
        //     priceChgPct: 0.0447,
        //     priceChg: 2878.7
        // }
        //
        object marketId = this.safeString(ticker, "symbol");
        market = this.safeMarket(marketId, market, "-");
        object last = this.safeString2(ticker, "price", "lastTradePrice");
        object timestamp = this.safeIntegerProduct(ticker, "ts", 0.000001);
        return this.safeTicker(new Dictionary<string, object>() {
            { "symbol", getValue(market, "symbol") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "high", this.safeString(ticker, "highPrice") },
            { "low", this.safeString(ticker, "lowPrice") },
            { "bid", this.safeString(ticker, "bestBidPrice") },
            { "bidVolume", this.safeString(ticker, "bestBidSize") },
            { "ask", this.safeString(ticker, "bestAskPrice") },
            { "askVolume", this.safeString(ticker, "bestAskSize") },
            { "vwap", null },
            { "open", null },
            { "close", last },
            { "last", last },
            { "previousClose", null },
            { "change", this.safeString(ticker, "priceChg") },
            { "percentage", this.safeString(ticker, "priceChgPct") },
            { "average", null },
            { "baseVolume", this.safeString(ticker, "volumeOf24h") },
            { "quoteVolume", this.safeString(ticker, "turnoverOf24h") },
            { "markPrice", this.safeString2(ticker, "markPrice", "value") },
            { "indexPrice", this.safeString(ticker, "indexPrice") },
            { "info", ticker },
        }, market);
    }

    /**
     * @method
     * @name kucoinfutures#fetchBidsAsks
     * @description fetches the bid and ask price and volume for multiple markets
     * @param {string[]} [symbols] unified symbols of the markets to fetch the bids and asks for, all markets are returned if not assigned
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchBidsAsks(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object request = new Dictionary<string, object>() {
            { "method", "futuresPublicGetAllTickers" },
        };
        return await this.fetchTickers(symbols, this.extend(request, parameters));
    }

    /**
     * @method
     * @name kucoinfutures#fetchFundingHistory
     * @description fetch the history of funding payments paid and received on this account
     * @see https://www.kucoin.com/docs/rest/futures-trading/funding-fees/get-funding-history
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch funding history for
     * @param {int} [limit] the maximum number of funding history structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [funding history structure]{@link https://docs.ccxt.com/#/?id=funding-history-structure}
     */
    public async override Task<object> fetchFundingHistory(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchFundingHistory() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startAt"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            // * Since is ignored if limit is defined
            ((IDictionary<string,object>)request)["maxCount"] = limit;
        }
        object response = await this.futuresPrivateGetFundingHistory(this.extend(request, parameters));
        //
        //    {
        //        "code": "200000",
        //        "data": {
        //            "dataList": [
        //                {
        //                    "id": 239471298749817,
        //                    "symbol": "ETHUSDTM",
        //                    "timePoint": 1638532800000,
        //                    "fundingRate": 0.000100,
        //                    "markPrice": 4612.83********,
        //                    "positionQty": 12,
        //                    "positionCost": 553.5396000000,
        //                    "funding": -0.0553539600,
        //                    "settleCurrency": "USDT"
        //                },
        //                ...
        //            ],
        //            "hasMore": true
        //        }
        //    }
        //
        object data = this.safeValue(response, "data");
        object dataList = this.safeList(data, "dataList", new List<object>() {});
        object fees = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(dataList)); postFixIncrement(ref i))
        {
            object listItem = getValue(dataList, i);
            object timestamp = this.safeInteger(listItem, "timePoint");
            ((IList<object>)fees).Add(new Dictionary<string, object>() {
                { "info", listItem },
                { "symbol", symbol },
                { "code", this.safeCurrencyCode(this.safeString(listItem, "settleCurrency")) },
                { "timestamp", timestamp },
                { "datetime", this.iso8601(timestamp) },
                { "id", this.safeNumber(listItem, "id") },
                { "amount", this.safeNumber(listItem, "funding") },
                { "fundingRate", this.safeNumber(listItem, "fundingRate") },
                { "markPrice", this.safeNumber(listItem, "markPrice") },
                { "positionQty", this.safeNumber(listItem, "positionQty") },
                { "positionCost", this.safeNumber(listItem, "positionCost") },
            });
        }
        return fees;
    }

    /**
     * @method
     * @name kucoinfutures#fetchPosition
     * @see https://docs.kucoin.com/futures/#get-position-details
     * @description fetch data on an open position
     * @param {string} symbol unified market symbol of the market the position is held in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [position structure]{@link https://docs.ccxt.com/#/?id=position-structure}
     */
    public async override Task<object> fetchPosition(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object response = await this.futuresPrivateGetPosition(this.extend(request, parameters));
        //
        //    {
        //        "code": "200000",
        //        "data": {
        //            "id": "6505ee6eaff4070001f651c4",
        //            "symbol": "XBTUSDTM",
        //            "autoDeposit": false,
        //            "maintMarginReq": 0,
        //            "riskLimit": 200,
        //            "realLeverage": 0.0,
        //            "crossMode": false,
        //            "delevPercentage": 0.0,
        //            "currentTimestamp": 1694887534594,
        //            "currentQty": 0,
        //            "currentCost": 0.0,
        //            "currentComm": 0.0,
        //            "unrealisedCost": 0.0,
        //            "realisedGrossCost": 0.0,
        //            "realisedCost": 0.0,
        //            "isOpen": false,
        //            "markPrice": 26611.71,
        //            "markValue": 0.0,
        //            "posCost": 0.0,
        //            "posCross": 0,
        //            "posInit": 0.0,
        //            "posComm": 0.0,
        //            "posLoss": 0.0,
        //            "posMargin": 0.0,
        //            "posMaint": 0.0,
        //            "maintMargin": 0.0,
        //            "realisedGrossPnl": 0.0,
        //            "realisedPnl": 0.0,
        //            "unrealisedPnl": 0.0,
        //            "unrealisedPnlPcnt": 0,
        //            "unrealisedRoePcnt": 0,
        //            "avgEntryPrice": 0.0,
        //            "liquidationPrice": 0.0,
        //            "bankruptPrice": 0.0,
        //            "settleCurrency": "USDT",
        //            "maintainMargin": 0,
        //            "riskLimitLevel": 1
        //        }
        //    }
        //
        object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        return this.parsePosition(data, market);
    }

    /**
     * @method
     * @name kucoinfutures#fetchPositions
     * @description fetch all open positions
     * @see https://docs.kucoin.com/futures/#get-position-list
     * @param {string[]|undefined} symbols list of unified market symbols
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [position structure]{@link https://docs.ccxt.com/#/?id=position-structure}
     */
    public async override Task<object> fetchPositions(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.futuresPrivateGetPositions(parameters);
        //
        //    {
        //        "code": "200000",
        //        "data": [
        //            {
        //                "id": "615ba79f83a3410001cde321",
        //                "symbol": "ETHUSDTM",
        //                "autoDeposit": false,
        //                "maintMarginReq": 0.005,
        //                "riskLimit": 1000000,
        //                "realLeverage": 18.61,
        //                "crossMode": false,
        //                "delevPercentage": 0.86,
        //                "openingTimestamp": 1638563515618,
        //                "currentTimestamp": 1638576872774,
        //                "currentQty": 2,
        //                "currentCost": 83.64200000,
        //                "currentComm": 0.********,
        //                "unrealisedCost": 83.64200000,
        //                "realisedGrossCost": 0.********,
        //                "realisedCost": 0.********,
        //                "isOpen": true,
        //                "markPrice": 4225.01,
        //                "markValue": 84.50020000,
        //                "posCost": 83.64200000,
        //                "posCross": 0.********00,
        //                "posInit": 3.63660870,
        //                "posComm": 0.05236717,
        //                "posLoss": 0.********,
        //                "posMargin": 3.68897586,
        //                "posMaint": 0.********,
        //                "maintMargin": 4.********,
        //                "realisedGrossPnl": 0.********,
        //                "realisedPnl": -0.********,
        //                "unrealisedPnl": 0.********,
        //                "unrealisedPnlPcnt": 0.0103,
        //                "unrealisedRoePcnt": 0.2360,
        //                "avgEntryPrice": 4182.10,
        //                "liquidationPrice": 4023.00,
        //                "bankruptPrice": 4000.25,
        //                "settleCurrency": "USDT",
        //                "isInverse": false
        //            }
        //        ]
        //    }
        //
        object data = this.safeList(response, "data");
        return this.parsePositions(data, symbols);
    }

    /**
     * @method
     * @name kucoinfutures#fetchPositionsHistory
     * @description fetches historical positions
     * @see https://www.kucoin.com/docs/rest/futures-trading/positions/get-positions-history
     * @param {string[]} [symbols] list of unified market symbols
     * @param {int} [since] the earliest time in ms to fetch position history for
     * @param {int} [limit] the maximum number of entries to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] closing end time
     * @param {int} [params.pageId] page id
     * @returns {object[]} a list of [position structure]{@link https://docs.ccxt.com/#/?id=position-structure}
     */
    public async override Task<object> fetchPositionsHistory(object symbols = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        if (isTrue(isEqual(limit, null)))
        {
            limit = 200;
        }
        object request = new Dictionary<string, object>() {
            { "limit", limit },
        };
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["from"] = since;
        }
        object until = this.safeInteger(parameters, "until");
        if (isTrue(!isEqual(until, null)))
        {
            parameters = this.omit(parameters, "until");
            ((IDictionary<string,object>)request)["to"] = until;
        }
        object response = await this.futuresPrivateGetHistoryPositions(this.extend(request, parameters));
        //
        // {
        //     "success": true,
        //     "code": "200",
        //     "msg": "success",
        //     "retry": false,
        //     "data": {
        //         "currentPage": 1,
        //         "pageSize": 10,
        //         "totalNum": 25,
        //         "totalPage": 3,
        //         "items": [
        //             {
        //                 "closeId": "3********000000030",
        //                 "positionId": "3****************9",
        //                 "uid": **************,
        //                 "userId": "6527d4fc8c7f3d0001f40f5f",
        //                 "symbol": "XBTUSDM",
        //                 "settleCurrency": "XBT",
        //                 "leverage": "0.0",
        //                 "type": "LIQUID_LONG",
        //                 "side": null,
        //                 "closeSize": null,
        //                 "pnl": "-1.****************",
        //                 "realisedGrossCost": "0.****************",
        //                 "withdrawPnl": "0.0",
        //                 "roe": null,
        //                 "tradeFee": "0.0006154045",
        //                 "fundingFee": "0.0",
        //                 "openTime": 1713785751181,
        //                 "closeTime": 1713785752784,
        //                 "openPrice": null,
        //                 "closePrice": null
        //             }
        //         ]
        //     }
        // }
        //
        object data = this.safeDict(response, "data");
        object items = this.safeList(data, "items", new List<object>() {});
        return this.parsePositions(items, symbols);
    }

    public override object parsePosition(object position, object market = null)
    {
        //
        //    {
        //        "code": "200000",
        //        "data": [
        //            {
        //                "id": "615ba79f83a3410001cde321",         // Position ID
        //                "symbol": "ETHUSDTM",                     // Symbol
        //                "autoDeposit": false,                     // Auto deposit margin or not
        //                "maintMarginReq": 0.005,                  // Maintenance margin requirement
        //                "riskLimit": 1000000,                     // Risk limit
        //                "realLeverage": 25.92,                    // Leverage of the order
        //                "crossMode": false,                       // Cross mode or not
        //                "delevPercentage": 0.76,                  // ADL ranking percentile
        //                "openingTimestamp": 1638578546031,        // Open time
        //                "currentTimestamp": 1638578563580,        // Current timestamp
        //                "currentQty": 2,                          // Current postion quantity
        //                "currentCost": 83.787,                    // Current postion value
        //                "currentComm": 0.0167574,                 // Current commission
        //                "unrealisedCost": 83.787,                 // Unrealised value
        //                "realisedGrossCost": 0.0,                 // Accumulated realised gross profit value
        //                "realisedCost": 0.0167574,                // Current realised position value
        //                "isOpen": true,                           // Opened position or not
        //                "markPrice": 4183.38,                     // Mark price
        //                "markValue": 83.6676,                     // Mark value
        //                "posCost": 83.787,                        // Position value
        //                "posCross": 0.0,                          // added margin
        //                "posInit": 3.35148,                       // Leverage margin
        //                "posComm": 0.********,                    // Bankruptcy cost
        //                "posLoss": 0.0,                           // Funding fees paid out
        //                "posMargin": 3.********,                  // Position margin
        //                "posMaint": 0.********,                   // Maintenance margin
        //                "maintMargin": 3.********,                // Position margin
        //                "realisedGrossPnl": 0.0,                  // Accumulated realised gross profit value
        //                "realisedPnl": -0.0167574,                // Realised profit and loss
        //                "unrealisedPnl": -0.1194,                 // Unrealised profit and loss
        //                "unrealisedPnlPcnt": -0.0014,             // Profit-loss ratio of the position
        //                "unrealisedRoePcnt": -0.0356,             // Rate of return on investment
        //                "avgEntryPrice": 4189.35,                 // Average entry price
        //                "liquidationPrice": 4044.55,              // Liquidation price
        //                "bankruptPrice": 4021.75,                 // Bankruptcy price
        //                "settleCurrency": "USDT",                 // Currency used to clear and settle the trades
        //                "isInverse": false
        //            }
        //        ]
        //    }
        // position history
        //             {
        //                 "closeId": "3********000000030",
        //                 "positionId": "3****************9",
        //                 "uid": **************,
        //                 "userId": "6527d4fc8c7f3d0001f40f5f",
        //                 "symbol": "XBTUSDM",
        //                 "settleCurrency": "XBT",
        //                 "leverage": "0.0",
        //                 "type": "LIQUID_LONG",
        //                 "side": null,
        //                 "closeSize": null,
        //                 "pnl": "-1.****************",
        //                 "realisedGrossCost": "0.****************",
        //                 "withdrawPnl": "0.0",
        //                 "roe": null,
        //                 "tradeFee": "0.0006154045",
        //                 "fundingFee": "0.0",
        //                 "openTime": 1713785751181,
        //                 "closeTime": 1713785752784,
        //                 "openPrice": null,
        //                 "closePrice": null
        //             }
        //
        object symbol = this.safeString(position, "symbol");
        market = this.safeMarket(symbol, market);
        object timestamp = this.safeInteger(position, "currentTimestamp");
        object size = this.safeString(position, "currentQty");
        object side = null;
        object type = this.safeStringLower(position, "type");
        if (isTrue(!isEqual(size, null)))
        {
            if (isTrue(Precise.stringGt(size, "0")))
            {
                side = "long";
            } else if (isTrue(Precise.stringLt(size, "0")))
            {
                side = "short";
            }
        } else if (isTrue(!isEqual(type, null)))
        {
            if (isTrue(isGreaterThan(getIndexOf(type, "long"), -1)))
            {
                side = "long";
            } else
            {
                side = "short";
            }
        }
        object notional = Precise.stringAbs(this.safeString(position, "posCost"));
        object initialMargin = this.safeString(position, "posInit");
        object initialMarginPercentage = Precise.stringDiv(initialMargin, notional);
        // const marginRatio = Precise.stringDiv (maintenanceRate, collateral);
        object unrealisedPnl = this.safeString(position, "unrealisedPnl");
        object crossMode = this.safeValue(position, "crossMode");
        // currently crossMode is always set to false and only isolated positions are supported
        object marginMode = null;
        if (isTrue(!isEqual(crossMode, null)))
        {
            marginMode = ((bool) isTrue(crossMode)) ? "cross" : "isolated";
        }
        return this.safePosition(new Dictionary<string, object>() {
            { "info", position },
            { "id", this.safeString2(position, "id", "positionId") },
            { "symbol", this.safeString(market, "symbol") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "lastUpdateTimestamp", this.safeInteger(position, "closeTime") },
            { "initialMargin", this.parseNumber(initialMargin) },
            { "initialMarginPercentage", this.parseNumber(initialMarginPercentage) },
            { "maintenanceMargin", this.safeNumber(position, "posMaint") },
            { "maintenanceMarginPercentage", this.safeNumber(position, "maintMarginReq") },
            { "entryPrice", this.safeNumber2(position, "avgEntryPrice", "openPrice") },
            { "notional", this.parseNumber(notional) },
            { "leverage", this.safeNumber2(position, "realLeverage", "leverage") },
            { "unrealizedPnl", this.parseNumber(unrealisedPnl) },
            { "contracts", this.parseNumber(Precise.stringAbs(size)) },
            { "contractSize", this.safeValue(market, "contractSize") },
            { "realizedPnl", this.safeNumber2(position, "realisedPnl", "pnl") },
            { "marginRatio", null },
            { "liquidationPrice", this.safeNumber(position, "liquidationPrice") },
            { "markPrice", this.safeNumber(position, "markPrice") },
            { "lastPrice", null },
            { "collateral", this.safeNumber(position, "maintMargin") },
            { "marginMode", marginMode },
            { "side", side },
            { "percentage", null },
            { "stopLossPrice", null },
            { "takeProfitPrice", null },
        });
    }

    /**
     * @method
     * @name kucoinfutures#createOrder
     * @description Create an order on the exchange
     * @see https://www.kucoin.com/docs/rest/futures-trading/orders/place-order
     * @see https://www.kucoin.com/docs/rest/futures-trading/orders/place-take-profit-and-stop-loss-order#http-request
     * @param {string} symbol Unified CCXT market symbol
     * @param {string} type 'limit' or 'market'
     * @param {string} side 'buy' or 'sell'
     * @param {float} amount the amount of currency to trade
     * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
     * @param {object} [params]  extra parameters specific to the exchange API endpoint
     * @param {object} [params.takeProfit] *takeProfit object in params* containing the triggerPrice at which the attached take profit order will be triggered and the triggerPriceType
     * @param {object} [params.stopLoss] *stopLoss object in params* containing the triggerPrice at which the attached stop loss order will be triggered and the triggerPriceType
     * @param {float} [params.triggerPrice] The price a trigger order is triggered at
     * @param {float} [params.stopLossPrice] price to trigger stop-loss orders
     * @param {float} [params.takeProfitPrice] price to trigger take-profit orders
     * @param {bool} [params.reduceOnly] A mark to reduce the position size only. Set to false by default. Need to set the position size when reduceOnly is true.
     * @param {string} [params.timeInForce] GTC, GTT, IOC, or FOK, default is GTC, limit orders only
     * @param {string} [params.postOnly] Post only flag, invalid when timeInForce is IOC or FOK
     * @param {float} [params.cost] the cost of the order in units of USDT
     * @param {string} [params.marginMode] 'cross' or 'isolated', default is 'isolated'
     * ----------------- Exchange Specific Parameters -----------------
     * @param {float} [params.leverage] Leverage size of the order (mandatory param in request, default is 1)
     * @param {string} [params.clientOid] client order id, defaults to uuid if not passed
     * @param {string} [params.remark] remark for the order, length cannot exceed 100 utf8 characters
     * @param {string} [params.stop] 'up' or 'down', the direction the triggerPrice is triggered from, requires triggerPrice. down: Triggers when the price reaches or goes below the triggerPrice. up: Triggers when the price reaches or goes above the triggerPrice.
     * @param {string} [params.triggerPriceType] "last", "mark", "index" - defaults to "mark"
     * @param {string} [params.stopPriceType] exchange-specific alternative for triggerPriceType: TP, IP or MP
     * @param {bool} [params.closeOrder] set to true to close position
     * @param {bool} [params.test] set to true to use the test order endpoint (does not submit order, use to validate params)
     * @param {bool} [params.forceHold] A mark to forcely hold the funds for an order, even though it's an order to reduce the position size. This helps the order stay on the order book and not get canceled when the position size changes. Set to false by default.
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createOrder(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object testOrder = this.safeBool(parameters, "test", false);
        parameters = this.omit(parameters, "test");
        object isTpAndSlOrder = isTrue((!isEqual(this.safeValue(parameters, "stopLoss"), null))) || isTrue((!isEqual(this.safeValue(parameters, "takeProfit"), null)));
        object orderRequest = this.createContractOrderRequest(symbol, type, side, amount, price, parameters);
        object response = null;
        if (isTrue(testOrder))
        {
            response = await this.futuresPrivatePostOrdersTest(orderRequest);
        } else
        {
            if (isTrue(isTpAndSlOrder))
            {
                response = await this.futuresPrivatePostStOrders(orderRequest);
            } else
            {
                response = await this.futuresPrivatePostOrders(orderRequest);
            }
        }
        //
        //    {
        //        "code": "200000",
        //        "data": {
        //            "orderId": "619717484f1d010001510cde",
        //        },
        //    }
        //
        object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        return this.parseOrder(data, market);
    }

    /**
     * @method
     * @name kucoinfutures#createOrders
     * @description create a list of trade orders
     * @see https://www.kucoin.com/docs/rest/futures-trading/orders/place-multiple-orders
     * @param {Array} orders list of orders to create, each object should contain the parameters required by createOrder, namely symbol, type, side, amount, price and params
     * @param {object} [params]  extra parameters specific to the exchange API endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createOrders(object orders, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object ordersRequests = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(orders)); postFixIncrement(ref i))
        {
            object rawOrder = getValue(orders, i);
            object symbol = this.safeString(rawOrder, "symbol");
            object market = this.market(symbol);
            object type = this.safeString(rawOrder, "type");
            object side = this.safeString(rawOrder, "side");
            object amount = this.safeValue(rawOrder, "amount");
            object price = this.safeValue(rawOrder, "price");
            object orderParams = this.safeValue(rawOrder, "params", new Dictionary<string, object>() {});
            object orderRequest = this.createContractOrderRequest(getValue(market, "id"), type, side, amount, price, orderParams);
            ((IList<object>)ordersRequests).Add(orderRequest);
        }
        object response = await this.futuresPrivatePostOrdersMulti(ordersRequests);
        //
        //     {
        //         "code": "200000",
        //         "data": [
        //             {
        //                 "orderId": "135241412609331200",
        //                 "clientOid": "3d8fcc13-0b13-447f-ad30-4b3441e05213",
        //                 "symbol": "LTCUSDTM",
        //                 "code": "200000",
        //                 "msg": "success"
        //             },
        //             {
        //                 "orderId": "135241412747743234",
        //                 "clientOid": "b878c7ee-ae3e-4d63-a20b-038acbb7306f",
        //                 "symbol": "LTCUSDTM",
        //                 "code": "200000",
        //                 "msg": "success"
        //             }
        //         ]
        //     }
        //
        object data = this.safeList(response, "data", new List<object>() {});
        return this.parseOrders(data);
    }

    public virtual object createContractOrderRequest(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object market = this.market(symbol);
        // required param, cannot be used twice
        object clientOrderId = this.safeString2(parameters, "clientOid", "clientOrderId", this.uuid());
        parameters = this.omit(parameters, new List<object>() {"clientOid", "clientOrderId"});
        object request = new Dictionary<string, object>() {
            { "clientOid", clientOrderId },
            { "side", side },
            { "symbol", getValue(market, "id") },
            { "type", type },
            { "leverage", 1 },
        };
        object marginModeUpper = this.safeStringUpper(parameters, "marginMode");
        if (isTrue(!isEqual(marginModeUpper, null)))
        {
            parameters = this.omit(parameters, "marginMode");
            ((IDictionary<string,object>)request)["marginMode"] = marginModeUpper;
        }
        object cost = this.safeString(parameters, "cost");
        parameters = this.omit(parameters, "cost");
        if (isTrue(!isEqual(cost, null)))
        {
            ((IDictionary<string,object>)request)["valueQty"] = this.costToPrecision(symbol, cost);
        } else
        {
            if (isTrue(isLessThan(amount, 1)))
            {
                throw new InvalidOrder ((string)add(this.id, " createOrder() minimum contract order amount is 1")) ;
            }
            ((IDictionary<string,object>)request)["size"] = parseInt(this.amountToPrecision(symbol, amount));
        }
        var triggerPricestopLossPricetakeProfitPriceVariable = this.handleTriggerPrices(parameters);
        var triggerPrice = ((IList<object>) triggerPricestopLossPricetakeProfitPriceVariable)[0];
        var stopLossPrice = ((IList<object>) triggerPricestopLossPricetakeProfitPriceVariable)[1];
        var takeProfitPrice = ((IList<object>) triggerPricestopLossPricetakeProfitPriceVariable)[2];
        object stopLoss = this.safeDict(parameters, "stopLoss");
        object takeProfit = this.safeDict(parameters, "takeProfit");
        // const isTpAndSl = stopLossPrice && takeProfitPrice;
        object triggerPriceTypes = new Dictionary<string, object>() {
            { "mark", "MP" },
            { "last", "TP" },
            { "index", "IP" },
        };
        object triggerPriceType = this.safeString(parameters, "triggerPriceType", "mark");
        object triggerPriceTypeValue = this.safeString(triggerPriceTypes, triggerPriceType, triggerPriceType);
        parameters = this.omit(parameters, new List<object>() {"stopLossPrice", "takeProfitPrice", "triggerPrice", "stopPrice", "takeProfit", "stopLoss"});
        if (isTrue(triggerPrice))
        {
            ((IDictionary<string,object>)request)["stop"] = ((bool) isTrue((isEqual(side, "buy")))) ? "up" : "down";
            ((IDictionary<string,object>)request)["stopPrice"] = this.priceToPrecision(symbol, triggerPrice);
            ((IDictionary<string,object>)request)["stopPriceType"] = triggerPriceTypeValue;
        } else if (isTrue(isTrue(!isEqual(stopLoss, null)) || isTrue(!isEqual(takeProfit, null))))
        {
            object priceType = triggerPriceTypeValue;
            if (isTrue(!isEqual(stopLoss, null)))
            {
                object slPrice = this.safeString2(stopLoss, "triggerPrice", "stopPrice");
                ((IDictionary<string,object>)request)["triggerStopDownPrice"] = this.priceToPrecision(symbol, slPrice);
                priceType = this.safeString(stopLoss, "triggerPriceType", "mark");
                priceType = this.safeString(triggerPriceTypes, priceType, priceType);
            }
            if (isTrue(!isEqual(takeProfit, null)))
            {
                object tpPrice = this.safeString2(takeProfit, "triggerPrice", "takeProfitPrice");
                ((IDictionary<string,object>)request)["triggerStopUpPrice"] = this.priceToPrecision(symbol, tpPrice);
                priceType = this.safeString(takeProfit, "triggerPriceType", "mark");
                priceType = this.safeString(triggerPriceTypes, priceType, priceType);
            }
            ((IDictionary<string,object>)request)["stopPriceType"] = priceType;
        } else if (isTrue(isTrue(stopLossPrice) || isTrue(takeProfitPrice)))
        {
            if (isTrue(stopLossPrice))
            {
                ((IDictionary<string,object>)request)["stop"] = ((bool) isTrue((isEqual(side, "buy")))) ? "up" : "down";
                ((IDictionary<string,object>)request)["stopPrice"] = this.priceToPrecision(symbol, stopLossPrice);
            } else
            {
                ((IDictionary<string,object>)request)["stop"] = ((bool) isTrue((isEqual(side, "buy")))) ? "down" : "up";
                ((IDictionary<string,object>)request)["stopPrice"] = this.priceToPrecision(symbol, takeProfitPrice);
            }
            ((IDictionary<string,object>)request)["reduceOnly"] = true;
            ((IDictionary<string,object>)request)["stopPriceType"] = triggerPriceTypeValue;
        }
        object uppercaseType = ((string)type).ToUpper();
        object timeInForce = this.safeStringUpper(parameters, "timeInForce");
        if (isTrue(isEqual(uppercaseType, "LIMIT")))
        {
            if (isTrue(isEqual(price, null)))
            {
                throw new ArgumentsRequired ((string)add(this.id, " createOrder() requires a price argument for limit orders")) ;
            } else
            {
                ((IDictionary<string,object>)request)["price"] = this.priceToPrecision(symbol, price);
            }
            if (isTrue(!isEqual(timeInForce, null)))
            {
                ((IDictionary<string,object>)request)["timeInForce"] = timeInForce;
            }
        }
        object postOnly = null;
        var postOnlyparametersVariable = this.handlePostOnly(isEqual(type, "market"), false, parameters);
        postOnly = ((IList<object>)postOnlyparametersVariable)[0];
        parameters = ((IList<object>)postOnlyparametersVariable)[1];
        if (isTrue(postOnly))
        {
            ((IDictionary<string,object>)request)["postOnly"] = true;
        }
        object hidden = this.safeValue(parameters, "hidden");
        if (isTrue(isTrue(postOnly) && isTrue((!isEqual(hidden, null)))))
        {
            throw new BadRequest ((string)add(this.id, " createOrder() does not support the postOnly parameter together with a hidden parameter")) ;
        }
        object iceberg = this.safeValue(parameters, "iceberg");
        if (isTrue(iceberg))
        {
            object visibleSize = this.safeValue(parameters, "visibleSize");
            if (isTrue(isEqual(visibleSize, null)))
            {
                throw new ArgumentsRequired ((string)add(this.id, " createOrder() requires a visibleSize parameter for iceberg orders")) ;
            }
        }
        parameters = this.omit(parameters, new List<object>() {"timeInForce", "stopPrice", "triggerPrice", "stopLossPrice", "takeProfitPrice"}); // Time in force only valid for limit orders, exchange error when gtc for market orders
        return this.extend(request, parameters);
    }

    /**
     * @method
     * @name kucoinfutures#cancelOrder
     * @description cancels an open order
     * @see https://www.kucoin.com/docs/rest/futures-trading/orders/cancel-futures-order-by-orderid
     * @param {string} id order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.clientOrderId] cancel order by client order id
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object clientOrderId = this.safeString2(parameters, "clientOid", "clientOrderId");
        parameters = this.omit(parameters, new List<object>() {"clientOrderId"});
        object request = new Dictionary<string, object>() {};
        object response = null;
        if (isTrue(!isEqual(clientOrderId, null)))
        {
            if (isTrue(isEqual(symbol, null)))
            {
                throw new ArgumentsRequired ((string)add(this.id, " cancelOrder() requires a symbol argument when cancelling by clientOrderId")) ;
            }
            object market = this.market(symbol);
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
            ((IDictionary<string,object>)request)["clientOid"] = clientOrderId;
            response = await this.futuresPrivateDeleteOrdersClientOrderClientOid(this.extend(request, parameters));
        } else
        {
            ((IDictionary<string,object>)request)["orderId"] = id;
            response = await this.futuresPrivateDeleteOrdersOrderId(this.extend(request, parameters));
        }
        //
        //   {
        //       "code": "200000",
        //       "data": {
        //           "cancelledOrderIds": [
        //                "619714b8b6353000014c505a",
        //           ],
        //       },
        //   }
        //
        return this.safeOrder(new Dictionary<string, object>() {
            { "info", response },
        });
    }

    /**
     * @method
     * @name kucoinfutures#cancelOrders
     * @description cancel multiple orders
     * @see https://www.kucoin.com/docs/rest/futures-trading/orders/batch-cancel-orders
     * @param {string[]} ids order ids
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string[]} [params.clientOrderIds] client order ids
     * @returns {object} an list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async virtual Task<object> cancelOrders(object ids, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
        }
        object ordersRequests = new List<object>() {};
        object clientOrderIds = this.safeList2(parameters, "clientOrderIds", "clientOids", new List<object>() {});
        parameters = this.omit(parameters, new List<object>() {"clientOrderIds", "clientOids"});
        object useClientorderId = false;
        for (object i = 0; isLessThan(i, getArrayLength(clientOrderIds)); postFixIncrement(ref i))
        {
            useClientorderId = true;
            if (isTrue(isEqual(symbol, null)))
            {
                throw new ArgumentsRequired ((string)add(this.id, " cancelOrders() requires a symbol argument when cancelling by clientOrderIds")) ;
            }
            ((IList<object>)ordersRequests).Add(new Dictionary<string, object>() {
                { "symbol", getValue(market, "id") },
                { "clientOid", this.safeString(clientOrderIds, i) },
            });
        }
        for (object i = 0; isLessThan(i, getArrayLength(ids)); postFixIncrement(ref i))
        {
            ((IList<object>)ordersRequests).Add(getValue(ids, i));
        }
        object requestKey = ((bool) isTrue(useClientorderId)) ? "clientOidsList" : "orderIdsList";
        object request = new Dictionary<string, object>() {};
        ((IDictionary<string,object>)request)[(string)requestKey] = ordersRequests;
        object response = await this.futuresPrivateDeleteOrdersMultiCancel(this.extend(request, parameters));
        //
        //   {
        //       "code": "200000",
        //       "data":
        //       [
        //           {
        //               "orderId": "80465574458560512",
        //               "clientOid": null,
        //               "code": "200",
        //               "msg": "success"
        //           },
        //           {
        //               "orderId": "80465575289094144",
        //               "clientOid": null,
        //               "code": "200",
        //               "msg": "success"
        //           }
        //       ]
        //   }
        //
        object orders = this.safeList(response, "data", new List<object>() {});
        return this.parseOrders(orders, market);
    }

    /**
     * @method
     * @name kucoinfutures#cancelAllOrders
     * @description cancel all open orders
     * @see https://www.kucoin.com/docs/rest/futures-trading/orders/cancel-multiple-futures-limit-orders
     * @see https://www.kucoin.com/docs/rest/futures-trading/orders/cancel-multiple-futures-stop-orders
     * @param {string} symbol unified market symbol, only orders in the market of this symbol are cancelled when symbol is not undefined
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {object} [params.trigger] When true, all the trigger orders will be cancelled
     * @returns Response from the exchange
     */
    public async override Task<object> cancelAllOrders(object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(symbol, null)))
        {
            ((IDictionary<string,object>)request)["symbol"] = this.marketId(symbol);
        }
        object trigger = this.safeValue2(parameters, "stop", "trigger");
        parameters = this.omit(parameters, new List<object>() {"stop", "trigger"});
        object response = null;
        if (isTrue(trigger))
        {
            response = await this.futuresPrivateDeleteStopOrders(this.extend(request, parameters));
        } else
        {
            response = await this.futuresPrivateDeleteOrders(this.extend(request, parameters));
        }
        //
        //   {
        //       "code": "200000",
        //       "data": {
        //           "cancelledOrderIds": [
        //                "619714b8b6353000014c505a",
        //           ],
        //       },
        //   }
        //
        object data = this.safeDict(response, "data");
        return new List<object> {this.safeOrder(new Dictionary<string, object>() {
    { "info", data },
})};
    }

    /**
     * @method
     * @name kucoinfutures#addMargin
     * @description add margin
     * @see https://www.kucoin.com/docs/rest/futures-trading/positions/add-margin-manually
     * @param {string} symbol unified market symbol
     * @param {float} amount amount of margin to add
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [margin structure]{@link https://docs.ccxt.com/#/?id=add-margin-structure}
     */
    public async override Task<object> addMargin(object symbol, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object uuid = this.uuid();
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
            { "margin", this.amountToPrecision(symbol, amount) },
            { "bizNo", uuid },
        };
        object response = await this.futuresPrivatePostPositionMarginDepositMargin(this.extend(request, parameters));
        //
        //    {
        //        "code": "200000",
        //        "data": {
        //            "id": "62311d26064e8f00013f2c6d",
        //            "symbol": "XRPUSDTM",
        //            "autoDeposit": false,
        //            "maintMarginReq": 0.01,
        //            "riskLimit": 200000,
        //            "realLeverage": 0.88,
        //            "crossMode": false,
        //            "delevPercentage": 0.4,
        //            "openingTimestamp": 1647385894798,
        //            "currentTimestamp": 1647414510672,
        //            "currentQty": -1,
        //            "currentCost": -7.658,
        //            "currentComm": 0.0053561,
        //            "unrealisedCost": -7.658,
        //            "realisedGrossCost": 0,
        //            "realisedCost": 0.0053561,
        //            "isOpen": true,
        //            "markPrice": 0.7635,
        //            "markValue": -7.635,
        //            "posCost": -7.658,
        //            "posCross": 1.00016084,
        //            "posInit": 7.658,
        //            "posComm": 0.********,
        //            "posLoss": 0,
        //            "posMargin": 8.6679509,
        //            "posMaint": 0.********,
        //            "maintMargin": 8.6909509,
        //            "realisedGrossPnl": 0,
        //            "realisedPnl": -0.0038335,
        //            "unrealisedPnl": 0.023,
        //            "unrealisedPnlPcnt": 0.003,
        //            "unrealisedRoePcnt": 0.003,
        //            "avgEntryPrice": 0.7658,
        //            "liquidationPrice": 1.6239,
        //            "bankruptPrice": 1.6317,
        //            "settleCurrency": "USDT"
        //        }
        //    }
        //
        //
        //    {
        //        "code":"200000",
        //        "msg":"Position does not exist"
        //    }
        //
        object data = this.safeValue(response, "data");
        return this.extend(this.parseMarginModification(data, market), new Dictionary<string, object>() {
            { "amount", this.amountToPrecision(symbol, amount) },
            { "direction", "in" },
        });
    }

    public override object parseMarginModification(object info, object market = null)
    {
        //
        //    {
        //        "id": "62311d26064e8f00013f2c6d",
        //        "symbol": "XRPUSDTM",
        //        "autoDeposit": false,
        //        "maintMarginReq": 0.01,
        //        "riskLimit": 200000,
        //        "realLeverage": 0.88,
        //        "crossMode": false,
        //        "delevPercentage": 0.4,
        //        "openingTimestamp": 1647385894798,
        //        "currentTimestamp": 1647414510672,
        //        "currentQty": -1,
        //        "currentCost": -7.658,
        //        "currentComm": 0.0053561,
        //        "unrealisedCost": -7.658,
        //        "realisedGrossCost": 0,
        //        "realisedCost": 0.0053561,
        //        "isOpen": true,
        //        "markPrice": 0.7635,
        //        "markValue": -7.635,
        //        "posCost": -7.658,
        //        "posCross": 1.00016084,
        //        "posInit": 7.658,
        //        "posComm": 0.********,
        //        "posLoss": 0,
        //        "posMargin": 8.6679509,
        //        "posMaint": 0.********,
        //        "maintMargin": 8.6909509,
        //        "realisedGrossPnl": 0,
        //        "realisedPnl": -0.0038335,
        //        "unrealisedPnl": 0.023,
        //        "unrealisedPnlPcnt": 0.003,
        //        "unrealisedRoePcnt": 0.003,
        //        "avgEntryPrice": 0.7658,
        //        "liquidationPrice": 1.6239,
        //        "bankruptPrice": 1.6317,
        //        "settleCurrency": "USDT"
        //    }
        //
        //    {
        //        "code":"200000",
        //        "msg":"Position does not exist"
        //    }
        //
        object id = this.safeString(info, "id");
        market = this.safeMarket(id, market);
        object currencyId = this.safeString(info, "settleCurrency");
        object crossMode = this.safeValue(info, "crossMode");
        object mode = ((bool) isTrue(crossMode)) ? "cross" : "isolated";
        object marketId = this.safeString(market, "symbol");
        object timestamp = this.safeInteger(info, "currentTimestamp");
        return new Dictionary<string, object>() {
            { "info", info },
            { "symbol", this.safeSymbol(marketId, market) },
            { "type", null },
            { "marginMode", mode },
            { "amount", null },
            { "total", null },
            { "code", this.safeCurrencyCode(currencyId) },
            { "status", null },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
        };
    }

    /**
     * @method
     * @name kucoinfutures#fetchOrdersByStatus
     * @description fetches a list of orders placed on the exchange
     * @see https://docs.kucoin.com/futures/#get-order-list
     * @see https://docs.kucoin.com/futures/#get-untriggered-stop-order-list
     * @param {string} status 'active' or 'closed', only 'active' is valid for stop orders
     * @param {string} symbol unified symbol for the market to retrieve orders from
     * @param {int} [since] timestamp in ms of the earliest order to retrieve
     * @param {int} [limit] The maximum number of orders to retrieve
     * @param {object} [params] exchange specific parameters
     * @param {bool} [params.trigger] set to true to retrieve untriggered stop orders
     * @param {int} [params.until] End time in ms
     * @param {string} [params.side] buy or sell
     * @param {string} [params.type] limit or market
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns An [array of order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOrdersByStatus(object status, object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchOrdersByStatus", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDynamic("fetchOrdersByStatus", symbol, since, limit, parameters);
        }
        object trigger = this.safeBool2(parameters, "stop", "trigger");
        object until = this.safeInteger(parameters, "until");
        parameters = this.omit(parameters, new List<object>() {"stop", "until", "trigger"});
        if (isTrue(isEqual(status, "closed")))
        {
            status = "done";
        } else if (isTrue(isEqual(status, "open")))
        {
            status = "active";
        }
        object request = new Dictionary<string, object>() {};
        if (!isTrue(trigger))
        {
            ((IDictionary<string,object>)request)["status"] = status;
        } else if (isTrue(!isEqual(status, "active")))
        {
            throw new BadRequest ((string)add(this.id, " fetchOrdersByStatus() can only fetch untriggered stop orders")) ;
        }
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startAt"] = since;
        }
        if (isTrue(!isEqual(until, null)))
        {
            ((IDictionary<string,object>)request)["endAt"] = until;
        }
        object response = null;
        if (isTrue(trigger))
        {
            response = await this.futuresPrivateGetStopOrders(this.extend(request, parameters));
        } else
        {
            response = await this.futuresPrivateGetOrders(this.extend(request, parameters));
        }
        //
        //     {
        //         "code": "200000",
        //         "data": {
        //             "currentPage": 1,
        //             "pageSize": 50,
        //             "totalNum": 4,
        //             "totalPage": 1,
        //             "items": [
        //                 {
        //                     "id": "64507d02921f1c0001ff6892",
        //                     "symbol": "XBTUSDTM",
        //                     "type": "market",
        //                     "side": "buy",
        //                     "price": null,
        //                     "size": 1,
        //                     "value": "27.992",
        //                     "dealValue": "27.992",
        //                     "dealSize": 1,
        //                     "stp": "",
        //                     "stop": "",
        //                     "stopPriceType": "",
        //                     "stopTriggered": false,
        //                     "stopPrice": null,
        //                     "timeInForce": "GTC",
        //                     "postOnly": false,
        //                     "hidden": false,
        //                     "iceberg": false,
        //                     "leverage": "17",
        //                     "forceHold": false,
        //                     "closeOrder": false,
        //                     "visibleSize": null,
        //                     "clientOid": null,
        //                     "remark": null,
        //                     "tags": null,
        //                     "isActive": false,
        //                     "cancelExist": false,
        //                     "createdAt": 1682996482000,
        //                     "updatedAt": 1682996483062,
        //                     "endAt": 1682996483062,
        //                     "orderTime": 1682996482953900677,
        //                     "settleCurrency": "USDT",
        //                     "status": "done",
        //                     "filledValue": "27.992",
        //                     "filledSize": 1,
        //                     "reduceOnly": false
        //                 }
        //             ]
        //         }
        //     }
        //
        object responseData = this.safeDict(response, "data", new Dictionary<string, object>() {});
        object orders = this.safeList(responseData, "items", new List<object>() {});
        return this.parseOrders(orders, market, since, limit);
    }

    /**
     * @method
     * @name kucoinfutures#fetchClosedOrders
     * @description fetches information on multiple closed orders made by the user
     * @see https://docs.kucoin.com/futures/#get-order-list
     * @param {string} symbol unified market symbol of the market orders were made in
     * @param {int} [since] the earliest time in ms to fetch orders for
     * @param {int} [limit] the maximum number of order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] end time in ms
     * @param {string} [params.side] buy or sell
     * @param {string} [params.type] limit, or market
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchClosedOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchClosedOrders", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDynamic("fetchClosedOrders", symbol, since, limit, parameters);
        }
        return await this.fetchOrdersByStatus("done", symbol, since, limit, parameters);
    }

    /**
     * @method
     * @name kucoinfutures#fetchOpenOrders
     * @description fetches information on multiple open orders made by the user
     * @see https://docs.kucoin.com/futures/#get-order-list
     * @see https://docs.kucoin.com/futures/#get-untriggered-stop-order-list
     * @param {string} symbol unified market symbol of the market orders were made in
     * @param {int} [since] the earliest time in ms to fetch orders for
     * @param {int} [limit] the maximum number of order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] end time in ms
     * @param {string} [params.side] buy or sell
     * @param {string} [params.type] limit, or market
     * @param {boolean} [params.trigger] set to true to retrieve untriggered stop orders
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOpenOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchOpenOrders", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDynamic("fetchOpenOrders", symbol, since, limit, parameters);
        }
        return await this.fetchOrdersByStatus("open", symbol, since, limit, parameters);
    }

    /**
     * @method
     * @name kucoinfutures#fetchOrder
     * @description fetches information on an order made by the user
     * @see https://docs.kucoin.com/futures/#get-details-of-a-single-order
     * @param {string} id order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        object response = null;
        if (isTrue(isEqual(id, null)))
        {
            object clientOrderId = this.safeString2(parameters, "clientOid", "clientOrderId");
            if (isTrue(isEqual(clientOrderId, null)))
            {
                throw new InvalidOrder ((string)add(this.id, " fetchOrder() requires parameter id or params.clientOid")) ;
            }
            ((IDictionary<string,object>)request)["clientOid"] = clientOrderId;
            parameters = this.omit(parameters, new List<object>() {"clientOid", "clientOrderId"});
            response = await this.futuresPrivateGetOrdersByClientOid(this.extend(request, parameters));
        } else
        {
            ((IDictionary<string,object>)request)["orderId"] = id;
            response = await this.futuresPrivateGetOrdersOrderId(this.extend(request, parameters));
        }
        //
        //     {
        //         "code": "200000",
        //         "data": {
        //             "id": "64507d02921f1c0001ff6892",
        //             "symbol": "XBTUSDTM",
        //             "type": "market",
        //             "side": "buy",
        //             "price": null,
        //             "size": 1,
        //             "value": "27.992",
        //             "dealValue": "27.992",
        //             "dealSize": 1,
        //             "stp": "",
        //             "stop": "",
        //             "stopPriceType": "",
        //             "stopTriggered": false,
        //             "stopPrice": null,
        //             "timeInForce": "GTC",
        //             "postOnly": false,
        //             "hidden": false,
        //             "iceberg": false,
        //             "leverage": "17",
        //             "forceHold": false,
        //             "closeOrder": false,
        //             "visibleSize": null,
        //             "clientOid": null,
        //             "remark": null,
        //             "tags": null,
        //             "isActive": false,
        //             "cancelExist": false,
        //             "createdAt": 1682996482000,
        //             "updatedAt": 1682996483000,
        //             "endAt": 1682996483000,
        //             "orderTime": 1682996482953900677,
        //             "settleCurrency": "USDT",
        //             "status": "done",
        //             "filledSize": 1,
        //             "filledValue": "27.992",
        //             "reduceOnly": false
        //         }
        //     }
        //
        object market = ((bool) isTrue((!isEqual(symbol, null)))) ? this.market(symbol) : null;
        object responseData = this.safeDict(response, "data");
        return this.parseOrder(responseData, market);
    }

    public override object parseOrder(object order, object market = null)
    {
        //
        // fetchOrder, fetchOrdersByStatus
        //
        //     {
        //         "id": "64507d02921f1c0001ff6892",
        //         "symbol": "XBTUSDTM",
        //         "type": "market",
        //         "side": "buy",
        //         "price": null,
        //         "size": 1,
        //         "value": "27.992",
        //         "dealValue": "27.992",
        //         "dealSize": 1,
        //         "stp": "",
        //         "stop": "",
        //         "stopPriceType": "",
        //         "stopTriggered": false,
        //         "stopPrice": null,
        //         "timeInForce": "GTC",
        //         "postOnly": false,
        //         "hidden": false,
        //         "iceberg": false,
        //         "leverage": "17",
        //         "forceHold": false,
        //         "closeOrder": false,
        //         "visibleSize": null,
        //         "clientOid": null,
        //         "remark": null,
        //         "tags": null,
        //         "isActive": false,
        //         "cancelExist": false,
        //         "createdAt": 1682996482000,
        //         "updatedAt": 1682996483062,
        //         "endAt": 1682996483062,
        //         "orderTime": 1682996482953900677,
        //         "settleCurrency": "USDT",
        //         "status": "done",
        //         "filledValue": "27.992",
        //         "filledSize": 1,
        //         "reduceOnly": false
        //     }
        //
        // createOrder
        //
        //     {
        //         "orderId": "619717484f1d010001510cde"
        //     }
        //
        // createOrders
        //
        //     {
        //         "orderId": "80465574458560512",
        //         "clientOid": "5c52e11203aa677f33e491",
        //         "symbol": "ETHUSDTM",
        //         "code": "200000",
        //         "msg": "success"
        //     }
        //
        object marketId = this.safeString(order, "symbol");
        market = this.safeMarket(marketId, market);
        object symbol = getValue(market, "symbol");
        object orderId = this.safeString2(order, "id", "orderId");
        object type = this.safeString(order, "type");
        object timestamp = this.safeInteger(order, "createdAt");
        object datetime = this.iso8601(timestamp);
        object price = this.safeString(order, "price");
        // price is zero for market order
        // omitZero is called in safeOrder2
        object side = this.safeString(order, "side");
        object feeCurrencyId = this.safeString(order, "feeCurrency");
        object feeCurrency = this.safeCurrencyCode(feeCurrencyId);
        object feeCost = this.safeNumber(order, "fee");
        object amount = this.safeString(order, "size");
        object filled = this.safeString(order, "filledSize");
        object cost = this.safeString(order, "filledValue");
        object average = this.safeString(order, "avgDealPrice");
        if (isTrue(isTrue((isEqual(average, null))) && isTrue(Precise.stringGt(filled, "0"))))
        {
            object contractSize = this.safeString(market, "contractSize");
            if (isTrue(getValue(market, "linear")))
            {
                average = Precise.stringDiv(cost, Precise.stringMul(contractSize, filled));
            } else
            {
                average = Precise.stringDiv(Precise.stringMul(contractSize, filled), cost);
            }
        }
        // precision reported by their api is 8 d.p.
        // const average = Precise.stringDiv (cost, Precise.stringMul (filled, market['contractSize']));
        // bool
        object isActive = this.safeValue(order, "isActive");
        object cancelExist = this.safeBool(order, "cancelExist", false);
        object status = null;
        if (isTrue(!isEqual(isActive, null)))
        {
            status = ((bool) isTrue(isActive)) ? "open" : "closed";
        }
        status = ((bool) isTrue(cancelExist)) ? "canceled" : status;
        object fee = null;
        if (isTrue(!isEqual(feeCost, null)))
        {
            fee = new Dictionary<string, object>() {
                { "currency", feeCurrency },
                { "cost", feeCost },
            };
        }
        object clientOrderId = this.safeString(order, "clientOid");
        object timeInForce = this.safeString(order, "timeInForce");
        object postOnly = this.safeValue(order, "postOnly");
        object reduceOnly = this.safeValue(order, "reduceOnly");
        object lastUpdateTimestamp = this.safeInteger(order, "updatedAt");
        return this.safeOrder(new Dictionary<string, object>() {
            { "id", orderId },
            { "clientOrderId", clientOrderId },
            { "symbol", symbol },
            { "type", type },
            { "timeInForce", timeInForce },
            { "postOnly", postOnly },
            { "reduceOnly", reduceOnly },
            { "side", side },
            { "amount", amount },
            { "price", price },
            { "triggerPrice", this.safeNumber(order, "stopPrice") },
            { "cost", cost },
            { "filled", filled },
            { "remaining", null },
            { "timestamp", timestamp },
            { "datetime", datetime },
            { "fee", fee },
            { "status", status },
            { "info", order },
            { "lastTradeTimestamp", null },
            { "lastUpdateTimestamp", lastUpdateTimestamp },
            { "average", average },
            { "trades", null },
        }, market);
    }

    /**
     * @method
     * @name kucoinfutures#fetchFundingRate
     * @description fetch the current funding rate
     * @see https://www.kucoin.com/docs/rest/futures-trading/funding-fees/get-current-funding-rate
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [funding rate structure]{@link https://docs.ccxt.com/#/?id=funding-rate-structure}
     */
    public async override Task<object> fetchFundingRate(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object response = await this.futuresPublicGetFundingRateSymbolCurrent(this.extend(request, parameters));
        //
        //    {
        //        "code": "200000",
        //        "data": {
        //            "symbol": ".ETHUSDTMFPI8H",
        //            "granularity": 28800000,
        //            "timePoint": 1637380800000,
        //            "value": 0.0001,
        //            "predictedValue": 0.0001,
        //        },
        //    }
        //
        object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        // the website displayes the previous funding rate as "funding rate"
        return this.parseFundingRate(data, market);
    }

    /**
     * @method
     * @name kucoinfutures#fetchFundingInterval
     * @description fetch the current funding rate interval
     * @see https://www.kucoin.com/docs/rest/futures-trading/funding-fees/get-current-funding-rate
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [funding rate structure]{@link https://docs.ccxt.com/#/?id=funding-rate-structure}
     */
    public async override Task<object> fetchFundingInterval(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.fetchFundingRate(symbol, parameters);
    }

    public override object parseFundingRate(object data, object market = null)
    {
        //
        //     {
        //         "symbol": ".ETHUSDTMFPI8H",
        //         "granularity": 28800000,
        //         "timePoint": 1637380800000,
        //         "value": 0.0001,
        //         "predictedValue": 0.0001,
        //     }
        //
        object fundingTimestamp = this.safeInteger(data, "timePoint");
        object marketId = this.safeString(data, "symbol");
        return new Dictionary<string, object>() {
            { "info", data },
            { "symbol", this.safeSymbol(marketId, market, null, "contract") },
            { "markPrice", null },
            { "indexPrice", null },
            { "interestRate", null },
            { "estimatedSettlePrice", null },
            { "timestamp", null },
            { "datetime", null },
            { "fundingRate", this.safeNumber(data, "value") },
            { "fundingTimestamp", fundingTimestamp },
            { "fundingDatetime", this.iso8601(fundingTimestamp) },
            { "nextFundingRate", this.safeNumber(data, "predictedValue") },
            { "nextFundingTimestamp", null },
            { "nextFundingDatetime", null },
            { "previousFundingRate", null },
            { "previousFundingTimestamp", null },
            { "previousFundingDatetime", null },
            { "interval", this.parseFundingInterval(this.safeString(data, "granularity")) },
        };
    }

    public virtual object parseFundingInterval(object interval)
    {
        object intervals = new Dictionary<string, object>() {
            { "3600000", "1h" },
            { "14400000", "4h" },
            { "28800000", "8h" },
            { "57600000", "16h" },
            { "86400000", "24h" },
        };
        return this.safeString(intervals, interval, interval);
    }

    public override object parseBalance(object response)
    {
        object result = new Dictionary<string, object>() {
            { "info", response },
            { "timestamp", null },
            { "datetime", null },
        };
        object data = this.safeValue(response, "data");
        object currencyId = this.safeString(data, "currency");
        object code = this.safeCurrencyCode(currencyId);
        object account = this.account();
        ((IDictionary<string,object>)account)["free"] = this.safeString(data, "availableBalance");
        ((IDictionary<string,object>)account)["total"] = this.safeString(data, "accountEquity");
        ((IDictionary<string,object>)result)[(string)code] = account;
        return this.safeBalance(result);
    }

    /**
     * @method
     * @name kucoinfutures#fetchBalance
     * @description query for balance and get the amount of funds available for trading or funds locked in orders
     * @see https://www.kucoin.com/docs/rest/funding/funding-overview/get-account-detail-futures
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {object} [params.code] the unified currency code to fetch the balance for, if not provided, the default .options['fetchBalance']['code'] will be used
     * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
     */
    public async override Task<object> fetchBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        // only fetches one balance at a time
        object defaultCode = this.safeString(this.options, "code");
        object fetchBalanceOptions = this.safeValue(this.options, "fetchBalance", new Dictionary<string, object>() {});
        defaultCode = this.safeString(fetchBalanceOptions, "code", defaultCode);
        object code = this.safeString(parameters, "code", defaultCode);
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "currency", getValue(currency, "id") },
        };
        object response = await this.futuresPrivateGetAccountOverview(this.extend(request, parameters));
        //
        //     {
        //         "code": "200000",
        //         "data": {
        //             "accountEquity": 0.00005,
        //             "unrealisedPNL": 0,
        //             "marginBalance": 0.00005,
        //             "positionMargin": 0,
        //             "orderMargin": 0,
        //             "frozenFunds": 0,
        //             "availableBalance": 0.00005,
        //             "currency": "XBT"
        //         }
        //     }
        //
        return this.parseBalance(response);
    }

    /**
     * @method
     * @name kucoinfutures#transfer
     * @description transfer currency internally between wallets on the same account
     * @see https://www.kucoin.com/docs/rest/funding/transfer/transfer-to-main-or-trade-account
     * @see https://www.kucoin.com/docs/rest/funding/transfer/transfer-to-futures-account
     * @param {string} code unified currency code
     * @param {float} amount amount to transfer
     * @param {string} fromAccount account to transfer from
     * @param {string} toAccount account to transfer to
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transfer structure]{@link https://docs.ccxt.com/#/?id=transfer-structure}
     */
    public async override Task<object> transfer(object code, object amount, object fromAccount, object toAccount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object amountToPrecision = this.currencyToPrecision(code, amount);
        object request = new Dictionary<string, object>() {
            { "currency", this.safeString(currency, "id") },
            { "amount", amountToPrecision },
        };
        object toAccountString = this.parseTransferType(toAccount);
        object response = null;
        if (isTrue(isTrue(isEqual(toAccountString, "TRADE")) || isTrue(isEqual(toAccountString, "MAIN"))))
        {
            ((IDictionary<string,object>)request)["recAccountType"] = toAccountString;
            response = await this.futuresPrivatePostTransferOut(this.extend(request, parameters));
        } else if (isTrue(isTrue(isTrue(isEqual(toAccount, "future")) || isTrue(isEqual(toAccount, "swap"))) || isTrue(isEqual(toAccount, "contract"))))
        {
            ((IDictionary<string,object>)request)["payAccountType"] = this.parseTransferType(fromAccount);
            response = await this.futuresPrivatePostTransferIn(this.extend(request, parameters));
        } else
        {
            throw new BadRequest ((string)add(this.id, " transfer() only supports transfers between future/swap, spot and funding accounts")) ;
        }
        object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        return this.extend(this.parseTransfer(data, currency), new Dictionary<string, object>() {
            { "amount", this.parseNumber(amountToPrecision) },
            { "fromAccount", fromAccount },
            { "toAccount", toAccount },
        });
    }

    public override object parseTransfer(object transfer, object currency = null)
    {
        //
        // transfer to spot or funding account
        //
        //     {
        //            "applyId": "5bffb63303aa675e8bbe18f9" // Transfer-out request ID
        //     }
        //
        // transfer to future account
        //
        //     {
        //         "applyId": "6738754373ceee00011ec3f8",
        //         "bizNo": "6738754373ceee00011ec3f7",
        //         "payAccountType": "CONTRACT",
        //         "payTag": "DEFAULT",
        //         "remark": "",
        //         "recAccountType": "MAIN",
        //         "recTag": "DEFAULT",
        //         "recRemark": "",
        //         "recSystem": "KUCOIN",
        //         "status": "PROCESSING",
        //         "currency": "USDT",
        //         "amount": "5",
        //         "fee": "0",
        //         "sn": ****************,
        //         "reason": "",
        //         "createdAt": *************,
        //         "updatedAt": *************
        //     }
        //
        object timestamp = this.safeInteger(transfer, "updatedAt");
        return new Dictionary<string, object>() {
            { "id", this.safeString(transfer, "applyId") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "currency", this.safeCurrencyCode(null, currency) },
            { "amount", this.safeNumber(transfer, "amount") },
            { "fromAccount", null },
            { "toAccount", null },
            { "status", this.safeString(transfer, "status") },
            { "info", transfer },
        };
    }

    public override object parseTransferStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "PROCESSING", "pending" },
        };
        return this.safeString(statuses, status, status);
    }

    public virtual object parseTransferType(object transferType)
    {
        object transferTypes = new Dictionary<string, object>() {
            { "spot", "TRADE" },
            { "funding", "MAIN" },
        };
        return this.safeStringUpper(transferTypes, transferType, transferType);
    }

    /**
     * @method
     * @name kucoinfutures#fetchMyTrades
     * @see https://docs.kucoin.com/futures/#get-fills
     * @description fetch all trades made by the user
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch trades for
     * @param {int} [limit] the maximum number of trades structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] End time in ms
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
     */
    public async override Task<object> fetchMyTrades(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchMyTrades", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDynamic("fetchMyTrades", symbol, since, limit, parameters);
        }
        object request = new Dictionary<string, object>() {};
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startAt"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["pageSize"] = mathMin(1000, limit);
        }
        var requestparametersVariable = this.handleUntilOption("endAt", request, parameters);
        request = ((IList<object>)requestparametersVariable)[0];
        parameters = ((IList<object>)requestparametersVariable)[1];
        object response = await this.futuresPrivateGetFills(this.extend(request, parameters));
        //
        //    {
        //        "code": "200000",
        //        "data": {
        //          "currentPage": 1,
        //          "pageSize": 1,
        //          "totalNum": 251915,
        //          "totalPage": 251915,
        //          "items": [
        //              {
        //                  "symbol": "XBTUSDM",  // Ticker symbol of the contract
        //                  "tradeId": "5ce24c1f0c19fc3c58edc47c",  // Trade ID
        //                  "orderId": "5ce24c16b210233c36ee321d",  // Order ID
        //                  "side": "sell",  // Transaction side
        //                  "liquidity": "taker",  // Liquidity- taker or maker
        //                  "price": "8302",  // Filled price
        //                  "size": 10,  // Filled amount
        //                  "value": "0.001204529",  // Order value
        //                  "feeRate": "0.0005",  // Floating fees
        //                  "fixFee": "0.00000006",  // Fixed fees
        //                  "feeCurrency": "XBT",  // Charging currency
        //                  "stop": "",  // A mark to the stop order type
        //                  "fee": "0.0000012022",  // Transaction fee
        //                  "orderType": "limit",  // Order type
        //                  "tradeType": "trade",  // Trade type (trade, liquidation, ADL or settlement)
        //                  "createdAt": 1558334496000,  // Time the order created
        //                  "settleCurrency": "XBT", // settlement currency
        //                  "tradeTime": 1558334496********0 // trade time in nanosecond
        //              }
        //            ]
        //        }
        //    }
        //
        object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        object trades = this.safeList(data, "items", new List<object>() {});
        return this.parseTrades(trades, market, since, limit);
    }

    /**
     * @method
     * @name kucoinfutures#fetchTrades
     * @description get the list of most recent trades for a particular symbol
     * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-transaction-history
     * @param {string} symbol unified symbol of the market to fetch trades for
     * @param {int} [since] timestamp in ms of the earliest trade to fetch
     * @param {int} [limit] the maximum amount of trades to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
     */
    public async override Task<object> fetchTrades(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object response = await this.futuresPublicGetTradeHistory(this.extend(request, parameters));
        //
        //      {
        //          "code": "200000",
        //          "data": [
        //              {
        //                  "sequence": 32114961,
        //                  "side": "buy",
        //                  "size": 39,
        //                  "price": "4001.65********",
        //                  "takerOrderId": "61c20742f172110001e0ebe4",
        //                  "makerOrderId": "61c2073fcfc88100010fcb5d",
        //                  "tradeId": "61c2074277a0c473e69029b8",
        //                  "ts": 1640105794099993896   // filled time
        //              }
        //          ]
        //      }
        //
        object trades = this.safeList(response, "data", new List<object>() {});
        return this.parseTrades(trades, market, since, limit);
    }

    public override object parseTrade(object trade, object market = null)
    {
        //
        // fetchTrades (public)
        //
        //     {
        //         "sequence": 32114961,
        //         "side": "buy",
        //         "size": 39,
        //         "price": "4001.65********",
        //         "takerOrderId": "61c20742f172110001e0ebe4",
        //         "makerOrderId": "61c2073fcfc88100010fcb5d",
        //         "tradeId": "61c2074277a0c473e69029b8",
        //         "ts": 1640105794099993896   // filled time
        //     }
        //
        // fetchMyTrades (private) v2
        //
        //     {
        //         "symbol":"BTC-USDT",
        //         "tradeId":"5c35c02709e4f67d5266954e",
        //         "orderId":"5c35c02703aa673ceec2a168",
        //         "counterOrderId":"5c1ab46003aa676e487fa8e3",
        //         "side":"buy",
        //         "liquidity":"taker",
        //         "forceTaker":true,
        //         "price":"0.083",
        //         "size":"0.8424304",
        //         "funds":"0.0699217232",
        //         "fee":"0",
        //         "feeRate":"0",
        //         "feeCurrency":"USDT",
        //         "stop":"",
        //         "type":"limit",
        //         "createdAt":1547026472000
        //     }
        //
        // fetchMyTrades (private) v1
        //
        //    {
        //        "symbol":"DOGEUSDTM",
        //        "tradeId":"620ec41a96bab27b5f4ced56",
        //        "orderId":"620ec41a0d1d8a0001560bd0",
        //        "side":"sell",
        //        "liquidity":"taker",
        //        "forceTaker":true,
        //        "price":"0.13969",
        //        "size":1,
        //        "value":"13.969",
        //        "feeRate":"0.0006",
        //        "fixFee":"0",
        //        "feeCurrency":"USDT",
        //        "stop":"",
        //        "tradeTime":1645134874858018058,
        //        "fee":"0.0083814",
        //        "settleCurrency":"USDT",
        //        "orderType":"market",
        //        "tradeType":"trade",
        //        "createdAt":1645134874858
        //    }
        //
        // watchTrades
        //
        //    {
        //        "makerUserId": "62286a4d720edf0001e81961",
        //        "symbol": "ADAUSDTM",
        //        "sequence": 41320766,
        //        "side": "sell",
        //        "size": 2,
        //        "price": 0.35904,
        //        "takerOrderId": "636dd9da9857ba00010cfa44",
        //        "makerOrderId": "636dd9c8df149d0001e62bc8",
        //        "takerUserId": "6180be22b6ab210001fa3371",
        //        "tradeId": "636dd9da0000d400d477eca7",
        //        "ts": 1668143578987357700
        //    }
        //
        object marketId = this.safeString(trade, "symbol");
        market = this.safeMarket(marketId, market, "-");
        object id = this.safeString2(trade, "tradeId", "id");
        object orderId = this.safeString(trade, "orderId");
        object takerOrMaker = this.safeString(trade, "liquidity");
        object timestamp = this.safeInteger(trade, "ts");
        if (isTrue(!isEqual(timestamp, null)))
        {
            timestamp = this.parseToInt(divide(timestamp, 1000000));
        } else
        {
            timestamp = this.safeInteger(trade, "createdAt");
            // if it's a historical v1 trade, the exchange returns timestamp in seconds
            if (isTrue(isTrue((inOp(trade, "dealValue"))) && isTrue((!isEqual(timestamp, null)))))
            {
                timestamp = multiply(timestamp, 1000);
            }
        }
        object priceString = this.safeString2(trade, "price", "dealPrice");
        object amountString = this.safeString2(trade, "size", "amount");
        object side = this.safeString(trade, "side");
        object fee = null;
        object feeCostString = this.safeString(trade, "fee");
        if (isTrue(!isEqual(feeCostString, null)))
        {
            object feeCurrencyId = this.safeString(trade, "feeCurrency");
            object feeCurrency = this.safeCurrencyCode(feeCurrencyId);
            if (isTrue(isEqual(feeCurrency, null)))
            {
                feeCurrency = ((bool) isTrue((isEqual(side, "sell")))) ? getValue(market, "quote") : getValue(market, "base");
            }
            fee = new Dictionary<string, object>() {
                { "cost", feeCostString },
                { "currency", feeCurrency },
                { "rate", this.safeString(trade, "feeRate") },
            };
        }
        object type = this.safeString2(trade, "type", "orderType");
        if (isTrue(isEqual(type, "match")))
        {
            type = null;
        }
        object costString = this.safeString2(trade, "funds", "value");
        if (isTrue(isEqual(costString, null)))
        {
            object contractSize = this.safeString(market, "contractSize");
            object contractCost = Precise.stringMul(priceString, amountString);
            costString = Precise.stringMul(contractCost, contractSize);
        }
        return this.safeTrade(new Dictionary<string, object>() {
            { "info", trade },
            { "id", id },
            { "order", orderId },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "symbol", getValue(market, "symbol") },
            { "type", type },
            { "takerOrMaker", takerOrMaker },
            { "side", side },
            { "price", priceString },
            { "amount", amountString },
            { "cost", costString },
            { "fee", fee },
        }, market);
    }

    /**
     * @method
     * @name kucoinfutures#fetchDeposits
     * @description fetch all deposits made to an account
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch deposits for
     * @param {int} [limit] the maximum number of deposits structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchDeposits(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        object currency = null;
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
            ((IDictionary<string,object>)request)["currency"] = getValue(currency, "id");
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["pageSize"] = limit;
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startAt"] = since;
        }
        object response = await this.futuresPrivateGetDepositList(this.extend(request, parameters));
        //
        //     {
        //         "code": "200000",
        //         "data": {
        //             "currentPage": 1,
        //             "pageSize": 5,
        //             "totalNum": 2,
        //             "totalPage": 1,
        //             "items": [
        //                 {
        //                     "address": "******************************************",
        //                     "memo": "5c247c8a03aa677cea2a251d",
        //                     "amount": 1,
        //                     "fee": 0.0001,
        //                     "currency": "KCS",
        //                     "isInner": false,
        //                     "walletTxId": "5bbb57386d99522d9f954c5a@test004",
        //                     "status": "SUCCESS",
        //                     "createdAt": 1544178843000,
        //                     "updatedAt": 1544178891000
        //                     "remark":"foobar"
        //                 },
        //                 ...
        //             ]
        //         }
        //     }
        //
        object responseData = getValue(getValue(response, "data"), "items");
        return this.parseTransactions(responseData, currency, since, limit, new Dictionary<string, object>() {
            { "type", "deposit" },
        });
    }

    /**
     * @method
     * @name kucoinfutures#fetchWithdrawals
     * @description fetch all withdrawals made from an account
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch withdrawals for
     * @param {int} [limit] the maximum number of withdrawals structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchWithdrawals(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        object currency = null;
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
            ((IDictionary<string,object>)request)["currency"] = getValue(currency, "id");
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["pageSize"] = limit;
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startAt"] = since;
        }
        object response = await this.futuresPrivateGetWithdrawalList(this.extend(request, parameters));
        //
        //     {
        //         "code": "200000",
        //         "data": {
        //             "currentPage": 1,
        //             "pageSize": 5,
        //             "totalNum": 2,
        //             "totalPage": 1,
        //             "items": [
        //                 {
        //                     "id": "5c2dc64e03aa675aa263f1ac",
        //                     "address": "******************************************",
        //                     "memo": "",
        //                     "currency": "ETH",
        //                     "amount": 1.0000000,
        //                     "fee": 0.0100000,
        //                     "walletTxId": "3e2414d82acce78d38be7fe9",
        //                     "isInner": false,
        //                     "status": "FAILURE",
        //                     "createdAt": 1546503758000,
        //                     "updatedAt": 1546504603000
        //                 },
        //                 ...
        //             ]
        //         }
        //     }
        //
        object responseData = getValue(getValue(response, "data"), "items");
        return this.parseTransactions(responseData, currency, since, limit, new Dictionary<string, object>() {
            { "type", "withdrawal" },
        });
    }

    /**
     * @method
     * @name kucoinfutures#fetchMarketLeverageTiers
     * @description retrieve information on the maximum leverage, and maintenance margin for trades of varying trade sizes for a single market
     * @see https://www.kucoin.com/docs/rest/futures-trading/risk-limit/get-futures-risk-limit-level
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [leverage tiers structure]{@link https://docs.ccxt.com/#/?id=leverage-tiers-structure}
     */
    public async override Task<object> fetchMarketLeverageTiers(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        if (!isTrue(getValue(market, "contract")))
        {
            throw new BadRequest ((string)add(this.id, " fetchMarketLeverageTiers() supports contract markets only")) ;
        }
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object response = await this.futuresPublicGetContractsRiskLimitSymbol(this.extend(request, parameters));
        //
        //    {
        //        "code": "200000",
        //        "data": [
        //            {
        //                "symbol": "ETHUSDTM",
        //                "level": 1,
        //                "maxRiskLimit": 300000,
        //                "minRiskLimit": 0,
        //                "maxLeverage": 100,
        //                "initialMargin": 0.01********,
        //                "maintainMargin": 0.0050000000
        //            },
        //            ...
        //        ]
        //    }
        //
        object data = this.safeList(response, "data", new List<object>() {});
        return this.parseMarketLeverageTiers(data, market);
    }

    public override object parseMarketLeverageTiers(object info, object market = null)
    {
        /**
         * @ignore
         * @method
         * @name kucoinfutures#parseMarketLeverageTiers
         * @param {object} info Exchange market response for 1 market
         * @param {object} market CCXT market
         */
        //
        //    {
        //        "symbol": "ETHUSDTM",
        //        "level": 1,
        //        "maxRiskLimit": 300000,
        //        "minRiskLimit": 0,
        //        "maxLeverage": 100,
        //        "initialMargin": 0.01********,
        //        "maintainMargin": 0.0050000000
        //    }
        //
        object tiers = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(info)); postFixIncrement(ref i))
        {
            object tier = getValue(info, i);
            object marketId = this.safeString(tier, "symbol");
            ((IList<object>)tiers).Add(new Dictionary<string, object>() {
                { "tier", this.safeNumber(tier, "level") },
                { "symbol", this.safeSymbol(marketId, market, null, "contract") },
                { "currency", getValue(market, "base") },
                { "minNotional", this.safeNumber(tier, "minRiskLimit") },
                { "maxNotional", this.safeNumber(tier, "maxRiskLimit") },
                { "maintenanceMarginRate", this.safeNumber(tier, "maintainMargin") },
                { "maxLeverage", this.safeNumber(tier, "maxLeverage") },
                { "info", tier },
            });
        }
        return tiers;
    }

    /**
     * @method
     * @name kucoinfutures#fetchFundingRateHistory
     * @see https://www.kucoin.com/docs/rest/futures-trading/funding-fees/get-public-funding-history#request-url
     * @description fetches historical funding rate prices
     * @param {string} symbol unified symbol of the market to fetch the funding rate history for
     * @param {int} [since] not used by kucuoinfutures
     * @param {int} [limit] the maximum amount of [funding rate structures]{@link https://docs.ccxt.com/#/?id=funding-rate-history-structure} to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] end time in ms
     * @returns {object[]} a list of [funding rate structures]{@link https://docs.ccxt.com/#/?id=funding-rate-history-structure}
     */
    public async override Task<object> fetchFundingRateHistory(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchFundingRateHistory() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
            { "from", 0 },
            { "to", this.milliseconds() },
        };
        object until = this.safeInteger(parameters, "until");
        parameters = this.omit(parameters, new List<object>() {"until"});
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["from"] = since;
            if (isTrue(isEqual(until, null)))
            {
                ((IDictionary<string,object>)request)["to"] = add(since, multiply(multiply(multiply(multiply(1000, 8), 60), 60), 100));
            }
        }
        if (isTrue(!isEqual(until, null)))
        {
            ((IDictionary<string,object>)request)["to"] = until;
            if (isTrue(isEqual(since, null)))
            {
                ((IDictionary<string,object>)request)["to"] = subtract(until, multiply(multiply(multiply(multiply(1000, 8), 60), 60), 100));
            }
        }
        object response = await this.futuresPublicGetContractFundingRates(this.extend(request, parameters));
        //
        //     {
        //         "code": "200000",
        //         "data": [
        //             {
        //                 "symbol": "IDUSDTM",
        //                 "fundingRate": 2.26E-4,
        //                 "timepoint": 1702296000000
        //             }
        //         ]
        //     }
        //
        object data = this.safeList(response, "data", new List<object>() {});
        return this.parseFundingRateHistories(data, market, since, limit);
    }

    public override object parseFundingRateHistory(object info, object market = null)
    {
        object timestamp = this.safeInteger(info, "timepoint");
        object marketId = this.safeString(info, "symbol");
        return new Dictionary<string, object>() {
            { "info", info },
            { "symbol", this.safeSymbol(marketId, market) },
            { "fundingRate", this.safeNumber(info, "fundingRate") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
        };
    }

    /**
     * @method
     * @name kucoinfutures#closePosition
     * @description closes open positions for a market
     * @see https://www.kucoin.com/docs/rest/futures-trading/orders/place-order
     * @param {string} symbol Unified CCXT market symbol
     * @param {string} side not used by kucoinfutures closePositions
     * @param {object} [params] extra parameters specific to the okx api endpoint
     * @param {string} [params.clientOrderId] client order id of the order
     * @returns {object[]} [A list of position structures]{@link https://docs.ccxt.com/#/?id=position-structure}
     */
    public async override Task<object> closePosition(object symbol, object side = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object clientOrderId = this.safeString(parameters, "clientOrderId");
        object testOrder = this.safeBool(parameters, "test", false);
        parameters = this.omit(parameters, new List<object>() {"test", "clientOrderId"});
        if (isTrue(isEqual(clientOrderId, null)))
        {
            clientOrderId = this.numberToString(this.nonce());
        }
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
            { "closeOrder", true },
            { "clientOid", clientOrderId },
            { "type", "market" },
        };
        object response = null;
        if (isTrue(testOrder))
        {
            response = await this.futuresPrivatePostOrdersTest(this.extend(request, parameters));
        } else
        {
            response = await this.futuresPrivatePostOrders(this.extend(request, parameters));
        }
        return this.parseOrder(response, market);
    }

    /**
     * @method
     * @name kucoinfutures#fetchTradingFee
     * @description fetch the trading fees for a market
     * @see https://www.kucoin.com/docs/rest/funding/trade-fee/trading-pair-actual-fee-futures
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [fee structure]{@link https://docs.ccxt.com/#/?id=fee-structure}
     */
    public async override Task<object> fetchTradingFee(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbols", getValue(market, "id") },
        };
        object response = await this.privateGetTradeFees(this.extend(request, parameters));
        //
        //  {
        //      "code": "200000",
        //      "data": {
        //        "symbol": "XBTUSDTM",
        //        "takerFeeRate": "0.0006",
        //        "makerFeeRate": "0.0002"
        //      }
        //  }
        //
        object data = this.safeList(response, "data", new List<object>() {});
        object first = this.safeDict(data, 0);
        object marketId = this.safeString(first, "symbol");
        return new Dictionary<string, object>() {
            { "info", response },
            { "symbol", this.safeSymbol(marketId, market) },
            { "maker", this.safeNumber(first, "makerFeeRate") },
            { "taker", this.safeNumber(first, "takerFeeRate") },
            { "percentage", true },
            { "tierBased", true },
        };
    }

    /**
     * @method
     * @name kucoinfutures#fetchMarginMode
     * @description fetches the margin mode of a trading pair
     * @see https://www.kucoin.com/docs/rest/futures-trading/positions/get-margin-mode
     * @param {string} symbol unified symbol of the market to fetch the margin mode for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [margin mode structure]{@link https://docs.ccxt.com/#/?id=margin-mode-structure}
     */
    public async override Task<object> fetchMarginMode(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object response = await this.futuresPrivateGetPositionGetMarginMode(this.extend(request, parameters));
        //
        //     {
        //         "code": "200000",
        //         "data": {
        //             "symbol": "XBTUSDTM",
        //             "marginMode": "ISOLATED"
        //         }
        //     }
        //
        object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        return this.parseMarginMode(data, market);
    }

    public override object parseMarginMode(object marginMode, object market = null)
    {
        object marginType = this.safeString(marginMode, "marginMode");
        marginType = ((bool) isTrue((isEqual(marginType, "ISOLATED")))) ? "isolated" : "cross";
        return new Dictionary<string, object>() {
            { "info", marginMode },
            { "symbol", getValue(market, "symbol") },
            { "marginMode", marginType },
        };
    }

    /**
     * @method
     * @name kucoinfutures#setMarginMode
     * @description set margin mode to 'cross' or 'isolated'
     * @see https://www.kucoin.com/docs/rest/futures-trading/positions/modify-margin-mode
     * @param {string} marginMode 'cross' or 'isolated'
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} response from the exchange
     */
    public async override Task<object> setMarginMode(object marginMode, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " setMarginMode() requires a symbol argument")) ;
        }
        this.checkRequiredArgument("setMarginMode", marginMode, "marginMode", new List<object>() {"cross", "isolated"});
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
            { "marginMode", ((string)marginMode).ToUpper() },
        };
        object response = await this.futuresPrivatePostPositionChangeMarginMode(this.extend(request, parameters));
        //
        //    {
        //        "code": "200000",
        //        "data": {
        //            "symbol": "XBTUSDTM",
        //            "marginMode": "ISOLATED"
        //        }
        //    }
        //
        object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        return ((object)this.parseMarginMode(data, market));
    }

    /**
     * @method
     * @name kucoinfutures#fetchLeverage
     * @description fetch the set leverage for a market
     * @see https://www.kucoin.com/docs/rest/futures-trading/positions/get-cross-margin-leverage
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [leverage structure]{@link https://docs.ccxt.com/#/?id=leverage-structure}
     */
    public async override Task<object> fetchLeverage(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object marginMode = null;
        var marginModeparametersVariable = this.handleMarginModeAndParams(symbol, parameters);
        marginMode = ((IList<object>)marginModeparametersVariable)[0];
        parameters = ((IList<object>)marginModeparametersVariable)[1];
        if (isTrue(!isEqual(marginMode, "cross")))
        {
            throw new NotSupported ((string)add(this.id, " fetchLeverage() currently supports only params[\"marginMode\"] = \"cross\"")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object response = await this.futuresPrivateGetGetCrossUserLeverage(this.extend(request, parameters));
        //
        //    {
        //        "code": "200000",
        //        "data": {
        //            "symbol": "XBTUSDTM",
        //            "leverage": "3"
        //        }
        //    }
        //
        object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        object parsed = this.parseLeverage(data, market);
        return this.extend(parsed, new Dictionary<string, object>() {
            { "marginMode", marginMode },
        });
    }

    /**
     * @method
     * @name kucoinfutures#setLeverage
     * @description set the level of leverage for a market
     * @see https://www.kucoin.com/docs/rest/futures-trading/positions/modify-cross-margin-leverage
     * @param {float} leverage the rate of leverage
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} response from the exchange
     */
    public async override Task<object> setLeverage(object leverage, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object marginMode = null;
        var marginModeparametersVariable = this.handleMarginModeAndParams(symbol, parameters);
        marginMode = ((IList<object>)marginModeparametersVariable)[0];
        parameters = ((IList<object>)marginModeparametersVariable)[1];
        if (isTrue(!isEqual(marginMode, "cross")))
        {
            throw new NotSupported ((string)add(this.id, " setLeverage() currently supports only params[\"marginMode\"] = \"cross\"")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
            { "leverage", ((object)leverage).ToString() },
        };
        object response = await this.futuresPrivatePostChangeCrossUserLeverage(this.extend(request, parameters));
        //
        //    {
        //        "code": "200000",
        //        "data": true
        //    }
        //
        return this.parseLeverage(response, market);
    }

    public override object parseLeverage(object leverage, object market = null)
    {
        object marketId = this.safeString(leverage, "symbol");
        market = this.safeMarket(marketId, market);
        object leverageNum = this.safeInteger(leverage, "leverage");
        return new Dictionary<string, object>() {
            { "info", leverage },
            { "symbol", getValue(market, "symbol") },
            { "marginMode", null },
            { "longLeverage", leverageNum },
            { "shortLeverage", leverageNum },
        };
    }
}
