// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code
// EDIT THE CORRESPONDENT .ts FILE INSTEAD

/**
 *  The Application Binary Interface (ABI) describes how method input
 *  parameters should be encoded, their results decoded, and how to
 *  decode events and errors.
 *
 *  See [About ABIs](docs-abi) for more details how they are used.
 *
 *  @_section api/abi:Application Binary Interface  [about-abi]
 *  @_navTitle: ABI
 */
import { AbiCoder } from "./abi-coder.js";
export { decodeBytes32String, encodeBytes32String } from "./bytes32.js";
export { ConstructorFragment, ErrorFragment, EventFragment, FallbackFragment, Fragment, FunctionFragment, NamedFragment, ParamType, StructFragment, } from "./fragments.js";
export { checkResultErrors, Indexed, Interface, ErrorDescription, LogDescription, TransactionDescription, Result } from "./interface.js";
export { Typed } from "./typed.js";
export default AbiCoder.defaultAbiCoder();
