// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code
// EDIT THE CORRESPONDENT .ts FILE INSTEAD

/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */
import { hmac } from '../noble-hashes/hmac.js';
import { concatBytes, randomBytes } from '../noble-hashes/utils.js';
import { weierstrass } from './abstract/weierstrass.js';
// connects noble-curves to noble-hashes
export function getHash(hash) {
    return {
        hash,
        hmac: (key, ...msgs) => hmac(hash, key, concatBytes(...msgs)),
        randomBytes,
    };
}
export function createCurve(curveDef, defHash) {
    const create = (hash) => weierstrass({ ...curveDef, ...getHash(hash) });
    return Object.freeze({ ...create(defHash), create });
}
