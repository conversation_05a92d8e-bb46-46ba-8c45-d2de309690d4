export declare enum ValidateType {
    DEPLOY = "DEPLOY",
    CALL = "CALL",
    INVOKE = "INVOKE"
}
export declare enum Uint {
    u8 = "core::integer::u8",
    u16 = "core::integer::u16",
    u32 = "core::integer::u32",
    u64 = "core::integer::u64",
    u128 = "core::integer::u128",
    u256 = "core::integer::u256",
    u512 = "core::integer::u512"
}
export declare enum Literal {
    ClassHash = "core::starknet::class_hash::ClassHash",
    ContractAddress = "core::starknet::contract_address::ContractAddress",
    Secp256k1Point = "core::starknet::secp256k1::Secp256k1Point"
}
