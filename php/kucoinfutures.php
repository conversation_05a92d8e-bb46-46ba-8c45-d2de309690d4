<?php

namespace ccxt;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

use Exception; // a common import
use ccxt\abstract\kucoinfutures as kucoin;

class kucoinfutures extends kucoin {

    public function describe(): mixed {
        return $this->deep_extend(parent::describe(), array(
            'id' => 'kucoinfutures',
            'name' => 'KuCoin Futures',
            'countries' => array( 'SC' ),
            'rateLimit' => 75,
            'version' => 'v1',
            'certified' => true,
            'pro' => true,
            'comment' => 'Platform 2.0',
            'quoteJsonNumbers' => false,
            'has' => array(
                'CORS' => null,
                'spot' => false,
                'margin' => false,
                'swap' => true,
                'future' => true,
                'option' => false,
                'addMargin' => true,
                'cancelAllOrders' => true,
                'cancelOrder' => true,
                'cancelOrders' => true,
                'closeAllPositions' => false,
                'closePosition' => true,
                'closePositions' => false,
                'createDepositAddress' => true,
                'createOrder' => true,
                'createOrders' => true,
                'createOrderWithTakeProfitAndStopLoss' => true,
                'createReduceOnlyOrder' => true,
                'createStopLimitOrder' => true,
                'createStopLossOrder' => true,
                'createStopMarketOrder' => true,
                'createStopOrder' => true,
                'createTakeProfitOrder' => true,
                'createTriggerOrder' => true,
                'fetchAccounts' => true,
                'fetchBalance' => true,
                'fetchBidsAsks' => true,
                'fetchBorrowRateHistories' => false,
                'fetchBorrowRateHistory' => false,
                'fetchClosedOrders' => true,
                'fetchCrossBorrowRate' => false,
                'fetchCrossBorrowRates' => false,
                'fetchCurrencies' => false,
                'fetchDepositAddress' => true,
                'fetchDepositAddresses' => false,
                'fetchDepositAddressesByNetwork' => false,
                'fetchDeposits' => true,
                'fetchDepositWithdrawFee' => false,
                'fetchDepositWithdrawFees' => false,
                'fetchFundingHistory' => true,
                'fetchFundingInterval' => true,
                'fetchFundingIntervals' => false,
                'fetchFundingRate' => true,
                'fetchFundingRateHistory' => true,
                'fetchIndexOHLCV' => false,
                'fetchIsolatedBorrowRate' => false,
                'fetchIsolatedBorrowRates' => false,
                'fetchL3OrderBook' => true,
                'fetchLedger' => true,
                'fetchLeverage' => true,
                'fetchLeverageTiers' => false,
                'fetchMarginAdjustmentHistory' => false,
                'fetchMarginMode' => true,
                'fetchMarketLeverageTiers' => true,
                'fetchMarkets' => true,
                'fetchMarkOHLCV' => false,
                'fetchMarkPrice' => true,
                'fetchMyTrades' => true,
                'fetchOHLCV' => true,
                'fetchOpenOrders' => true,
                'fetchOrder' => true,
                'fetchOrderBook' => true,
                'fetchPosition' => true,
                'fetchPositionHistory' => false,
                'fetchPositionMode' => false,
                'fetchPositions' => true,
                'fetchPositionsHistory' => true,
                'fetchPremiumIndexOHLCV' => false,
                'fetchStatus' => true,
                'fetchTicker' => true,
                'fetchTickers' => true,
                'fetchTime' => true,
                'fetchTrades' => true,
                'fetchTradingFee' => true,
                'fetchTransactionFee' => false,
                'fetchWithdrawals' => true,
                'setLeverage' => false,
                'setMarginMode' => true,
                'transfer' => true,
                'withdraw' => null,
            ),
            'urls' => array(
                'logo' => 'https://user-images.githubusercontent.com/1294454/147508995-9e35030a-d046-43a1-a006-6fabd981b554.jpg',
                'doc' => array(
                    'https://docs.kucoin.com/futures',
                    'https://docs.kucoin.com',
                ),
                'www' => 'https://futures.kucoin.com/',
                'referral' => 'https://futures.kucoin.com/?rcode=E5wkqe',
                'api' => array(
                    'public' => 'https://openapi-v2.kucoin.com',
                    'private' => 'https://openapi-v2.kucoin.com',
                    'futuresPrivate' => 'https://api-futures.kucoin.com',
                    'futuresPublic' => 'https://api-futures.kucoin.com',
                    'webExchange' => 'https://futures.kucoin.com/_api/web-front',
                ),
            ),
            'requiredCredentials' => array(
                'apiKey' => true,
                'secret' => true,
                'password' => true,
            ),
            'api' => array(
                'futuresPublic' => array(
                    'get' => array(
                        'contracts/active' => 1,
                        'contracts/{symbol}' => 1,
                        'contracts/risk-limit/{symbol}' => 1,
                        'ticker' => 1,
                        'allTickers' => 1,
                        'level2/snapshot' => 1.33,
                        'level2/depth{limit}' => 1,
                        'level2/message/query' => 1,
                        'level3/message/query' => 1, // deprecated，level3/snapshot is suggested
                        'level3/snapshot' => 1, // v2
                        'trade/history' => 1,
                        'interest/query' => 1,
                        'index/query' => 1,
                        'mark-price/{symbol}/current' => 1,
                        'premium/query' => 1,
                        'funding-rate/{symbol}/current' => 1,
                        'timestamp' => 1,
                        'status' => 1,
                        'kline/query' => 1,
                    ),
                    'post' => array(
                        'bullet-public' => 1,
                    ),
                ),
                'futuresPrivate' => array(
                    'get' => array(
                        'account-overview' => 1.33,
                        'transaction-history' => 4.44,
                        'deposit-address' => 1,
                        'deposit-list' => 1,
                        'withdrawals/quotas' => 1,
                        'withdrawal-list' => 1,
                        'transfer-list' => 1,
                        'orders' => 1.33,
                        'stopOrders' => 1,
                        'recentDoneOrders' => 1,
                        'orders/{orderId}' => 1, // ?clientOid={client-order-id} // get order by orderId
                        'orders/byClientOid' => 1, // ?clientOid=eresc138b21023a909e5ad59 // get order by clientOid
                        'fills' => 4.44,
                        'recentFills' => 4.44,
                        'openOrderStatistics' => 1,
                        'position' => 1,
                        'positions' => 4.44,
                        'funding-history' => 4.44,
                        'sub/api-key' => 1,
                        'trade-statistics' => 1,
                        'trade-fees' => 1,
                        'history-positions' => 1,
                        'getMaxOpenSize' => 1,
                        'getCrossUserLeverage' => 1,
                        'position/getMarginMode' => 1,
                    ),
                    'post' => array(
                        'withdrawals' => 1,
                        'transfer-out' => 1, // v2
                        'transfer-in' => 1,
                        'orders' => 1.33,
                        'st-orders' => 1.33,
                        'orders/test' => 1.33,
                        'position/margin/auto-deposit-status' => 1,
                        'position/margin/deposit-margin' => 1,
                        'position/risk-limit-level/change' => 1,
                        'bullet-private' => 1,
                        'sub/api-key' => 1,
                        'sub/api-key/update' => 1,
                        'changeCrossUserLeverage' => 1,
                        'position/changeMarginMode' => 1,
                    ),
                    'delete' => array(
                        'withdrawals/{withdrawalId}' => 1,
                        'cancel/transfer-out' => 1,
                        'orders/{orderId}' => 1,
                        'orders' => 4.44,
                        'stopOrders' => 1,
                        'sub/api-key' => 1,
                        'orders/client-order/{clientOid}' => 1,
                        'orders/multi-cancel' => 20,
                    ),
                ),
                'webExchange' => array(
                    'get' => array(
                        'contract/{symbol}/funding-rates' => 1,
                    ),
                ),
            ),
            'precisionMode' => TICK_SIZE,
            'exceptions' => array(
                'exact' => array(
                    '400' => '\\ccxt\\BadRequest', // Bad Request -- Invalid request format
                    '401' => '\\ccxt\\AuthenticationError', // Unauthorized -- Invalid API Key
                    '403' => '\\ccxt\\NotSupported', // Forbidden -- The request is forbidden
                    '404' => '\\ccxt\\NotSupported', // Not Found -- The specified resource could not be found
                    '405' => '\\ccxt\\NotSupported', // Method Not Allowed -- You tried to access the resource with an invalid method.
                    '415' => '\\ccxt\\BadRequest',  // Content-Type -- application/json
                    '429' => '\\ccxt\\RateLimitExceeded', // Too Many Requests -- Access limit breached
                    '500' => '\\ccxt\\ExchangeNotAvailable', // Internal Server Error -- We had a problem with our server. Try again later.
                    '503' => '\\ccxt\\ExchangeNotAvailable', // Service Unavailable -- We're temporarily offline for maintenance. Please try again later.
                    '100001' => '\\ccxt\\OrderNotFound',     // array("msg":"error.getOrder.orderNotExist","code":"100001")
                    '100004' => '\\ccxt\\BadRequest',       // array("code":"100004","msg":"Order is in not cancelable state")
                    '101030' => '\\ccxt\\PermissionDenied', // array("code":"101030","msg":"You haven't yet enabled the margin trading")
                    '200004' => '\\ccxt\\InsufficientFunds',
                    '230003' => '\\ccxt\\InsufficientFunds', // array("code":"230003","msg":"Balance insufficient!")
                    '260100' => '\\ccxt\\InsufficientFunds', // array("code":"260100","msg":"account.noBalance")
                    '300003' => '\\ccxt\\InsufficientFunds',
                    '300012' => '\\ccxt\\InvalidOrder',
                    '400001' => '\\ccxt\\AuthenticationError', // Any of KC-API-KEY, KC-API-SIGN, KC-API-TIMESTAMP, KC-API-PASSPHRASE is missing in your request header.
                    '400002' => '\\ccxt\\InvalidNonce', // KC-API-TIMESTAMP Invalid -- Time differs from server time by more than 5 seconds
                    '400003' => '\\ccxt\\AuthenticationError', // KC-API-KEY not exists
                    '400004' => '\\ccxt\\AuthenticationError', // KC-API-PASSPHRASE error
                    '400005' => '\\ccxt\\AuthenticationError', // Signature error -- Please check your signature
                    '400006' => '\\ccxt\\AuthenticationError', // The IP address is not in the API whitelist
                    '400007' => '\\ccxt\\AuthenticationError', // Access Denied -- Your API key does not have sufficient permissions to access the URI
                    '404000' => '\\ccxt\\NotSupported', // URL Not Found -- The requested resource could not be found
                    '400100' => '\\ccxt\\BadRequest', // Parameter Error -- You tried to access the resource with invalid parameters
                    '411100' => '\\ccxt\\AccountSuspended', // User is frozen -- Please contact us via support center
                    '500000' => '\\ccxt\\ExchangeNotAvailable', // Internal Server Error -- We had a problem with our server. Try again later.
                    '300009' => '\\ccxt\\InvalidOrder', // array("msg":"No open positions to close.","code":"300009")
                    '330008' => '\\ccxt\\InsufficientFunds', // array("msg":"Your current margin and leverage have reached the maximum open limit. Please increase your margin or raise your leverage to open larger positions.","code":"330008")
                ),
                'broad' => array(
                    'Position does not exist' => '\\ccxt\\OrderNotFound', // array( "code":"200000", "msg":"Position does not exist" )
                ),
            ),
            'fees' => array(
                'trading' => array(
                    'tierBased' => true,
                    'percentage' => true,
                    'taker' => $this->parse_number('0.0006'),
                    'maker' => $this->parse_number('0.0002'),
                    'tiers' => array(
                        'taker' => array(
                            array( $this->parse_number('0'), $this->parse_number('0.0006') ),
                            array( $this->parse_number('50'), $this->parse_number('0.0006') ),
                            array( $this->parse_number('200'), $this->parse_number('0.0006') ),
                            array( $this->parse_number('500'), $this->parse_number('0.0005') ),
                            array( $this->parse_number('1000'), $this->parse_number('0.0004') ),
                            array( $this->parse_number('2000'), $this->parse_number('0.0004') ),
                            array( $this->parse_number('4000'), $this->parse_number('0.00038') ),
                            array( $this->parse_number('8000'), $this->parse_number('0.00035') ),
                            array( $this->parse_number('15000'), $this->parse_number('0.00032') ),
                            array( $this->parse_number('25000'), $this->parse_number('0.0003') ),
                            array( $this->parse_number('40000'), $this->parse_number('0.0003') ),
                            array( $this->parse_number('60000'), $this->parse_number('0.0003') ),
                            array( $this->parse_number('80000'), $this->parse_number('0.0003') ),
                        ),
                        'maker' => array(
                            array( $this->parse_number('0'), $this->parse_number('0.02') ),
                            array( $this->parse_number('50'), $this->parse_number('0.015') ),
                            array( $this->parse_number('200'), $this->parse_number('0.01') ),
                            array( $this->parse_number('500'), $this->parse_number('0.01') ),
                            array( $this->parse_number('1000'), $this->parse_number('0.01') ),
                            array( $this->parse_number('2000'), $this->parse_number('0') ),
                            array( $this->parse_number('4000'), $this->parse_number('0') ),
                            array( $this->parse_number('8000'), $this->parse_number('0') ),
                            array( $this->parse_number('15000'), $this->parse_number('-0.003') ),
                            array( $this->parse_number('25000'), $this->parse_number('-0.006') ),
                            array( $this->parse_number('40000'), $this->parse_number('-0.009') ),
                            array( $this->parse_number('60000'), $this->parse_number('-0.012') ),
                            array( $this->parse_number('80000'), $this->parse_number('-0.015') ),
                        ),
                    ),
                ),
                'funding' => array(
                    'tierBased' => false,
                    'percentage' => false,
                    'withdraw' => array(),
                    'deposit' => array(),
                ),
            ),
            'commonCurrencies' => array(
                'HOT' => 'HOTNOW',
                'EDGE' => 'DADI', // https://github.com/ccxt/ccxt/issues/5756
                'WAX' => 'WAXP',
                'TRY' => 'Trias',
                'VAI' => 'VAIOT',
                'XBT' => 'BTC',
            ),
            'timeframes' => array(
                '1m' => 1,
                '3m' => null,
                '5m' => 5,
                '15m' => 15,
                '30m' => 30,
                '1h' => 60,
                '2h' => 120,
                '4h' => 240,
                '6h' => null,
                '8h' => 480,
                '12h' => 720,
                '1d' => 1440,
                '1w' => 10080,
            ),
            'options' => array(
                'version' => 'v1',
                'symbolSeparator' => '-',
                'defaultType' => 'swap',
                'code' => 'USDT',
                'marginModes' => array(),
                'marginTypes' => array(),
                // endpoint versions
                'versions' => array(
                    'futuresPrivate' => array(
                        'GET' => array(
                            'getMaxOpenSize' => 'v2',
                            'getCrossUserLeverage' => 'v2',
                            'position/getMarginMode' => 'v2',
                        ),
                        'POST' => array(
                            'transfer-out' => 'v2',
                            'changeCrossUserLeverage' => 'v2',
                            'position/changeMarginMode' => 'v2',
                        ),
                    ),
                    'futuresPublic' => array(
                        'GET' => array(
                            'level3/snapshot' => 'v2',
                        ),
                    ),
                ),
                'networks' => array(
                    'OMNI' => 'omni',
                    'ERC20' => 'eth',
                    'TRC20' => 'trx',
                ),
                // 'code' => 'BTC',
                // 'fetchBalance' => array(
                //    'code' => 'BTC',
                // ),
            ),
            'features' => array(
                'spot' => null,
                'forDerivs' => array(
                    'sandbox' => false,
                    'createOrder' => array(
                        'marginMode' => true,
                        'triggerPrice' => true,
                        'triggerPriceType' => array(
                            'last' => true,
                            'mark' => true,
                            'index' => true,
                        ),
                        'triggerDirection' => true,
                        'stopLossPrice' => true,
                        'takeProfitPrice' => true,
                        'attachedStopLossTakeProfit' => array(
                            'triggerPriceType' => null,
                            'price' => true,
                        ),
                        'timeInForce' => array(
                            'IOC' => true,
                            'FOK' => false,
                            'PO' => true,
                            'GTD' => false,
                        ),
                        'hedged' => false,
                        'trailing' => false,
                        'leverage' => true, // todo implement
                        'marketBuyByCost' => true,
                        'marketBuyRequiresPrice' => false,
                        'selfTradePrevention' => true, // todo implement
                        'iceberg' => true,
                    ),
                    'createOrders' => array(
                        'max' => 20,
                    ),
                    'fetchMyTrades' => array(
                        'marginMode' => true,
                        'limit' => 1000,
                        'daysBack' => null,
                        'untilDays' => 7,
                        'symbolRequired' => false,
                    ),
                    'fetchOrder' => array(
                        'marginMode' => false,
                        'trigger' => false,
                        'trailing' => false,
                        'symbolRequired' => false,
                    ),
                    'fetchOpenOrders' => array(
                        'marginMode' => false,
                        'limit' => 1000,
                        'trigger' => true,
                        'trailing' => false,
                        'symbolRequired' => false,
                    ),
                    'fetchOrders' => null,
                    'fetchClosedOrders' => array(
                        'marginMode' => false,
                        'limit' => 1000,
                        'daysBack' => null,
                        'daysBackCanceled' => null,
                        'untilDays' => null,
                        'trigger' => true,
                        'trailing' => false,
                        'symbolRequired' => false,
                    ),
                    'fetchOHLCV' => array(
                        'limit' => 500,
                    ),
                ),
                'swap' => array(
                    'linear' => array(
                        'extends' => 'forDerivs',
                    ),
                    'inverse' => array(
                        'extends' => 'forDerivs',
                    ),
                ),
                'future' => array(
                    'linear' => array(
                        'extends' => 'forDerivs',
                    ),
                    'inverse' => array(
                        'extends' => 'forDerivs',
                    ),
                ),
            ),
        ));
    }

    public function fetch_status($params = array ()) {
        /**
         * the latest known information on the availability of the exchange API
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-service-$status
         *
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} a ~@link https://docs.ccxt.com/#/?id=exchange-$status-structure $status structure~
         */
        $response = $this->futuresPublicGetStatus ($params);
        //
        //     {
        //         "code":"200000",
        //         "data":{
        //             "status" => "open", // open, close, cancelonly
        //             "msg" => "upgrade match engine" // remark for operation when $status not open
        //         }
        //     }
        //
        $data = $this->safe_dict($response, 'data', array());
        $status = $this->safe_string($data, 'status');
        return array(
            'status' => ($status === 'open') ? 'ok' : 'maintenance',
            'updated' => null,
            'eta' => null,
            'url' => null,
            'info' => $response,
        );
    }

    public function fetch_markets($params = array ()): array {
        /**
         * retrieves $data on all markets for kucoinfutures
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-symbols-list
         *
         * @param {array} [$params] extra parameters specific to the exchange api endpoint
         * @return {array[]} an array of objects representing $market $data
         */
        $response = $this->futuresPublicGetContractsActive ($params);
        //
        //    {
        //        "code" => "200000",
        //        "data" => {
        //            "symbol" => "ETHUSDTM",
        //            "rootSymbol" => "USDT",
        //            "type" => "FFWCSX",
        //            "firstOpenDate" => 1591086000000,
        //            "expireDate" => null,
        //            "settleDate" => null,
        //            "baseCurrency" => "ETH",
        //            "quoteCurrency" => "USDT",
        //            "settleCurrency" => "USDT",
        //            "maxOrderQty" => 1000000,
        //            "maxPrice" => 1000000.********00,
        //            "lotSize" => 1,
        //            "tickSize" => 0.05,
        //            "indexPriceTickSize" => 0.01,
        //            "multiplier" => 0.01,
        //            "initialMargin" => 0.01,
        //            "maintainMargin" => 0.005,
        //            "maxRiskLimit" => 1000000,
        //            "minRiskLimit" => 1000000,
        //            "riskStep" => 500000,
        //            "makerFeeRate" => 0.00020,
        //            "takerFeeRate" => 0.00060,
        //            "takerFixFee" => 0.********00,
        //            "makerFixFee" => 0.********00,
        //            "settlementFee" => null,
        //            "isDeleverage" => true,
        //            "isQuanto" => true,
        //            "isInverse" => false,
        //            "markMethod" => "FairPrice",
        //            "fairMethod" => "FundingRate",
        //            "fundingBaseSymbol" => ".ETHINT8H",
        //            "fundingQuoteSymbol" => ".USDTINT8H",
        //            "fundingRateSymbol" => ".ETHUSDTMFPI8H",
        //            "indexSymbol" => ".KETHUSDT",
        //            "settlementSymbol" => "",
        //            "status" => "Open",
        //            "fundingFeeRate" => 0.000535,
        //            "predictedFundingFeeRate" => 0.002197,
        //            "openInterest" => "8724443",
        //            "turnoverOf24h" => 341156641.03354263,
        //            "volumeOf24h" => 74833.54000000,
        //            "markPrice" => 4534.07,
        //            "indexPrice":4531.92,
        //            "lastTradePrice" => 4545.45********,
        //            "nextFundingRateTime" => 25481884,
        //            "maxLeverage" => 100,
        //            "sourceExchanges" =>  array( "huobi", "Okex", "Binance", "Kucoin", "Poloniex", "Hitbtc" ),
        //            "premiumsSymbol1M" => ".ETHUSDTMPI",
        //            "premiumsSymbol8H" => ".ETHUSDTMPI8H",
        //            "fundingBaseSymbol1M" => ".ETHINT",
        //            "fundingQuoteSymbol1M" => ".USDTINT",
        //            "lowPrice" => 4456.90,
        //            "highPrice" =>  4674.25,
        //            "priceChgPct" => 0.0046,
        //            "priceChg" => 21.15
        //        }
        //    }
        //
        $result = array();
        $data = $this->safe_list($response, 'data', array());
        for ($i = 0; $i < count($data); $i++) {
            $market = $data[$i];
            $id = $this->safe_string($market, 'symbol');
            $expiry = $this->safe_integer($market, 'expireDate');
            $future = $expiry ? true : false;
            $swap = !$future;
            $baseId = $this->safe_string($market, 'baseCurrency');
            $quoteId = $this->safe_string($market, 'quoteCurrency');
            $settleId = $this->safe_string($market, 'settleCurrency');
            $base = $this->safe_currency_code($baseId);
            $quote = $this->safe_currency_code($quoteId);
            $settle = $this->safe_currency_code($settleId);
            $symbol = $base . '/' . $quote . ':' . $settle;
            $type = 'swap';
            if ($future) {
                $symbol = $symbol . '-' . $this->yymmdd($expiry, '');
                $type = 'future';
            }
            $inverse = $this->safe_value($market, 'isInverse');
            $status = $this->safe_string($market, 'status');
            $multiplier = $this->safe_string($market, 'multiplier');
            $tickSize = $this->safe_number($market, 'tickSize');
            $lotSize = $this->safe_number($market, 'lotSize');
            $limitAmountMin = $lotSize;
            if ($limitAmountMin === null) {
                $limitAmountMin = $this->safe_number($market, 'baseMinSize');
            }
            $limitAmountMax = $this->safe_number($market, 'maxOrderQty');
            if ($limitAmountMax === null) {
                $limitAmountMax = $this->safe_number($market, 'baseMaxSize');
            }
            $limitPriceMax = $this->safe_number($market, 'maxPrice');
            if ($limitPriceMax === null) {
                $baseMinSizeString = $this->safe_string($market, 'baseMinSize');
                $quoteMaxSizeString = $this->safe_string($market, 'quoteMaxSize');
                $limitPriceMax = $this->parse_number(Precise::string_div($quoteMaxSizeString, $baseMinSizeString));
            }
            $result[] = array(
                'id' => $id,
                'symbol' => $symbol,
                'base' => $base,
                'quote' => $quote,
                'settle' => $settle,
                'baseId' => $baseId,
                'quoteId' => $quoteId,
                'settleId' => $settleId,
                'type' => $type,
                'spot' => false,
                'margin' => false,
                'swap' => $swap,
                'future' => $future,
                'option' => false,
                'active' => ($status === 'Open'),
                'contract' => true,
                'linear' => !$inverse,
                'inverse' => $inverse,
                'taker' => $this->safe_number($market, 'takerFeeRate'),
                'maker' => $this->safe_number($market, 'makerFeeRate'),
                'contractSize' => $this->parse_number(Precise::string_abs($multiplier)),
                'expiry' => $expiry,
                'expiryDatetime' => $this->iso8601($expiry),
                'strike' => null,
                'optionType' => null,
                'precision' => array(
                    'amount' => $lotSize,
                    'price' => $tickSize,
                ),
                'limits' => array(
                    'leverage' => array(
                        'min' => $this->parse_number('1'),
                        'max' => $this->safe_number($market, 'maxLeverage'),
                    ),
                    'amount' => array(
                        'min' => $limitAmountMin,
                        'max' => $limitAmountMax,
                    ),
                    'price' => array(
                        'min' => $tickSize,
                        'max' => $limitPriceMax,
                    ),
                    'cost' => array(
                        'min' => $this->safe_number($market, 'quoteMinSize'),
                        'max' => $this->safe_number($market, 'quoteMaxSize'),
                    ),
                ),
                'created' => $this->safe_integer($market, 'firstOpenDate'),
                'info' => $market,
            );
        }
        return $result;
    }

    public function fetch_time($params = array ()): ?int {
        /**
         * fetches the current integer timestamp in milliseconds from the exchange server
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-server-time
         *
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {int} the current integer timestamp in milliseconds from the exchange server
         */
        $response = $this->futuresPublicGetTimestamp ($params);
        //
        //    {
        //        "code" => "200000",
        //        "data" => 1637385119302,
        //    }
        //
        return $this->safe_integer($response, 'data');
    }

    public function fetch_ohlcv(string $symbol, $timeframe = '1m', ?int $since = null, ?int $limit = null, $params = array ()): array {
        /**
         * fetches historical candlestick $data containing the open, high, low, and close price, and the volume of a $market
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-klines
         *
         * @param {string} $symbol unified $symbol of the $market to fetch OHLCV $data for
         * @param {string} $timeframe the length of time each candle represents
         * @param {int} [$since] timestamp in ms of the earliest candle to fetch
         * @param {int} [$limit] the maximum amount of candles to fetch
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @param {boolean} [$params->paginate] default false, when true will automatically $paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-$params)
         * @return {int[][]} A list of candles ordered, open, high, low, close, volume
         */
        $this->load_markets();
        $paginate = false;
        list($paginate, $params) = $this->handle_option_and_params($params, 'fetchOHLCV', 'paginate');
        if ($paginate) {
            return $this->fetch_paginated_call_deterministic('fetchOHLCV', $symbol, $since, $limit, $timeframe, $params, 200);
        }
        $market = $this->market($symbol);
        $marketId = $market['id'];
        $parsedTimeframe = $this->safe_integer($this->timeframes, $timeframe);
        $request = array(
            'symbol' => $marketId,
        );
        if ($parsedTimeframe !== null) {
            $request['granularity'] = $parsedTimeframe;
        } else {
            $request['granularity'] = $timeframe;
        }
        $duration = $this->parse_timeframe($timeframe) * 1000;
        $endAt = $this->milliseconds();
        if ($since !== null) {
            $request['from'] = $since;
            if ($limit === null) {
                $limit = $this->safe_integer($this->options, 'fetchOHLCVLimit', 200);
            }
            $endAt = $this->sum($since, $limit * $duration);
        } elseif ($limit !== null) {
            $since = $endAt - $limit * $duration;
            $request['from'] = $since;
        }
        $request['to'] = $endAt;
        $response = $this->futuresPublicGetKlineQuery ($this->extend($request, $params));
        //
        //    {
        //        "code" => "200000",
        //        "data" => [
        //            [1636459200000, 4779.3, 4792.1, 4768.7, 4770.3, 78051],
        //            [1636460100000, 4770.25, 4778.55, 4757.55, 4777.25, 80164],
        //            [1636461000000, 4777.25, 4791.45, 4774.5, 4791.3, 51555]
        //        ]
        //    }
        //
        $data = $this->safe_list($response, 'data', array());
        return $this->parse_ohlcvs($data, $market, $timeframe, $since, $limit);
    }

    public function parse_ohlcv($ohlcv, ?array $market = null): array {
        //
        //    array(
        //        "1545904980000",          // Start time of the candle cycle
        //        "0.058",                  // opening price
        //        "0.049",                  // closing price
        //        "0.058",                  // highest price
        //        "0.049",                  // lowest price
        //        "0.018",                  // base volume
        //        "0.000945",               // quote volume
        //    )
        //
        return array(
            $this->safe_integer($ohlcv, 0),
            $this->safe_number($ohlcv, 1),
            $this->safe_number($ohlcv, 2),
            $this->safe_number($ohlcv, 3),
            $this->safe_number($ohlcv, 4),
            $this->safe_number($ohlcv, 5),
        );
    }

    public function fetch_deposit_address(string $code, $params = array ()): array {
        /**
         * fetch the deposit $address for a $currency associated with this account
         *
         * @see https://www.kucoin.com/docs/rest/funding/deposit/get-deposit-$address
         *
         * @param {string} $code unified $currency $code
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} an ~@link https://docs.ccxt.com/#/?id=$address-structure $address structure~
         */
        $this->load_markets();
        $currency = $this->currency($code);
        $currencyId = $currency['id'];
        $request = array(
            'currency' => $currencyId, // Currency,including XBT,USDT
        );
        $response = $this->futuresPrivateGetDepositAddress ($this->extend($request, $params));
        //
        //    {
        //        "code" => "200000",
        //        "data" => {
        //            "address" => "0x78d3ad1c0aa1bf068e19c94a2d7b16c9c0fcd8b1",//Deposit $address
        //            "memo" => null//Address tag. If the returned value is null, it means that the requested token has no memo. If you are to transfer funds from another platform to KuCoin Futures and if the token to be //transferred has memo(tag), you need to fill in the memo to ensure the transferred funds will be sent //to the $address you specified.
        //        }
        //    }
        //
        $data = $this->safe_dict($response, 'data', array());
        $address = $this->safe_string($data, 'address');
        if ($currencyId !== 'NIM') {
            // contains spaces
            $this->check_address($address);
        }
        return array(
            'info' => $response,
            'currency' => $currencyId,
            'network' => $this->safe_string($data, 'chain'),
            'address' => $address,
            'tag' => $this->safe_string($data, 'memo'),
        );
    }

    public function fetch_order_book(string $symbol, ?int $limit = null, $params = array ()): array {
        /**
         * fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other $data
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-part-order-book-$level-2
         *
         * @param {string} $symbol unified $symbol of the $market to fetch the order book for
         * @param {int} [$limit] the maximum amount of order book entries to return
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} A dictionary of ~@link https://docs.ccxt.com/#/?id=order-book-structure order book structures~ indexed by $market symbols
         */
        $this->load_markets();
        $level = $this->safe_number($params, 'level');
        if ($level !== 2 && $level !== null) {
            throw new BadRequest($this->id . ' fetchOrderBook() can only return $level 2');
        }
        $market = $this->market($symbol);
        $request = array(
            'symbol' => $market['id'],
        );
        if ($limit !== null) {
            if (($limit === 20) || ($limit === 100)) {
                $request['limit'] = $limit;
            } else {
                throw new BadRequest($this->id . ' fetchOrderBook() $limit argument must be 20 or 100');
            }
        } else {
            $request['limit'] = 20;
        }
        $response = $this->futuresPublicGetLevel2DepthLimit ($this->extend($request, $params));
        //
        //     {
        //         "code" => "200000",
        //         "data" => {
        //           "symbol" => "XBTUSDM",      //Symbol
        //           "sequence" => 100,          //Ticker sequence number
        //           "asks" => [
        //                 ["5000.0", 1000],   //Price, quantity
        //                 ["6000.0", 1983]    //Price, quantity
        //           ],
        //           "bids" => [
        //                 ["3200.0", 800],    //Price, quantity
        //                 ["3100.0", 100]     //Price, quantity
        //           ],
        //           "ts" => 1604643655040584408  // $timestamp
        //         }
        //     }
        //
        $data = $this->safe_dict($response, 'data', array());
        $timestamp = $this->parse_to_int($this->safe_integer($data, 'ts') / 1000000);
        $orderbook = $this->parse_order_book($data, $market['symbol'], $timestamp, 'bids', 'asks', 0, 1);
        $orderbook['nonce'] = $this->safe_integer($data, 'sequence');
        return $orderbook;
    }

    public function fetch_ticker(string $symbol, $params = array ()): array {
        /**
         * fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific $market
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-ticker
         *
         * @param {string} $symbol unified $symbol of the $market to fetch the ticker for
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} a ~@link https://docs.ccxt.com/#/?id=ticker-structure ticker structure~
         */
        $this->load_markets();
        $market = $this->market($symbol);
        $request = array(
            'symbol' => $market['id'],
        );
        $response = $this->futuresPublicGetTicker ($this->extend($request, $params));
        //
        //    {
        //        "code" => "200000",
        //        "data" => {
        //            "sequence" => 1638444978558,
        //            "symbol" => "ETHUSDTM",
        //            "side" => "sell",
        //            "size" => 4,
        //            "price" => "4229.35",
        //            "bestBidSize" => 2160,
        //            "bestBidPrice" => "4229.0",
        //            "bestAskPrice" => "4229.05",
        //            "tradeId" => "61aaa8b777a0c43055fe4851",
        //            "ts" => 1638574296209786785,
        //            "bestAskSize" => 36,
        //        }
        //    }
        //
        return $this->parse_ticker($response['data'], $market);
    }

    public function fetch_mark_price(string $symbol, $params = array ()): array {
        /**
         * fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific $market
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-current-mark-price
         *
         * @param {string} $symbol unified $symbol of the $market to fetch the ticker for
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} a ~@link https://docs.ccxt.com/#/?id=ticker-structure ticker structure~
         */
        $this->load_markets();
        $market = $this->market($symbol);
        $request = array(
            'symbol' => $market['id'],
        );
        $response = $this->futuresPublicGetMarkPriceSymbolCurrent ($this->extend($request, $params));
        //
        return $this->parse_ticker($response['data'], $market);
    }

    public function fetch_tickers(?array $symbols = null, $params = array ()): array {
        /**
         * fetches price $tickers for multiple markets, statistical information calculated over the past 24 hours for each market
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-$symbols-list
         *
         * @param {string[]} [$symbols] unified $symbols of the markets to fetch the ticker for, all market $tickers are returned if not assigned
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @param {string} [$params->method] the $method to use, futuresPublicGetAllTickers or futuresPublicGetContractsActive
         * @return {array} a dictionary of ~@link https://docs.ccxt.com/#/?id=ticker-structure ticker structures~
         */
        $this->load_markets();
        $symbols = $this->market_symbols($symbols);
        $method = null;
        list($method, $params) = $this->handle_option_and_params($params, 'fetchTickers', 'method', 'futuresPublicGetContractsActive');
        $response = null;
        if ($method === 'futuresPublicGetAllTickers') {
            $response = $this->futuresPublicGetAllTickers ($params);
        } else {
            $response = $this->futuresPublicGetContractsActive ($params);
        }
        //
        //    {
        //        "code" => "200000",
        //        "data" => {
        //            "symbol" => "ETHUSDTM",
        //            "rootSymbol" => "USDT",
        //            "type" => "FFWCSX",
        //            "firstOpenDate" => 1591086000000,
        //            "expireDate" => null,
        //            "settleDate" => null,
        //            "baseCurrency" => "ETH",
        //            "quoteCurrency" => "USDT",
        //            "settleCurrency" => "USDT",
        //            "maxOrderQty" => 1000000,
        //            "maxPrice" => 1000000.********00,
        //            "lotSize" => 1,
        //            "tickSize" => 0.05,
        //            "indexPriceTickSize" => 0.01,
        //            "multiplier" => 0.01,
        //            "initialMargin" => 0.01,
        //            "maintainMargin" => 0.005,
        //            "maxRiskLimit" => 1000000,
        //            "minRiskLimit" => 1000000,
        //            "riskStep" => 500000,
        //            "makerFeeRate" => 0.00020,
        //            "takerFeeRate" => 0.00060,
        //            "takerFixFee" => 0.********00,
        //            "makerFixFee" => 0.********00,
        //            "settlementFee" => null,
        //            "isDeleverage" => true,
        //            "isQuanto" => true,
        //            "isInverse" => false,
        //            "markMethod" => "FairPrice",
        //            "fairMethod" => "FundingRate",
        //            "fundingBaseSymbol" => ".ETHINT8H",
        //            "fundingQuoteSymbol" => ".USDTINT8H",
        //            "fundingRateSymbol" => ".ETHUSDTMFPI8H",
        //            "indexSymbol" => ".KETHUSDT",
        //            "settlementSymbol" => "",
        //            "status" => "Open",
        //            "fundingFeeRate" => 0.000535,
        //            "predictedFundingFeeRate" => 0.002197,
        //            "openInterest" => "8724443",
        //            "turnoverOf24h" => 341156641.03354263,
        //            "volumeOf24h" => 74833.54000000,
        //            "markPrice" => 4534.07,
        //            "indexPrice":4531.92,
        //            "lastTradePrice" => 4545.45********,
        //            "nextFundingRateTime" => 25481884,
        //            "maxLeverage" => 100,
        //            "sourceExchanges" =>  array( "huobi", "Okex", "Binance", "Kucoin", "Poloniex", "Hitbtc" ),
        //            "premiumsSymbol1M" => ".ETHUSDTMPI",
        //            "premiumsSymbol8H" => ".ETHUSDTMPI8H",
        //            "fundingBaseSymbol1M" => ".ETHINT",
        //            "fundingQuoteSymbol1M" => ".USDTINT",
        //            "lowPrice" => 4456.90,
        //            "highPrice" =>  4674.25,
        //            "priceChgPct" => 0.0046,
        //            "priceChg" => 21.15
        //        }
        //    }
        //
        $data = $this->safe_list($response, 'data');
        $tickers = $this->parse_tickers($data, $symbols);
        return $this->filter_by_array_tickers($tickers, 'symbol', $symbols);
    }

    public function parse_ticker(array $ticker, ?array $market = null): array {
        //
        //     {
        //         "symbol" => "LTCUSDTM",
        //         "granularity" => 1000,
        //         "timePoint" => 1727967339000,
        //         "value" => 62.37, mark price
        //         "indexPrice" => 62.37
        //      }
        //
        //     {
        //         "code" => "200000",
        //         "data" => {
        //             "sequence" =>  1629930362547,
        //             "symbol" => "ETHUSDTM",
        //             "side" => "buy",
        //             "size" =>  130,
        //             "price" => "4724.7",
        //             "bestBidSize" =>  5,
        //             "bestBidPrice" => "4724.6",
        //             "bestAskPrice" => "4724.65",
        //             "tradeId" => "618d2a5a77a0c4431d2335f4",
        //             "ts" =>  1636641371963227600,
        //             "bestAskSize" =>  1789
        //          }
        //     }
        //
        // from fetchTickers
        //
        // {
        //     symbol => "XBTUSDTM",
        //     rootSymbol => "USDT",
        //     type => "FFWCSX",
        //     firstOpenDate => 1585555200000,
        //     expireDate => null,
        //     settleDate => null,
        //     baseCurrency => "XBT",
        //     quoteCurrency => "USDT",
        //     settleCurrency => "USDT",
        //     maxOrderQty => 1000000,
        //     maxPrice => 1000000,
        //     lotSize => 1,
        //     tickSize => 0.1,
        //     indexPriceTickSize => 0.01,
        //     multiplier => 0.001,
        //     initialMargin => 0.008,
        //     maintainMargin => 0.004,
        //     maxRiskLimit => 100000,
        //     minRiskLimit => 100000,
        //     riskStep => 50000,
        //     makerFeeRate => 0.0002,
        //     takerFeeRate => 0.0006,
        //     takerFixFee => 0,
        //     makerFixFee => 0,
        //     settlementFee => null,
        //     isDeleverage => true,
        //     isQuanto => true,
        //     isInverse => false,
        //     markMethod => "FairPrice",
        //     fairMethod => "FundingRate",
        //     fundingBaseSymbol => ".XBTINT8H",
        //     fundingQuoteSymbol => ".USDTINT8H",
        //     fundingRateSymbol => ".XBTUSDTMFPI8H",
        //     indexSymbol => ".KXBTUSDT",
        //     settlementSymbol => "",
        //     status => "Open",
        //     fundingFeeRate => 0.000297,
        //     predictedFundingFeeRate => 0.000327,
        //     fundingRateGranularity => 28800000,
        //     openInterest => "8033200",
        //     turnoverOf24h => 659795309.2524643,
        //     volumeOf24h => 9998.54,
        //     markPrice => 67193.51,
        //     indexPrice => 67184.81,
        //     lastTradePrice => 67191.8,
        //     nextFundingRateTime => 20022985,
        //     maxLeverage => 125,
        //     premiumsSymbol1M => ".XBTUSDTMPI",
        //     premiumsSymbol8H => ".XBTUSDTMPI8H",
        //     fundingBaseSymbol1M => ".XBTINT",
        //     fundingQuoteSymbol1M => ".USDTINT",
        //     lowPrice => 64041.6,
        //     highPrice => 67737.3,
        //     priceChgPct => 0.0447,
        //     priceChg => 2878.7
        // }
        //
        $marketId = $this->safe_string($ticker, 'symbol');
        $market = $this->safe_market($marketId, $market, '-');
        $last = $this->safe_string_2($ticker, 'price', 'lastTradePrice');
        $timestamp = $this->safe_integer_product($ticker, 'ts', 0.000001);
        return $this->safe_ticker(array(
            'symbol' => $market['symbol'],
            'timestamp' => $timestamp,
            'datetime' => $this->iso8601($timestamp),
            'high' => $this->safe_string($ticker, 'highPrice'),
            'low' => $this->safe_string($ticker, 'lowPrice'),
            'bid' => $this->safe_string($ticker, 'bestBidPrice'),
            'bidVolume' => $this->safe_string($ticker, 'bestBidSize'),
            'ask' => $this->safe_string($ticker, 'bestAskPrice'),
            'askVolume' => $this->safe_string($ticker, 'bestAskSize'),
            'vwap' => null,
            'open' => null,
            'close' => $last,
            'last' => $last,
            'previousClose' => null,
            'change' => $this->safe_string($ticker, 'priceChg'),
            'percentage' => $this->safe_string($ticker, 'priceChgPct'),
            'average' => null,
            'baseVolume' => $this->safe_string($ticker, 'volumeOf24h'),
            'quoteVolume' => $this->safe_string($ticker, 'turnoverOf24h'),
            'markPrice' => $this->safe_string_2($ticker, 'markPrice', 'value'),
            'indexPrice' => $this->safe_string($ticker, 'indexPrice'),
            'info' => $ticker,
        ), $market);
    }

    public function fetch_bids_asks(?array $symbols = null, $params = array ()) {
        /**
         * fetches the bid and ask price and volume for multiple markets
         * @param {string[]} [$symbols] unified $symbols of the markets to fetch the bids and asks for, all markets are returned if not assigned
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} a dictionary of ~@link https://docs.ccxt.com/#/?id=ticker-structure ticker structures~
         */
        $request = array(
            'method' => 'futuresPublicGetAllTickers',
        );
        return $this->fetch_tickers($symbols, $this->extend($request, $params));
    }

    public function fetch_funding_history(?string $symbol = null, ?int $since = null, ?int $limit = null, $params = array ()) {
        /**
         * fetch the history of funding payments paid and received on this account
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/funding-fees/get-funding-history
         *
         * @param {string} $symbol unified $market $symbol
         * @param {int} [$since] the earliest time in ms to fetch funding history for
         * @param {int} [$limit] the maximum number of funding history structures to retrieve
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} a ~@link https://docs.ccxt.com/#/?id=funding-history-structure funding history structure~
         */
        if ($symbol === null) {
            throw new ArgumentsRequired($this->id . ' fetchFundingHistory() requires a $symbol argument');
        }
        $this->load_markets();
        $market = $this->market($symbol);
        $request = array(
            'symbol' => $market['id'],
        );
        if ($since !== null) {
            $request['startAt'] = $since;
        }
        if ($limit !== null) {
            // * Since is ignored if $limit is defined
            $request['maxCount'] = $limit;
        }
        $response = $this->futuresPrivateGetFundingHistory ($this->extend($request, $params));
        //
        //    {
        //        "code" => "200000",
        //        "data" => {
        //            "dataList" => array(
        //                array(
        //                    "id" => 239471298749817,
        //                    "symbol" => "ETHUSDTM",
        //                    "timePoint" => 1638532800000,
        //                    "fundingRate" => 0.000100,
        //                    "markPrice" => 4612.83********,
        //                    "positionQty" => 12,
        //                    "positionCost" => 553.5396000000,
        //                    "funding" => -0.0553539600,
        //                    "settleCurrency" => "USDT"
        //                ),
        //                ...
        //            ),
        //            "hasMore" => true
        //        }
        //    }
        //
        $data = $this->safe_value($response, 'data');
        $dataList = $this->safe_list($data, 'dataList', array());
        $fees = array();
        for ($i = 0; $i < count($dataList); $i++) {
            $listItem = $dataList[$i];
            $timestamp = $this->safe_integer($listItem, 'timePoint');
            $fees[] = array(
                'info' => $listItem,
                'symbol' => $symbol,
                'code' => $this->safe_currency_code($this->safe_string($listItem, 'settleCurrency')),
                'timestamp' => $timestamp,
                'datetime' => $this->iso8601($timestamp),
                'id' => $this->safe_number($listItem, 'id'),
                'amount' => $this->safe_number($listItem, 'funding'),
                'fundingRate' => $this->safe_number($listItem, 'fundingRate'),
                'markPrice' => $this->safe_number($listItem, 'markPrice'),
                'positionQty' => $this->safe_number($listItem, 'positionQty'),
                'positionCost' => $this->safe_number($listItem, 'positionCost'),
            );
        }
        return $fees;
    }

    public function fetch_position(string $symbol, $params = array ()) {
        /**
         *
         * @see https://docs.kucoin.com/futures/#get-position-details
         *
         * fetch $data on an open position
         * @param {string} $symbol unified $market $symbol of the $market the position is held in
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} a ~@link https://docs.ccxt.com/#/?id=position-structure position structure~
         */
        $this->load_markets();
        $market = $this->market($symbol);
        $request = array(
            'symbol' => $market['id'],
        );
        $response = $this->futuresPrivateGetPosition ($this->extend($request, $params));
        //
        //    {
        //        "code" => "200000",
        //        "data" => {
        //            "id" => "6505ee6eaff4070001f651c4",
        //            "symbol" => "XBTUSDTM",
        //            "autoDeposit" => false,
        //            "maintMarginReq" => 0,
        //            "riskLimit" => 200,
        //            "realLeverage" => 0.0,
        //            "crossMode" => false,
        //            "delevPercentage" => 0.0,
        //            "currentTimestamp" => 1694887534594,
        //            "currentQty" => 0,
        //            "currentCost" => 0.0,
        //            "currentComm" => 0.0,
        //            "unrealisedCost" => 0.0,
        //            "realisedGrossCost" => 0.0,
        //            "realisedCost" => 0.0,
        //            "isOpen" => false,
        //            "markPrice" => 26611.71,
        //            "markValue" => 0.0,
        //            "posCost" => 0.0,
        //            "posCross" => 0,
        //            "posInit" => 0.0,
        //            "posComm" => 0.0,
        //            "posLoss" => 0.0,
        //            "posMargin" => 0.0,
        //            "posMaint" => 0.0,
        //            "maintMargin" => 0.0,
        //            "realisedGrossPnl" => 0.0,
        //            "realisedPnl" => 0.0,
        //            "unrealisedPnl" => 0.0,
        //            "unrealisedPnlPcnt" => 0,
        //            "unrealisedRoePcnt" => 0,
        //            "avgEntryPrice" => 0.0,
        //            "liquidationPrice" => 0.0,
        //            "bankruptPrice" => 0.0,
        //            "settleCurrency" => "USDT",
        //            "maintainMargin" => 0,
        //            "riskLimitLevel" => 1
        //        }
        //    }
        //
        $data = $this->safe_dict($response, 'data', array());
        return $this->parse_position($data, $market);
    }

    public function fetch_positions(?array $symbols = null, $params = array ()): array {
        /**
         * fetch all open positions
         *
         * @see https://docs.kucoin.com/futures/#get-position-list
         *
         * @param {string[]|null} $symbols list of unified market $symbols
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array[]} a list of ~@link https://docs.ccxt.com/#/?id=position-structure position structure~
         */
        $this->load_markets();
        $response = $this->futuresPrivateGetPositions ($params);
        //
        //    {
        //        "code" => "200000",
        //        "data" => array(
        //            {
        //                "id" => "615ba79f83a3410001cde321",
        //                "symbol" => "ETHUSDTM",
        //                "autoDeposit" => false,
        //                "maintMarginReq" => 0.005,
        //                "riskLimit" => 1000000,
        //                "realLeverage" => 18.61,
        //                "crossMode" => false,
        //                "delevPercentage" => 0.86,
        //                "openingTimestamp" => 1638563515618,
        //                "currentTimestamp" => 1638576872774,
        //                "currentQty" => 2,
        //                "currentCost" => 83.64200000,
        //                "currentComm" => 0.********,
        //                "unrealisedCost" => 83.64200000,
        //                "realisedGrossCost" => 0.********,
        //                "realisedCost" => 0.********,
        //                "isOpen" => true,
        //                "markPrice" => 4225.01,
        //                "markValue" => 84.50020000,
        //                "posCost" => 83.64200000,
        //                "posCross" => 0.********00,
        //                "posInit" => 3.63660870,
        //                "posComm" => 0.05236717,
        //                "posLoss" => 0.********,
        //                "posMargin" => 3.68897586,
        //                "posMaint" => 0.********,
        //                "maintMargin" => 4.********,
        //                "realisedGrossPnl" => 0.********,
        //                "realisedPnl" => -0.********,
        //                "unrealisedPnl" => 0.********,
        //                "unrealisedPnlPcnt" => 0.0103,
        //                "unrealisedRoePcnt" => 0.2360,
        //                "avgEntryPrice" => 4182.10,
        //                "liquidationPrice" => 4023.00,
        //                "bankruptPrice" => 4000.25,
        //                "settleCurrency" => "USDT",
        //                "isInverse" => false
        //            }
        //        )
        //    }
        //
        $data = $this->safe_list($response, 'data');
        return $this->parse_positions($data, $symbols);
    }

    public function fetch_positions_history(?array $symbols = null, ?int $since = null, ?int $limit = null, $params = array ()) {
        /**
         * fetches historical positions
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/positions/get-positions-history
         *
         * @param {string[]} [$symbols] list of unified market $symbols
         * @param {int} [$since] the earliest time in ms to fetch position history for
         * @param {int} [$limit] the maximum number of entries to retrieve
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @param {int} [$params->until] closing end time
         * @param {int} [$params->pageId] page id
         * @return {array[]} a list of ~@link https://docs.ccxt.com/#/?id=position-structure position structure~
         */
        $this->load_markets();
        if ($limit === null) {
            $limit = 200;
        }
        $request = array(
            'limit' => $limit,
        );
        if ($since !== null) {
            $request['from'] = $since;
        }
        $until = $this->safe_integer($params, 'until');
        if ($until !== null) {
            $params = $this->omit($params, 'until');
            $request['to'] = $until;
        }
        $response = $this->futuresPrivateGetHistoryPositions ($this->extend($request, $params));
        //
        // {
        //     "success" => true,
        //     "code" => "200",
        //     "msg" => "success",
        //     "retry" => false,
        //     "data" => {
        //         "currentPage" => 1,
        //         "pageSize" => 10,
        //         "totalNum" => 25,
        //         "totalPage" => 3,
        //         "items" => array(
        //             {
        //                 "closeId" => "3********000000030",
        //                 "positionId" => "3****************9",
        //                 "uid" => **************,
        //                 "userId" => "6527d4fc8c7f3d0001f40f5f",
        //                 "symbol" => "XBTUSDM",
        //                 "settleCurrency" => "XBT",
        //                 "leverage" => "0.0",
        //                 "type" => "LIQUID_LONG",
        //                 "side" => null,
        //                 "closeSize" => null,
        //                 "pnl" => "-1.****************",
        //                 "realisedGrossCost" => "0.****************",
        //                 "withdrawPnl" => "0.0",
        //                 "roe" => null,
        //                 "tradeFee" => "0.0006154045",
        //                 "fundingFee" => "0.0",
        //                 "openTime" => 1713785751181,
        //                 "closeTime" => 1713785752784,
        //                 "openPrice" => null,
        //                 "closePrice" => null
        //             }
        //         )
        //     }
        // }
        //
        $data = $this->safe_dict($response, 'data');
        $items = $this->safe_list($data, 'items', array());
        return $this->parse_positions($items, $symbols);
    }

    public function parse_position(array $position, ?array $market = null) {
        //
        //    {
        //        "code" => "200000",
        //        "data" => array(
        //            {
        //                "id" => "615ba79f83a3410001cde321",         // Position ID
        //                "symbol" => "ETHUSDTM",                     // Symbol
        //                "autoDeposit" => false,                     // Auto deposit margin or not
        //                "maintMarginReq" => 0.005,                  // Maintenance margin requirement
        //                "riskLimit" => 1000000,                     // Risk limit
        //                "realLeverage" => 25.92,                    // Leverage of the order
        //                "crossMode" => false,                       // Cross mode or not
        //                "delevPercentage" => 0.76,                  // ADL ranking percentile
        //                "openingTimestamp" => 1638578546031,        // Open time
        //                "currentTimestamp" => 1638578563580,        // Current $timestamp
        //                "currentQty" => 2,                          // Current postion quantity
        //                "currentCost" => 83.787,                    // Current postion value
        //                "currentComm" => 0.0167574,                 // Current commission
        //                "unrealisedCost" => 83.787,                 // Unrealised value
        //                "realisedGrossCost" => 0.0,                 // Accumulated realised gross profit value
        //                "realisedCost" => 0.0167574,                // Current realised $position value
        //                "isOpen" => true,                           // Opened $position or not
        //                "markPrice" => 4183.38,                     // Mark price
        //                "markValue" => 83.6676,                     // Mark value
        //                "posCost" => 83.787,                        // Position value
        //                "posCross" => 0.0,                          // added margin
        //                "posInit" => 3.35148,                       // Leverage margin
        //                "posComm" => 0.********,                    // Bankruptcy cost
        //                "posLoss" => 0.0,                           // Funding fees paid out
        //                "posMargin" => 3.********,                  // Position margin
        //                "posMaint" => 0.********,                   // Maintenance margin
        //                "maintMargin" => 3.********,                // Position margin
        //                "realisedGrossPnl" => 0.0,                  // Accumulated realised gross profit value
        //                "realisedPnl" => -0.0167574,                // Realised profit and loss
        //                "unrealisedPnl" => -0.1194,                 // Unrealised profit and loss
        //                "unrealisedPnlPcnt" => -0.0014,             // Profit-loss ratio of the $position
        //                "unrealisedRoePcnt" => -0.0356,             // Rate of return on investment
        //                "avgEntryPrice" => 4189.35,                 // Average entry price
        //                "liquidationPrice" => 4044.55,              // Liquidation price
        //                "bankruptPrice" => 4021.75,                 // Bankruptcy price
        //                "settleCurrency" => "USDT",                 // Currency used to clear and settle the trades
        //                "isInverse" => false
        //            }
        //        )
        //    }
        // $position history
        //             {
        //                 "closeId" => "3********000000030",
        //                 "positionId" => "3****************9",
        //                 "uid" => **************,
        //                 "userId" => "6527d4fc8c7f3d0001f40f5f",
        //                 "symbol" => "XBTUSDM",
        //                 "settleCurrency" => "XBT",
        //                 "leverage" => "0.0",
        //                 "type" => "LIQUID_LONG",
        //                 "side" => null,
        //                 "closeSize" => null,
        //                 "pnl" => "-1.****************",
        //                 "realisedGrossCost" => "0.****************",
        //                 "withdrawPnl" => "0.0",
        //                 "roe" => null,
        //                 "tradeFee" => "0.0006154045",
        //                 "fundingFee" => "0.0",
        //                 "openTime" => 1713785751181,
        //                 "closeTime" => 1713785752784,
        //                 "openPrice" => null,
        //                 "closePrice" => null
        //             }
        //
        $symbol = $this->safe_string($position, 'symbol');
        $market = $this->safe_market($symbol, $market);
        $timestamp = $this->safe_integer($position, 'currentTimestamp');
        $size = $this->safe_string($position, 'currentQty');
        $side = null;
        $type = $this->safe_string_lower($position, 'type');
        if ($size !== null) {
            if (Precise::string_gt($size, '0')) {
                $side = 'long';
            } elseif (Precise::string_lt($size, '0')) {
                $side = 'short';
            }
        } elseif ($type !== null) {
            if (mb_strpos($type, 'long') > -1) {
                $side = 'long';
            } else {
                $side = 'short';
            }
        }
        $notional = Precise::string_abs($this->safe_string($position, 'posCost'));
        $initialMargin = $this->safe_string($position, 'posInit');
        $initialMarginPercentage = Precise::string_div($initialMargin, $notional);
        // $marginRatio = Precise::string_div(maintenanceRate, collateral);
        $unrealisedPnl = $this->safe_string($position, 'unrealisedPnl');
        $crossMode = $this->safe_value($position, 'crossMode');
        // currently $crossMode is always set to false and only isolated positions are supported
        $marginMode = null;
        if ($crossMode !== null) {
            $marginMode = $crossMode ? 'cross' : 'isolated';
        }
        return $this->safe_position(array(
            'info' => $position,
            'id' => $this->safe_string_2($position, 'id', 'positionId'),
            'symbol' => $this->safe_string($market, 'symbol'),
            'timestamp' => $timestamp,
            'datetime' => $this->iso8601($timestamp),
            'lastUpdateTimestamp' => $this->safe_integer($position, 'closeTime'),
            'initialMargin' => $this->parse_number($initialMargin),
            'initialMarginPercentage' => $this->parse_number($initialMarginPercentage),
            'maintenanceMargin' => $this->safe_number($position, 'posMaint'),
            'maintenanceMarginPercentage' => $this->safe_number($position, 'maintMarginReq'),
            'entryPrice' => $this->safe_number_2($position, 'avgEntryPrice', 'openPrice'),
            'notional' => $this->parse_number($notional),
            'leverage' => $this->safe_number_2($position, 'realLeverage', 'leverage'),
            'unrealizedPnl' => $this->parse_number($unrealisedPnl),
            'contracts' => $this->parse_number(Precise::string_abs($size)),
            'contractSize' => $this->safe_value($market, 'contractSize'),
            'realizedPnl' => $this->safe_number_2($position, 'realisedPnl', 'pnl'),
            'marginRatio' => null,
            'liquidationPrice' => $this->safe_number($position, 'liquidationPrice'),
            'markPrice' => $this->safe_number($position, 'markPrice'),
            'lastPrice' => null,
            'collateral' => $this->safe_number($position, 'maintMargin'),
            'marginMode' => $marginMode,
            'side' => $side,
            'percentage' => null,
            'stopLossPrice' => null,
            'takeProfitPrice' => null,
        ));
    }

    public function create_order(string $symbol, string $type, string $side, float $amount, ?float $price = null, $params = array ()) {
        /**
         * Create an order on the exchange
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/orders/place-order
         * @see https://www.kucoin.com/docs/rest/futures-trading/orders/place-take-profit-and-stop-loss-order#http-request
         *
         * @param {string} $symbol Unified CCXT $market $symbol
         * @param {string} $type 'limit' or 'market'
         * @param {string} $side 'buy' or 'sell'
         * @param {float} $amount the $amount of currency to trade
         * @param {float} [$price] the $price at which the order is to be fulfilled, in units of the quote currency, ignored in $market orders
         * @param {array} [$params]  extra parameters specific to the exchange API endpoint
         * @param {array} [$params->takeProfit] *takeProfit object in $params* containing the triggerPrice at which the attached take profit order will be triggered and the triggerPriceType
         * @param {array} [$params->stopLoss] *stopLoss object in $params* containing the triggerPrice at which the attached stop loss order will be triggered and the triggerPriceType
         * @param {float} [$params->triggerPrice] The $price a trigger order is triggered at
         * @param {float} [$params->stopLossPrice] $price to trigger stop-loss orders
         * @param {float} [$params->takeProfitPrice] $price to trigger take-profit orders
         * @param {bool} [$params->reduceOnly] A mark to reduce the position size only. Set to false by default. Need to set the position size when reduceOnly is true.
         * @param {string} [$params->timeInForce] GTC, GTT, IOC, or FOK, default is GTC, limit orders only
         * @param {string} [$params->postOnly] Post only flag, invalid when timeInForce is IOC or FOK
         * @param {float} [$params->cost] the cost of the order in units of USDT
         * @param {string} [$params->marginMode] 'cross' or 'isolated', default is 'isolated'
         * ----------------- Exchange Specific Parameters -----------------
         * @param {float} [$params->leverage] Leverage size of the order (mandatory param in request, default is 1)
         * @param {string} [$params->clientOid] client order id, defaults to uuid if not passed
         * @param {string} [$params->remark] remark for the order, length cannot exceed 100 utf8 characters
         * @param {string} [$params->stop] 'up' or 'down', the direction the triggerPrice is triggered from, requires triggerPrice. down => Triggers when the $price reaches or goes below the triggerPrice. up => Triggers when the $price reaches or goes above the triggerPrice.
         * @param {string} [$params->triggerPriceType] "last", "mark", "index" - defaults to "mark"
         * @param {string} [$params->stopPriceType] exchange-specific alternative for triggerPriceType => TP, IP or MP
         * @param {bool} [$params->closeOrder] set to true to close position
         * @param {bool} [$params->test] set to true to use the test order endpoint (does not submit order, use to validate $params)
         * @param {bool} [$params->forceHold] A mark to forcely hold the funds for an order, even though it's an order to reduce the position size. This helps the order stay on the order book and not get canceled when the position size changes. Set to false by default.
         * @return {array} an ~@link https://docs.ccxt.com/#/?id=order-structure order structure~
         */
        $this->load_markets();
        $market = $this->market($symbol);
        $testOrder = $this->safe_bool($params, 'test', false);
        $params = $this->omit($params, 'test');
        $isTpAndSlOrder = ($this->safe_value($params, 'stopLoss') !== null) || ($this->safe_value($params, 'takeProfit') !== null);
        $orderRequest = $this->create_contract_order_request($symbol, $type, $side, $amount, $price, $params);
        $response = null;
        if ($testOrder) {
            $response = $this->futuresPrivatePostOrdersTest ($orderRequest);
        } else {
            if ($isTpAndSlOrder) {
                $response = $this->futuresPrivatePostStOrders ($orderRequest);
            } else {
                $response = $this->futuresPrivatePostOrders ($orderRequest);
            }
        }
        //
        //    {
        //        "code" => "200000",
        //        "data" => array(
        //            "orderId" => "619717484f1d010001510cde",
        //        ),
        //    }
        //
        $data = $this->safe_dict($response, 'data', array());
        return $this->parse_order($data, $market);
    }

    public function create_orders(array $orders, $params = array ()) {
        /**
         * create a list of trade $orders
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/orders/place-multiple-$orders
         *
         * @param {Array} $orders list of $orders to create, each object should contain the parameters required by createOrder, namely $symbol, $type, $side, $amount, $price and $params
         * @param {array} [$params]  extra parameters specific to the exchange API endpoint
         * @return {array} an ~@link https://docs.ccxt.com/#/?id=order-structure order structure~
         */
        $this->load_markets();
        $ordersRequests = array();
        for ($i = 0; $i < count($orders); $i++) {
            $rawOrder = $orders[$i];
            $symbol = $this->safe_string($rawOrder, 'symbol');
            $market = $this->market($symbol);
            $type = $this->safe_string($rawOrder, 'type');
            $side = $this->safe_string($rawOrder, 'side');
            $amount = $this->safe_value($rawOrder, 'amount');
            $price = $this->safe_value($rawOrder, 'price');
            $orderParams = $this->safe_value($rawOrder, 'params', array());
            $orderRequest = $this->create_contract_order_request($market['id'], $type, $side, $amount, $price, $orderParams);
            $ordersRequests[] = $orderRequest;
        }
        $response = $this->futuresPrivatePostOrdersMulti ($ordersRequests);
        //
        //     {
        //         "code" => "200000",
        //         "data" => array(
        //             array(
        //                 "orderId" => "135241412609331200",
        //                 "clientOid" => "3d8fcc13-0b13-447f-ad30-4b3441e05213",
        //                 "symbol" => "LTCUSDTM",
        //                 "code" => "200000",
        //                 "msg" => "success"
        //             ),
        //             {
        //                 "orderId" => "135241412747743234",
        //                 "clientOid" => "b878c7ee-ae3e-4d63-a20b-038acbb7306f",
        //                 "symbol" => "LTCUSDTM",
        //                 "code" => "200000",
        //                 "msg" => "success"
        //             }
        //         )
        //     }
        //
        $data = $this->safe_list($response, 'data', array());
        return $this->parse_orders($data);
    }

    public function create_contract_order_request(string $symbol, string $type, string $side, float $amount, ?float $price = null, $params = array ()) {
        $market = $this->market($symbol);
        // required param, cannot be used twice
        $clientOrderId = $this->safe_string_2($params, 'clientOid', 'clientOrderId', $this->uuid());
        $params = $this->omit($params, array( 'clientOid', 'clientOrderId' ));
        $request = array(
            'clientOid' => $clientOrderId,
            'side' => $side,
            'symbol' => $market['id'],
            'type' => $type, // limit or $market
            'leverage' => 1,
        );
        $marginModeUpper = $this->safe_string_upper($params, 'marginMode');
        if ($marginModeUpper !== null) {
            $params = $this->omit($params, 'marginMode');
            $request['marginMode'] = $marginModeUpper;
        }
        $cost = $this->safe_string($params, 'cost');
        $params = $this->omit($params, 'cost');
        if ($cost !== null) {
            $request['valueQty'] = $this->cost_to_precision($symbol, $cost);
        } else {
            if ($amount < 1) {
                throw new InvalidOrder($this->id . ' createOrder() minimum contract order $amount is 1');
            }
            $request['size'] = intval($this->amount_to_precision($symbol, $amount));
        }
        list($triggerPrice, $stopLossPrice, $takeProfitPrice) = $this->handle_trigger_prices($params);
        $stopLoss = $this->safe_dict($params, 'stopLoss');
        $takeProfit = $this->safe_dict($params, 'takeProfit');
        // $isTpAndSl = $stopLossPrice && $takeProfitPrice;
        $triggerPriceTypes = array(
            'mark' => 'MP',
            'last' => 'TP',
            'index' => 'IP',
        );
        $triggerPriceType = $this->safe_string($params, 'triggerPriceType', 'mark');
        $triggerPriceTypeValue = $this->safe_string($triggerPriceTypes, $triggerPriceType, $triggerPriceType);
        $params = $this->omit($params, array( 'stopLossPrice', 'takeProfitPrice', 'triggerPrice', 'stopPrice', 'takeProfit', 'stopLoss' ));
        if ($triggerPrice) {
            $request['stop'] = ($side === 'buy') ? 'up' : 'down';
            $request['stopPrice'] = $this->price_to_precision($symbol, $triggerPrice);
            $request['stopPriceType'] = $triggerPriceTypeValue;
        } elseif ($stopLoss !== null || $takeProfit !== null) {
            $priceType = $triggerPriceTypeValue;
            if ($stopLoss !== null) {
                $slPrice = $this->safe_string_2($stopLoss, 'triggerPrice', 'stopPrice');
                $request['triggerStopDownPrice'] = $this->price_to_precision($symbol, $slPrice);
                $priceType = $this->safe_string($stopLoss, 'triggerPriceType', 'mark');
                $priceType = $this->safe_string($triggerPriceTypes, $priceType, $priceType);
            }
            if ($takeProfit !== null) {
                $tpPrice = $this->safe_string_2($takeProfit, 'triggerPrice', 'takeProfitPrice');
                $request['triggerStopUpPrice'] = $this->price_to_precision($symbol, $tpPrice);
                $priceType = $this->safe_string($takeProfit, 'triggerPriceType', 'mark');
                $priceType = $this->safe_string($triggerPriceTypes, $priceType, $priceType);
            }
            $request['stopPriceType'] = $priceType;
        } elseif ($stopLossPrice || $takeProfitPrice) {
            if ($stopLossPrice) {
                $request['stop'] = ($side === 'buy') ? 'up' : 'down';
                $request['stopPrice'] = $this->price_to_precision($symbol, $stopLossPrice);
            } else {
                $request['stop'] = ($side === 'buy') ? 'down' : 'up';
                $request['stopPrice'] = $this->price_to_precision($symbol, $takeProfitPrice);
            }
            $request['reduceOnly'] = true;
            $request['stopPriceType'] = $triggerPriceTypeValue;
        }
        $uppercaseType = strtoupper($type);
        $timeInForce = $this->safe_string_upper($params, 'timeInForce');
        if ($uppercaseType === 'LIMIT') {
            if ($price === null) {
                throw new ArgumentsRequired($this->id . ' createOrder() requires a $price argument for limit orders');
            } else {
                $request['price'] = $this->price_to_precision($symbol, $price);
            }
            if ($timeInForce !== null) {
                $request['timeInForce'] = $timeInForce;
            }
        }
        $postOnly = null;
        list($postOnly, $params) = $this->handle_post_only($type === 'market', false, $params);
        if ($postOnly) {
            $request['postOnly'] = true;
        }
        $hidden = $this->safe_value($params, 'hidden');
        if ($postOnly && ($hidden !== null)) {
            throw new BadRequest($this->id . ' createOrder() does not support the $postOnly parameter together with a $hidden parameter');
        }
        $iceberg = $this->safe_value($params, 'iceberg');
        if ($iceberg) {
            $visibleSize = $this->safe_value($params, 'visibleSize');
            if ($visibleSize === null) {
                throw new ArgumentsRequired($this->id . ' createOrder() requires a $visibleSize parameter for $iceberg orders');
            }
        }
        $params = $this->omit($params, array( 'timeInForce', 'stopPrice', 'triggerPrice', 'stopLossPrice', 'takeProfitPrice' )); // Time in force only valid for limit orders, exchange error when gtc for $market orders
        return $this->extend($request, $params);
    }

    public function cancel_order(string $id, ?string $symbol = null, $params = array ()) {
        /**
         * cancels an open order
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/orders/cancel-futures-order-by-orderid
         *
         * @param {string} $id order $id
         * @param {string} $symbol unified $symbol of the $market the order was made in
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @param {string} [$params->clientOrderId] cancel order by client order $id
         * @return {array} An ~@link https://docs.ccxt.com/#/?$id=order-structure order structure~
         */
        $this->load_markets();
        $clientOrderId = $this->safe_string_2($params, 'clientOid', 'clientOrderId');
        $params = $this->omit($params, array( 'clientOrderId' ));
        $request = array();
        $response = null;
        if ($clientOrderId !== null) {
            if ($symbol === null) {
                throw new ArgumentsRequired($this->id . ' cancelOrder() requires a $symbol argument when cancelling by clientOrderId');
            }
            $market = $this->market($symbol);
            $request['symbol'] = $market['id'];
            $request['clientOid'] = $clientOrderId;
            $response = $this->futuresPrivateDeleteOrdersClientOrderClientOid ($this->extend($request, $params));
        } else {
            $request['orderId'] = $id;
            $response = $this->futuresPrivateDeleteOrdersOrderId ($this->extend($request, $params));
        }
        //
        //   {
        //       "code" => "200000",
        //       "data" => array(
        //           "cancelledOrderIds" => array(
        //                "619714b8b6353000014c505a",
        //           ),
        //       ),
        //   }
        //
        return $this->safe_order(array( 'info' => $response ));
    }

    public function cancel_orders($ids, ?string $symbol = null, $params = array ()) {
        /**
         * cancel multiple $orders
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/orders/batch-cancel-$orders
         *
         * @param {string[]} $ids order $ids
         * @param {string} $symbol unified $symbol of the $market the order was made in
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @param {string[]} [$params->clientOrderIds] client order $ids
         * @return {array} an list of ~@link https://docs.ccxt.com/#/?id=order-structure order structures~
         */
        $this->load_markets();
        $market = null;
        if ($symbol !== null) {
            $market = $this->market($symbol);
        }
        $ordersRequests = array();
        $clientOrderIds = $this->safe_list_2($params, 'clientOrderIds', 'clientOids', array());
        $params = $this->omit($params, array( 'clientOrderIds', 'clientOids' ));
        $useClientorderId = false;
        for ($i = 0; $i < count($clientOrderIds); $i++) {
            $useClientorderId = true;
            if ($symbol === null) {
                throw new ArgumentsRequired($this->id . ' cancelOrders() requires a $symbol argument when cancelling by clientOrderIds');
            }
            $ordersRequests[] = array(
                'symbol' => $market['id'],
                'clientOid' => $this->safe_string($clientOrderIds, $i),
            );
        }
        for ($i = 0; $i < count($ids); $i++) {
            $ordersRequests[] = $ids[$i];
        }
        $requestKey = $useClientorderId ? 'clientOidsList' : 'orderIdsList';
        $request = array();
        $request[$requestKey] = $ordersRequests;
        $response = $this->futuresPrivateDeleteOrdersMultiCancel ($this->extend($request, $params));
        //
        //   {
        //       "code" => "200000",
        //       "data":
        //       array(
        //           array(
        //               "orderId" => "80465574458560512",
        //               "clientOid" => null,
        //               "code" => "200",
        //               "msg" => "success"
        //           ),
        //           {
        //               "orderId" => "80465575289094144",
        //               "clientOid" => null,
        //               "code" => "200",
        //               "msg" => "success"
        //           }
        //       )
        //   }
        //
        $orders = $this->safe_list($response, 'data', array());
        return $this->parse_orders($orders, $market);
    }

    public function cancel_all_orders(?string $symbol = null, $params = array ()) {
        /**
         * cancel all open orders
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/orders/cancel-multiple-futures-limit-orders
         * @see https://www.kucoin.com/docs/rest/futures-trading/orders/cancel-multiple-futures-stop-orders
         *
         * @param {string} $symbol unified market $symbol, only orders in the market of this $symbol are cancelled when $symbol is not null
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @param {array} [$params->trigger] When true, all the $trigger orders will be cancelled
         * @return Response from the exchange
         */
        $this->load_markets();
        $request = array();
        if ($symbol !== null) {
            $request['symbol'] = $this->market_id($symbol);
        }
        $trigger = $this->safe_value_2($params, 'stop', 'trigger');
        $params = $this->omit($params, array( 'stop', 'trigger' ));
        $response = null;
        if ($trigger) {
            $response = $this->futuresPrivateDeleteStopOrders ($this->extend($request, $params));
        } else {
            $response = $this->futuresPrivateDeleteOrders ($this->extend($request, $params));
        }
        //
        //   {
        //       "code" => "200000",
        //       "data" => array(
        //           "cancelledOrderIds" => array(
        //                "619714b8b6353000014c505a",
        //           ),
        //       ),
        //   }
        //
        $data = $this->safe_dict($response, 'data');
        return array( $this->safe_order(array( 'info' => $data )) );
    }

    public function add_margin(string $symbol, float $amount, $params = array ()): array {
        /**
         * add margin
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/positions/add-margin-manually
         *
         * @param {string} $symbol unified $market $symbol
         * @param {float} $amount amount of margin to add
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} a ~@link https://docs.ccxt.com/#/?id=add-margin-structure margin structure~
         */
        $this->load_markets();
        $market = $this->market($symbol);
        $uuid = $this->uuid();
        $request = array(
            'symbol' => $market['id'],
            'margin' => $this->amount_to_precision($symbol, $amount),
            'bizNo' => $uuid,
        );
        $response = $this->futuresPrivatePostPositionMarginDepositMargin ($this->extend($request, $params));
        //
        //    {
        //        "code" => "200000",
        //        "data" => {
        //            "id" => "62311d26064e8f00013f2c6d",
        //            "symbol" => "XRPUSDTM",
        //            "autoDeposit" => false,
        //            "maintMarginReq" => 0.01,
        //            "riskLimit" => 200000,
        //            "realLeverage" => 0.88,
        //            "crossMode" => false,
        //            "delevPercentage" => 0.4,
        //            "openingTimestamp" => 1647385894798,
        //            "currentTimestamp" => 1647414510672,
        //            "currentQty" => -1,
        //            "currentCost" => -7.658,
        //            "currentComm" => 0.0053561,
        //            "unrealisedCost" => -7.658,
        //            "realisedGrossCost" => 0,
        //            "realisedCost" => 0.0053561,
        //            "isOpen" => true,
        //            "markPrice" => 0.7635,
        //            "markValue" => -7.635,
        //            "posCost" => -7.658,
        //            "posCross" => 1.00016084,
        //            "posInit" => 7.658,
        //            "posComm" => 0.00979006,
        //            "posLoss" => 0,
        //            "posMargin" => 8.6679509,
        //            "posMaint" => 0.********,
        //            "maintMargin" => 8.6909509,
        //            "realisedGrossPnl" => 0,
        //            "realisedPnl" => -0.0038335,
        //            "unrealisedPnl" => 0.023,
        //            "unrealisedPnlPcnt" => 0.003,
        //            "unrealisedRoePcnt" => 0.003,
        //            "avgEntryPrice" => 0.7658,
        //            "liquidationPrice" => 1.6239,
        //            "bankruptPrice" => 1.6317,
        //            "settleCurrency" => "USDT"
        //        }
        //    }
        //
        //
        //    {
        //        "code":"200000",
        //        "msg":"Position does not exist"
        //    }
        //
        $data = $this->safe_value($response, 'data');
        return $this->extend($this->parse_margin_modification($data, $market), array(
            'amount' => $this->amount_to_precision($symbol, $amount),
            'direction' => 'in',
        ));
    }

    public function parse_margin_modification($info, ?array $market = null): array {
        //
        //    {
        //        "id" => "62311d26064e8f00013f2c6d",
        //        "symbol" => "XRPUSDTM",
        //        "autoDeposit" => false,
        //        "maintMarginReq" => 0.01,
        //        "riskLimit" => 200000,
        //        "realLeverage" => 0.88,
        //        "crossMode" => false,
        //        "delevPercentage" => 0.4,
        //        "openingTimestamp" => 1647385894798,
        //        "currentTimestamp" => 1647414510672,
        //        "currentQty" => -1,
        //        "currentCost" => -7.658,
        //        "currentComm" => 0.0053561,
        //        "unrealisedCost" => -7.658,
        //        "realisedGrossCost" => 0,
        //        "realisedCost" => 0.0053561,
        //        "isOpen" => true,
        //        "markPrice" => 0.7635,
        //        "markValue" => -7.635,
        //        "posCost" => -7.658,
        //        "posCross" => 1.00016084,
        //        "posInit" => 7.658,
        //        "posComm" => 0.00979006,
        //        "posLoss" => 0,
        //        "posMargin" => 8.6679509,
        //        "posMaint" => 0.********,
        //        "maintMargin" => 8.6909509,
        //        "realisedGrossPnl" => 0,
        //        "realisedPnl" => -0.0038335,
        //        "unrealisedPnl" => 0.023,
        //        "unrealisedPnlPcnt" => 0.003,
        //        "unrealisedRoePcnt" => 0.003,
        //        "avgEntryPrice" => 0.7658,
        //        "liquidationPrice" => 1.6239,
        //        "bankruptPrice" => 1.6317,
        //        "settleCurrency" => "USDT"
        //    }
        //
        //    {
        //        "code":"200000",
        //        "msg":"Position does not exist"
        //    }
        //
        $id = $this->safe_string($info, 'id');
        $market = $this->safe_market($id, $market);
        $currencyId = $this->safe_string($info, 'settleCurrency');
        $crossMode = $this->safe_value($info, 'crossMode');
        $mode = $crossMode ? 'cross' : 'isolated';
        $marketId = $this->safe_string($market, 'symbol');
        $timestamp = $this->safe_integer($info, 'currentTimestamp');
        return array(
            'info' => $info,
            'symbol' => $this->safe_symbol($marketId, $market),
            'type' => null,
            'marginMode' => $mode,
            'amount' => null,
            'total' => null,
            'code' => $this->safe_currency_code($currencyId),
            'status' => null,
            'timestamp' => $timestamp,
            'datetime' => $this->iso8601($timestamp),
        );
    }

    public function fetch_orders_by_status($status, ?string $symbol = null, ?int $since = null, ?int $limit = null, $params = array ()) {
        /**
         * fetches a list of $orders placed on the exchange
         *
         * @see https://docs.kucoin.com/futures/#get-order-list
         * @see https://docs.kucoin.com/futures/#get-untriggered-stop-order-list
         *
         * @param {string} $status 'active' or 'closed', only 'active' is valid for stop $orders
         * @param {string} $symbol unified $symbol for the $market to retrieve $orders from
         * @param {int} [$since] timestamp in ms of the earliest order to retrieve
         * @param {int} [$limit] The maximum number of $orders to retrieve
         * @param {array} [$params] exchange specific parameters
         * @param {bool} [$params->trigger] set to true to retrieve untriggered stop $orders
         * @param {int} [$params->until] End time in ms
         * @param {string} [$params->side] buy or sell
         * @param {string} [$params->type] $limit or $market
         * @param {boolean} [$params->paginate] default false, when true will automatically $paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-$params)
         * @return An ~@link https://docs.ccxt.com/#/?id=order-structure array of order structures~
         */
        $this->load_markets();
        $paginate = false;
        list($paginate, $params) = $this->handle_option_and_params($params, 'fetchOrdersByStatus', 'paginate');
        if ($paginate) {
            return $this->fetch_paginated_call_dynamic('fetchOrdersByStatus', $symbol, $since, $limit, $params);
        }
        $trigger = $this->safe_bool_2($params, 'stop', 'trigger');
        $until = $this->safe_integer($params, 'until');
        $params = $this->omit($params, array( 'stop', 'until', 'trigger' ));
        if ($status === 'closed') {
            $status = 'done';
        } elseif ($status === 'open') {
            $status = 'active';
        }
        $request = array();
        if (!$trigger) {
            $request['status'] = $status;
        } elseif ($status !== 'active') {
            throw new BadRequest($this->id . ' fetchOrdersByStatus() can only fetch untriggered stop orders');
        }
        $market = null;
        if ($symbol !== null) {
            $market = $this->market($symbol);
            $request['symbol'] = $market['id'];
        }
        if ($since !== null) {
            $request['startAt'] = $since;
        }
        if ($until !== null) {
            $request['endAt'] = $until;
        }
        $response = null;
        if ($trigger) {
            $response = $this->futuresPrivateGetStopOrders ($this->extend($request, $params));
        } else {
            $response = $this->futuresPrivateGetOrders ($this->extend($request, $params));
        }
        //
        //     {
        //         "code" => "200000",
        //         "data" => {
        //             "currentPage" => 1,
        //             "pageSize" => 50,
        //             "totalNum" => 4,
        //             "totalPage" => 1,
        //             "items" => array(
        //                 {
        //                     "id" => "64507d02921f1c0001ff6892",
        //                     "symbol" => "XBTUSDTM",
        //                     "type" => "market",
        //                     "side" => "buy",
        //                     "price" => null,
        //                     "size" => 1,
        //                     "value" => "27.992",
        //                     "dealValue" => "27.992",
        //                     "dealSize" => 1,
        //                     "stp" => "",
        //                     "stop" => "",
        //                     "stopPriceType" => "",
        //                     "stopTriggered" => false,
        //                     "stopPrice" => null,
        //                     "timeInForce" => "GTC",
        //                     "postOnly" => false,
        //                     "hidden" => false,
        //                     "iceberg" => false,
        //                     "leverage" => "17",
        //                     "forceHold" => false,
        //                     "closeOrder" => false,
        //                     "visibleSize" => null,
        //                     "clientOid" => null,
        //                     "remark" => null,
        //                     "tags" => null,
        //                     "isActive" => false,
        //                     "cancelExist" => false,
        //                     "createdAt" => 1682996482000,
        //                     "updatedAt" => 1682996483062,
        //                     "endAt" => 1682996483062,
        //                     "orderTime" => 1682996482953900677,
        //                     "settleCurrency" => "USDT",
        //                     "status" => "done",
        //                     "filledValue" => "27.992",
        //                     "filledSize" => 1,
        //                     "reduceOnly" => false
        //                 }
        //             )
        //         }
        //     }
        //
        $responseData = $this->safe_dict($response, 'data', array());
        $orders = $this->safe_list($responseData, 'items', array());
        return $this->parse_orders($orders, $market, $since, $limit);
    }

    public function fetch_closed_orders(?string $symbol = null, ?int $since = null, ?int $limit = null, $params = array ()): array {
        /**
         * fetches information on multiple closed orders made by the user
         *
         * @see https://docs.kucoin.com/futures/#get-order-list
         *
         * @param {string} $symbol unified market $symbol of the market orders were made in
         * @param {int} [$since] the earliest time in ms to fetch orders for
         * @param {int} [$limit] the maximum number of order structures to retrieve
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @param {int} [$params->until] end time in ms
         * @param {string} [$params->side] buy or sell
         * @param {string} [$params->type] $limit, or market
         * @param {boolean} [$params->paginate] default false, when true will automatically $paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-$params)
         * @return {Order[]} a list of ~@link https://docs.ccxt.com/#/?id=order-structure order structures~
         */
        $this->load_markets();
        $paginate = false;
        list($paginate, $params) = $this->handle_option_and_params($params, 'fetchClosedOrders', 'paginate');
        if ($paginate) {
            return $this->fetch_paginated_call_dynamic('fetchClosedOrders', $symbol, $since, $limit, $params);
        }
        return $this->fetch_orders_by_status('done', $symbol, $since, $limit, $params);
    }

    public function fetch_open_orders(?string $symbol = null, ?int $since = null, ?int $limit = null, $params = array ()): array {
        /**
         * fetches information on multiple open orders made by the user
         *
         * @see https://docs.kucoin.com/futures/#get-order-list
         * @see https://docs.kucoin.com/futures/#get-untriggered-stop-order-list
         *
         * @param {string} $symbol unified market $symbol of the market orders were made in
         * @param {int} [$since] the earliest time in ms to fetch orders for
         * @param {int} [$limit] the maximum number of order structures to retrieve
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @param {int} [$params->until] end time in ms
         * @param {string} [$params->side] buy or sell
         * @param {string} [$params->type] $limit, or market
         * @param {boolean} [$params->trigger] set to true to retrieve untriggered stop orders
         * @param {boolean} [$params->paginate] default false, when true will automatically $paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-$params)
         * @return {Order[]} a list of ~@link https://docs.ccxt.com/#/?id=order-structure order structures~
         */
        $this->load_markets();
        $paginate = false;
        list($paginate, $params) = $this->handle_option_and_params($params, 'fetchOpenOrders', 'paginate');
        if ($paginate) {
            return $this->fetch_paginated_call_dynamic('fetchOpenOrders', $symbol, $since, $limit, $params);
        }
        return $this->fetch_orders_by_status('open', $symbol, $since, $limit, $params);
    }

    public function fetch_order(?string $id, ?string $symbol = null, $params = array ()) {
        /**
         * fetches information on an order made by the user
         *
         * @see https://docs.kucoin.com/futures/#get-details-of-a-single-order
         *
         * @param {string} $id order $id
         * @param {string} $symbol unified $symbol of the $market the order was made in
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} An ~@link https://docs.ccxt.com/#/?$id=order-structure order structure~
         */
        $this->load_markets();
        $request = array();
        $response = null;
        if ($id === null) {
            $clientOrderId = $this->safe_string_2($params, 'clientOid', 'clientOrderId');
            if ($clientOrderId === null) {
                throw new InvalidOrder($this->id . ' fetchOrder() requires parameter $id or $params->clientOid');
            }
            $request['clientOid'] = $clientOrderId;
            $params = $this->omit($params, array( 'clientOid', 'clientOrderId' ));
            $response = $this->futuresPrivateGetOrdersByClientOid ($this->extend($request, $params));
        } else {
            $request['orderId'] = $id;
            $response = $this->futuresPrivateGetOrdersOrderId ($this->extend($request, $params));
        }
        //
        //     {
        //         "code" => "200000",
        //         "data" => {
        //             "id" => "64507d02921f1c0001ff6892",
        //             "symbol" => "XBTUSDTM",
        //             "type" => "market",
        //             "side" => "buy",
        //             "price" => null,
        //             "size" => 1,
        //             "value" => "27.992",
        //             "dealValue" => "27.992",
        //             "dealSize" => 1,
        //             "stp" => "",
        //             "stop" => "",
        //             "stopPriceType" => "",
        //             "stopTriggered" => false,
        //             "stopPrice" => null,
        //             "timeInForce" => "GTC",
        //             "postOnly" => false,
        //             "hidden" => false,
        //             "iceberg" => false,
        //             "leverage" => "17",
        //             "forceHold" => false,
        //             "closeOrder" => false,
        //             "visibleSize" => null,
        //             "clientOid" => null,
        //             "remark" => null,
        //             "tags" => null,
        //             "isActive" => false,
        //             "cancelExist" => false,
        //             "createdAt" => 1682996482000,
        //             "updatedAt" => 1682996483000,
        //             "endAt" => 1682996483000,
        //             "orderTime" => 1682996482953900677,
        //             "settleCurrency" => "USDT",
        //             "status" => "done",
        //             "filledSize" => 1,
        //             "filledValue" => "27.992",
        //             "reduceOnly" => false
        //         }
        //     }
        //
        $market = ($symbol !== null) ? $this->market($symbol) : null;
        $responseData = $this->safe_dict($response, 'data');
        return $this->parse_order($responseData, $market);
    }

    public function parse_order(array $order, ?array $market = null): array {
        //
        // fetchOrder, fetchOrdersByStatus
        //
        //     {
        //         "id" => "64507d02921f1c0001ff6892",
        //         "symbol" => "XBTUSDTM",
        //         "type" => "market",
        //         "side" => "buy",
        //         "price" => null,
        //         "size" => 1,
        //         "value" => "27.992",
        //         "dealValue" => "27.992",
        //         "dealSize" => 1,
        //         "stp" => "",
        //         "stop" => "",
        //         "stopPriceType" => "",
        //         "stopTriggered" => false,
        //         "stopPrice" => null,
        //         "timeInForce" => "GTC",
        //         "postOnly" => false,
        //         "hidden" => false,
        //         "iceberg" => false,
        //         "leverage" => "17",
        //         "forceHold" => false,
        //         "closeOrder" => false,
        //         "visibleSize" => null,
        //         "clientOid" => null,
        //         "remark" => null,
        //         "tags" => null,
        //         "isActive" => false,
        //         "cancelExist" => false,
        //         "createdAt" => 1682996482000,
        //         "updatedAt" => 1682996483062,
        //         "endAt" => 1682996483062,
        //         "orderTime" => 1682996482953900677,
        //         "settleCurrency" => "USDT",
        //         "status" => "done",
        //         "filledValue" => "27.992",
        //         "filledSize" => 1,
        //         "reduceOnly" => false
        //     }
        //
        // createOrder
        //
        //     {
        //         "orderId" => "619717484f1d010001510cde"
        //     }
        //
        // createOrders
        //
        //     {
        //         "orderId" => "80465574458560512",
        //         "clientOid" => "5c52e11203aa677f33e491",
        //         "symbol" => "ETHUSDTM",
        //         "code" => "200000",
        //         "msg" => "success"
        //     }
        //
        $marketId = $this->safe_string($order, 'symbol');
        $market = $this->safe_market($marketId, $market);
        $symbol = $market['symbol'];
        $orderId = $this->safe_string_2($order, 'id', 'orderId');
        $type = $this->safe_string($order, 'type');
        $timestamp = $this->safe_integer($order, 'createdAt');
        $datetime = $this->iso8601($timestamp);
        $price = $this->safe_string($order, 'price');
        // $price is zero for $market $order
        // omitZero is called in safeOrder2
        $side = $this->safe_string($order, 'side');
        $feeCurrencyId = $this->safe_string($order, 'feeCurrency');
        $feeCurrency = $this->safe_currency_code($feeCurrencyId);
        $feeCost = $this->safe_number($order, 'fee');
        $amount = $this->safe_string($order, 'size');
        $filled = $this->safe_string($order, 'filledSize');
        $cost = $this->safe_string($order, 'filledValue');
        $average = $this->safe_string($order, 'avgDealPrice');
        if (($average === null) && Precise::string_gt($filled, '0')) {
            $contractSize = $this->safe_string($market, 'contractSize');
            if ($market['linear']) {
                $average = Precise::string_div($cost, Precise::string_mul($contractSize, $filled));
            } else {
                $average = Precise::string_div(Precise::string_mul($contractSize, $filled), $cost);
            }
        }
        // precision reported by their api is 8 d.p.
        // $average = Precise::string_div($cost, Precise::string_mul($filled, $market['contractSize']));
        // bool
        $isActive = $this->safe_value($order, 'isActive');
        $cancelExist = $this->safe_bool($order, 'cancelExist', false);
        $status = null;
        if ($isActive !== null) {
            $status = $isActive ? 'open' : 'closed';
        }
        $status = $cancelExist ? 'canceled' : $status;
        $fee = null;
        if ($feeCost !== null) {
            $fee = array(
                'currency' => $feeCurrency,
                'cost' => $feeCost,
            );
        }
        $clientOrderId = $this->safe_string($order, 'clientOid');
        $timeInForce = $this->safe_string($order, 'timeInForce');
        $postOnly = $this->safe_value($order, 'postOnly');
        $reduceOnly = $this->safe_value($order, 'reduceOnly');
        $lastUpdateTimestamp = $this->safe_integer($order, 'updatedAt');
        return $this->safe_order(array(
            'id' => $orderId,
            'clientOrderId' => $clientOrderId,
            'symbol' => $symbol,
            'type' => $type,
            'timeInForce' => $timeInForce,
            'postOnly' => $postOnly,
            'reduceOnly' => $reduceOnly,
            'side' => $side,
            'amount' => $amount,
            'price' => $price,
            'triggerPrice' => $this->safe_number($order, 'stopPrice'),
            'cost' => $cost,
            'filled' => $filled,
            'remaining' => null,
            'timestamp' => $timestamp,
            'datetime' => $datetime,
            'fee' => $fee,
            'status' => $status,
            'info' => $order,
            'lastTradeTimestamp' => null,
            'lastUpdateTimestamp' => $lastUpdateTimestamp,
            'average' => $average,
            'trades' => null,
        ), $market);
    }

    public function fetch_funding_rate(string $symbol, $params = array ()): array {
        /**
         * fetch the current funding rate
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/funding-fees/get-current-funding-rate
         *
         * @param {string} $symbol unified $market $symbol
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} a ~@link https://docs.ccxt.com/#/?id=funding-rate-structure funding rate structure~
         */
        $this->load_markets();
        $market = $this->market($symbol);
        $request = array(
            'symbol' => $market['id'],
        );
        $response = $this->futuresPublicGetFundingRateSymbolCurrent ($this->extend($request, $params));
        //
        //    {
        //        "code" => "200000",
        //        "data" => array(
        //            "symbol" => ".ETHUSDTMFPI8H",
        //            "granularity" => 28800000,
        //            "timePoint" => 1637380800000,
        //            "value" => 0.0001,
        //            "predictedValue" => 0.0001,
        //        ),
        //    }
        //
        $data = $this->safe_dict($response, 'data', array());
        // the website displayes the previous funding rate as "funding rate"
        return $this->parse_funding_rate($data, $market);
    }

    public function fetch_funding_interval(string $symbol, $params = array ()): array {
        /**
         * fetch the current funding rate interval
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/funding-fees/get-current-funding-rate
         *
         * @param {string} $symbol unified market $symbol
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} a ~@link https://docs.ccxt.com/#/?id=funding-rate-structure funding rate structure~
         */
        return $this->fetch_funding_rate($symbol, $params);
    }

    public function parse_funding_rate($data, ?array $market = null): array {
        //
        //     {
        //         "symbol" => ".ETHUSDTMFPI8H",
        //         "granularity" => 28800000,
        //         "timePoint" => 1637380800000,
        //         "value" => 0.0001,
        //         "predictedValue" => 0.0001,
        //     }
        //
        $fundingTimestamp = $this->safe_integer($data, 'timePoint');
        $marketId = $this->safe_string($data, 'symbol');
        return array(
            'info' => $data,
            'symbol' => $this->safe_symbol($marketId, $market, null, 'contract'),
            'markPrice' => null,
            'indexPrice' => null,
            'interestRate' => null,
            'estimatedSettlePrice' => null,
            'timestamp' => null,
            'datetime' => null,
            'fundingRate' => $this->safe_number($data, 'value'),
            'fundingTimestamp' => $fundingTimestamp,
            'fundingDatetime' => $this->iso8601($fundingTimestamp),
            'nextFundingRate' => $this->safe_number($data, 'predictedValue'),
            'nextFundingTimestamp' => null,
            'nextFundingDatetime' => null,
            'previousFundingRate' => null,
            'previousFundingTimestamp' => null,
            'previousFundingDatetime' => null,
            'interval' => $this->parse_funding_interval($this->safe_string($data, 'granularity')),
        );
    }

    public function parse_funding_interval($interval) {
        $intervals = array(
            '3600000' => '1h',
            '14400000' => '4h',
            '28800000' => '8h',
            '********' => '16h',
            '********' => '24h',
        );
        return $this->safe_string($intervals, $interval, $interval);
    }

    public function parse_balance($response): array {
        $result = array(
            'info' => $response,
            'timestamp' => null,
            'datetime' => null,
        );
        $data = $this->safe_value($response, 'data');
        $currencyId = $this->safe_string($data, 'currency');
        $code = $this->safe_currency_code($currencyId);
        $account = $this->account();
        $account['free'] = $this->safe_string($data, 'availableBalance');
        $account['total'] = $this->safe_string($data, 'accountEquity');
        $result[$code] = $account;
        return $this->safe_balance($result);
    }

    public function fetch_balance($params = array ()): array {
        /**
         * query for balance and get the amount of funds available for trading or funds locked in orders
         *
         * @see https://www.kucoin.com/docs/rest/funding/funding-overview/get-account-detail-futures
         *
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @param {array} [$params->code] the unified $currency $code to fetch the balance for, if not provided, the default .options['fetchBalance']['code'] will be used
         * @return {array} a ~@link https://docs.ccxt.com/#/?id=balance-structure balance structure~
         */
        $this->load_markets();
        // only fetches one balance at a time
        $defaultCode = $this->safe_string($this->options, 'code');
        $fetchBalanceOptions = $this->safe_value($this->options, 'fetchBalance', array());
        $defaultCode = $this->safe_string($fetchBalanceOptions, 'code', $defaultCode);
        $code = $this->safe_string($params, 'code', $defaultCode);
        $currency = $this->currency($code);
        $request = array(
            'currency' => $currency['id'],
        );
        $response = $this->futuresPrivateGetAccountOverview ($this->extend($request, $params));
        //
        //     {
        //         "code" => "200000",
        //         "data" => {
        //             "accountEquity" => 0.00005,
        //             "unrealisedPNL" => 0,
        //             "marginBalance" => 0.00005,
        //             "positionMargin" => 0,
        //             "orderMargin" => 0,
        //             "frozenFunds" => 0,
        //             "availableBalance" => 0.00005,
        //             "currency" => "XBT"
        //         }
        //     }
        //
        return $this->parse_balance($response);
    }

    public function transfer(string $code, float $amount, string $fromAccount, string $toAccount, $params = array ()): array {
        /**
         * transfer $currency internally between wallets on the same account
         *
         * @see https://www.kucoin.com/docs/rest/funding/transfer/transfer-to-main-or-trade-account
         * @see https://www.kucoin.com/docs/rest/funding/transfer/transfer-to-futures-account
         *
         * @param {string} $code unified $currency $code
         * @param {float} $amount amount to transfer
         * @param {string} $fromAccount account to transfer from
         * @param {string} $toAccount account to transfer to
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} a ~@link https://docs.ccxt.com/#/?id=transfer-structure transfer structure~
         */
        $this->load_markets();
        $currency = $this->currency($code);
        $amountToPrecision = $this->currency_to_precision($code, $amount);
        $request = array(
            'currency' => $this->safe_string($currency, 'id'),
            'amount' => $amountToPrecision,
        );
        $toAccountString = $this->parse_transfer_type($toAccount);
        $response = null;
        if ($toAccountString === 'TRADE' || $toAccountString === 'MAIN') {
            $request['recAccountType'] = $toAccountString;
            $response = $this->futuresPrivatePostTransferOut ($this->extend($request, $params));
            //
            //     {
            //         "code" => "200000",
            //         "data" => {
            //             "applyId" => "6738754373ceee00011ec3f8",
            //             "bizNo" => "6738754373ceee00011ec3f7",
            //             "payAccountType" => "CONTRACT",
            //             "payTag" => "DEFAULT",
            //             "remark" => "",
            //             "recAccountType" => "MAIN",
            //             "recTag" => "DEFAULT",
            //             "recRemark" => "",
            //             "recSystem" => "KUCOIN",
            //             "status" => "PROCESSING",
            //             "currency" => "USDT",
            //             "amount" => "5",
            //             "fee" => "0",
            //             "sn" => ****************,
            //             "reason" => "",
            //             "createdAt" => *************,
            //             "updatedAt" => *************
            //         }
            //     }
            //
        } elseif ($toAccount === 'future' || $toAccount === 'swap' || $toAccount === 'contract') {
            $request['payAccountType'] = $this->parse_transfer_type($fromAccount);
            $response = $this->futuresPrivatePostTransferIn ($this->extend($request, $params));
            //
            //    {
            //        "code" => "200000",
            //        "data" => {
            //            "applyId" => "5bffb63303aa675e8bbe18f9" // Transfer-out $request ID
            //        }
            //    }
            //
        } else {
            throw new BadRequest($this->id . ' transfer() only supports transfers between future/swap, spot and funding accounts');
        }
        $data = $this->safe_dict($response, 'data', array());
        return $this->extend($this->parse_transfer($data, $currency), array(
            'amount' => $this->parse_number($amountToPrecision),
            'fromAccount' => $fromAccount,
            'toAccount' => $toAccount,
        ));
    }

    public function parse_transfer(array $transfer, ?array $currency = null): array {
        //
        // $transfer to spot or funding account
        //
        //     {
        //            "applyId" => "5bffb63303aa675e8bbe18f9" // Transfer-out request ID
        //     }
        //
        // $transfer to future account
        //
        //     {
        //         "applyId" => "6738754373ceee00011ec3f8",
        //         "bizNo" => "6738754373ceee00011ec3f7",
        //         "payAccountType" => "CONTRACT",
        //         "payTag" => "DEFAULT",
        //         "remark" => "",
        //         "recAccountType" => "MAIN",
        //         "recTag" => "DEFAULT",
        //         "recRemark" => "",
        //         "recSystem" => "KUCOIN",
        //         "status" => "PROCESSING",
        //         "currency" => "USDT",
        //         "amount" => "5",
        //         "fee" => "0",
        //         "sn" => ****************,
        //         "reason" => "",
        //         "createdAt" => *************,
        //         "updatedAt" => *************
        //     }
        //
        $timestamp = $this->safe_integer($transfer, 'updatedAt');
        return array(
            'id' => $this->safe_string($transfer, 'applyId'),
            'timestamp' => $timestamp,
            'datetime' => $this->iso8601($timestamp),
            'currency' => $this->safe_currency_code(null, $currency),
            'amount' => $this->safe_number($transfer, 'amount'),
            'fromAccount' => null,
            'toAccount' => null,
            'status' => $this->safe_string($transfer, 'status'),
            'info' => $transfer,
        );
    }

    public function parse_transfer_status(?string $status): ?string {
        $statuses = array(
            'PROCESSING' => 'pending',
        );
        return $this->safe_string($statuses, $status, $status);
    }

    public function parse_transfer_type(?string $transferType): ?string {
        $transferTypes = array(
            'spot' => 'TRADE',
            'funding' => 'MAIN',
        );
        return $this->safe_string_upper($transferTypes, $transferType, $transferType);
    }

    public function fetch_my_trades(?string $symbol = null, ?int $since = null, ?int $limit = null, $params = array ()) {
        /**
         *
         * @see https://docs.kucoin.com/futures/#get-fills
         *
         * fetch all $trades made by the user
         * @param {string} $symbol unified $market $symbol
         * @param {int} [$since] the earliest time in ms to fetch $trades for
         * @param {int} [$limit] the maximum number of $trades structures to retrieve
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @param {int} [$params->until] End time in ms
         * @param {boolean} [$params->paginate] default false, when true will automatically $paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-$params)
         * @return {Trade[]} a list of ~@link https://docs.ccxt.com/#/?id=trade-structure trade structures~
         */
        $this->load_markets();
        $paginate = false;
        list($paginate, $params) = $this->handle_option_and_params($params, 'fetchMyTrades', 'paginate');
        if ($paginate) {
            return $this->fetch_paginated_call_dynamic('fetchMyTrades', $symbol, $since, $limit, $params);
        }
        $request = array(
            // orderId ('strval') [optional] Fills for a specific order (other parameters can be ignored if specified)
            // $symbol ('strval') [optional] Symbol of the contract
            // side ('strval') [optional] buy or sell
            // type ('strval') [optional] $limit, $market, limit_stop or market_stop
            // startAt (long) [optional] Start time (millisecond)
            // endAt (long) [optional] End time (millisecond)
        );
        $market = null;
        if ($symbol !== null) {
            $market = $this->market($symbol);
            $request['symbol'] = $market['id'];
        }
        if ($since !== null) {
            $request['startAt'] = $since;
        }
        if ($limit !== null) {
            $request['pageSize'] = min (1000, $limit);
        }
        list($request, $params) = $this->handle_until_option('endAt', $request, $params);
        $response = $this->futuresPrivateGetFills ($this->extend($request, $params));
        //
        //    {
        //        "code" => "200000",
        //        "data" => {
        //          "currentPage" => 1,
        //          "pageSize" => 1,
        //          "totalNum" => 251915,
        //          "totalPage" => 251915,
        //          "items" => array(
        //              {
        //                  "symbol" => "XBTUSDM",  // Ticker $symbol of the contract
        //                  "tradeId" => "5ce24c1f0c19fc3c58edc47c",  // Trade ID
        //                  "orderId" => "5ce24c16b210233c36ee321d",  // Order ID
        //                  "side" => "sell",  // Transaction side
        //                  "liquidity" => "taker",  // Liquidity- taker or maker
        //                  "price" => "8302",  // Filled price
        //                  "size" => 10,  // Filled amount
        //                  "value" => "0.001204529",  // Order value
        //                  "feeRate" => "0.0005",  // Floating fees
        //                  "fixFee" => "0.00000006",  // Fixed fees
        //                  "feeCurrency" => "XBT",  // Charging currency
        //                  "stop" => "",  // A mark to the stop order type
        //                  "fee" => "0.0000012022",  // Transaction fee
        //                  "orderType" => "limit",  // Order type
        //                  "tradeType" => "trade",  // Trade type (trade, liquidation, ADL or settlement)
        //                  "createdAt" => 1558334496000,  // Time the order created
        //                  "settleCurrency" => "XBT", // settlement currency
        //                  "tradeTime" => 1558334496********0 // trade time in nanosecond
        //              }
        //            )
        //        }
        //    }
        //
        $data = $this->safe_dict($response, 'data', array());
        $trades = $this->safe_list($data, 'items', array());
        return $this->parse_trades($trades, $market, $since, $limit);
    }

    public function fetch_trades(string $symbol, ?int $since = null, ?int $limit = null, $params = array ()): array {
        /**
         * get the list of most recent $trades for a particular $symbol
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/market-data/get-transaction-history
         *
         * @param {string} $symbol unified $symbol of the $market to fetch $trades for
         * @param {int} [$since] timestamp in ms of the earliest trade to fetch
         * @param {int} [$limit] the maximum amount of $trades to fetch
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {Trade[]} a list of ~@link https://docs.ccxt.com/#/?id=public-$trades trade structures~
         */
        $this->load_markets();
        $market = $this->market($symbol);
        $request = array(
            'symbol' => $market['id'],
        );
        $response = $this->futuresPublicGetTradeHistory ($this->extend($request, $params));
        //
        //      {
        //          "code" => "200000",
        //          "data" => array(
        //              {
        //                  "sequence" => 32114961,
        //                  "side" => "buy",
        //                  "size" => 39,
        //                  "price" => "4001.65********",
        //                  "takerOrderId" => "61c20742f172110001e0ebe4",
        //                  "makerOrderId" => "61c2073fcfc88100010fcb5d",
        //                  "tradeId" => "61c2074277a0c473e69029b8",
        //                  "ts" => 1640105794099993896   // filled time
        //              }
        //          )
        //      }
        //
        $trades = $this->safe_list($response, 'data', array());
        return $this->parse_trades($trades, $market, $since, $limit);
    }

    public function parse_trade(array $trade, ?array $market = null): array {
        //
        // fetchTrades (public)
        //
        //     {
        //         "sequence" => 32114961,
        //         "side" => "buy",
        //         "size" => 39,
        //         "price" => "4001.65********",
        //         "takerOrderId" => "61c20742f172110001e0ebe4",
        //         "makerOrderId" => "61c2073fcfc88100010fcb5d",
        //         "tradeId" => "61c2074277a0c473e69029b8",
        //         "ts" => 1640105794099993896   // filled time
        //     }
        //
        // fetchMyTrades (private) v2
        //
        //     {
        //         "symbol":"BTC-USDT",
        //         "tradeId":"5c35c02709e4f67d5266954e",
        //         "orderId":"5c35c02703aa673ceec2a168",
        //         "counterOrderId":"5c1ab46003aa676e487fa8e3",
        //         "side":"buy",
        //         "liquidity":"taker",
        //         "forceTaker":true,
        //         "price":"0.083",
        //         "size":"0.8424304",
        //         "funds":"0.0699217232",
        //         "fee":"0",
        //         "feeRate":"0",
        //         "feeCurrency":"USDT",
        //         "stop":"",
        //         "type":"limit",
        //         "createdAt":1547026472000
        //     }
        //
        // fetchMyTrades (private) v1
        //
        //    {
        //        "symbol":"DOGEUSDTM",
        //        "tradeId":"620ec41a96bab27b5f4ced56",
        //        "orderId":"620ec41a0d1d8a0001560bd0",
        //        "side":"sell",
        //        "liquidity":"taker",
        //        "forceTaker":true,
        //        "price":"0.13969",
        //        "size":1,
        //        "value":"13.969",
        //        "feeRate":"0.0006",
        //        "fixFee":"0",
        //        "feeCurrency":"USDT",
        //        "stop":"",
        //        "tradeTime":1645134874858018058,
        //        "fee":"0.0083814",
        //        "settleCurrency":"USDT",
        //        "orderType":"market",
        //        "tradeType":"trade",
        //        "createdAt":1645134874858
        //    }
        //
        // watchTrades
        //
        //    {
        //        "makerUserId" => "62286a4d720edf0001e81961",
        //        "symbol" => "ADAUSDTM",
        //        "sequence" => 41320766,
        //        "side" => "sell",
        //        "size" => 2,
        //        "price" => 0.35904,
        //        "takerOrderId" => "636dd9da9857ba00010cfa44",
        //        "makerOrderId" => "636dd9c8df149d0001e62bc8",
        //        "takerUserId" => "6180be22b6ab210001fa3371",
        //        "tradeId" => "636dd9da0000d400d477eca7",
        //        "ts" => 1668143578987357700
        //    }
        //
        $marketId = $this->safe_string($trade, 'symbol');
        $market = $this->safe_market($marketId, $market, '-');
        $id = $this->safe_string_2($trade, 'tradeId', 'id');
        $orderId = $this->safe_string($trade, 'orderId');
        $takerOrMaker = $this->safe_string($trade, 'liquidity');
        $timestamp = $this->safe_integer($trade, 'ts');
        if ($timestamp !== null) {
            $timestamp = $this->parse_to_int($timestamp / 1000000);
        } else {
            $timestamp = $this->safe_integer($trade, 'createdAt');
            // if it's a historical v1 $trade, the exchange returns $timestamp in seconds
            if ((is_array($trade) && array_key_exists('dealValue', $trade)) && ($timestamp !== null)) {
                $timestamp = $timestamp * 1000;
            }
        }
        $priceString = $this->safe_string_2($trade, 'price', 'dealPrice');
        $amountString = $this->safe_string_2($trade, 'size', 'amount');
        $side = $this->safe_string($trade, 'side');
        $fee = null;
        $feeCostString = $this->safe_string($trade, 'fee');
        if ($feeCostString !== null) {
            $feeCurrencyId = $this->safe_string($trade, 'feeCurrency');
            $feeCurrency = $this->safe_currency_code($feeCurrencyId);
            if ($feeCurrency === null) {
                $feeCurrency = ($side === 'sell') ? $market['quote'] : $market['base'];
            }
            $fee = array(
                'cost' => $feeCostString,
                'currency' => $feeCurrency,
                'rate' => $this->safe_string($trade, 'feeRate'),
            );
        }
        $type = $this->safe_string_2($trade, 'type', 'orderType');
        if ($type === 'match') {
            $type = null;
        }
        $costString = $this->safe_string_2($trade, 'funds', 'value');
        if ($costString === null) {
            $contractSize = $this->safe_string($market, 'contractSize');
            $contractCost = Precise::string_mul($priceString, $amountString);
            $costString = Precise::string_mul($contractCost, $contractSize);
        }
        return $this->safe_trade(array(
            'info' => $trade,
            'id' => $id,
            'order' => $orderId,
            'timestamp' => $timestamp,
            'datetime' => $this->iso8601($timestamp),
            'symbol' => $market['symbol'],
            'type' => $type,
            'takerOrMaker' => $takerOrMaker,
            'side' => $side,
            'price' => $priceString,
            'amount' => $amountString,
            'cost' => $costString,
            'fee' => $fee,
        ), $market);
    }

    public function fetch_deposits(?string $code = null, ?int $since = null, ?int $limit = null, $params = array ()): array {
        /**
         * fetch all deposits made to an account
         * @param {string} $code unified $currency $code
         * @param {int} [$since] the earliest time in ms to fetch deposits for
         * @param {int} [$limit] the maximum number of deposits structures to retrieve
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array[]} a list of ~@link https://docs.ccxt.com/#/?id=transaction-structure transaction structures~
         */
        $this->load_markets();
        $request = array();
        $currency = null;
        if ($code !== null) {
            $currency = $this->currency($code);
            $request['currency'] = $currency['id'];
        }
        if ($limit !== null) {
            $request['pageSize'] = $limit;
        }
        if ($since !== null) {
            $request['startAt'] = $since;
        }
        $response = $this->futuresPrivateGetDepositList ($this->extend($request, $params));
        //
        //     {
        //         "code" => "200000",
        //         "data" => {
        //             "currentPage" => 1,
        //             "pageSize" => 5,
        //             "totalNum" => 2,
        //             "totalPage" => 1,
        //             "items" => array(
        //                 array(
        //                     "address" => "******************************************",
        //                     "memo" => "5c247c8a03aa677cea2a251d",
        //                     "amount" => 1,
        //                     "fee" => 0.0001,
        //                     "currency" => "KCS",
        //                     "isInner" => false,
        //                     "walletTxId" => "5bbb57386d99522d9f954c5a@test004",
        //                     "status" => "SUCCESS",
        //                     "createdAt" => 1544178843000,
        //                     "updatedAt" => 1544178891000
        //                     "remark":"foobar"
        //                 ),
        //                 ...
        //             )
        //         }
        //     }
        //
        $responseData = $response['data']['items'];
        return $this->parse_transactions($responseData, $currency, $since, $limit, array( 'type' => 'deposit' ));
    }

    public function fetch_withdrawals(?string $code = null, ?int $since = null, ?int $limit = null, $params = array ()): array {
        /**
         * fetch all withdrawals made from an account
         * @param {string} $code unified $currency $code
         * @param {int} [$since] the earliest time in ms to fetch withdrawals for
         * @param {int} [$limit] the maximum number of withdrawals structures to retrieve
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array[]} a list of ~@link https://docs.ccxt.com/#/?id=transaction-structure transaction structures~
         */
        $this->load_markets();
        $request = array();
        $currency = null;
        if ($code !== null) {
            $currency = $this->currency($code);
            $request['currency'] = $currency['id'];
        }
        if ($limit !== null) {
            $request['pageSize'] = $limit;
        }
        if ($since !== null) {
            $request['startAt'] = $since;
        }
        $response = $this->futuresPrivateGetWithdrawalList ($this->extend($request, $params));
        //
        //     {
        //         "code" => "200000",
        //         "data" => {
        //             "currentPage" => 1,
        //             "pageSize" => 5,
        //             "totalNum" => 2,
        //             "totalPage" => 1,
        //             "items" => array(
        //                 array(
        //                     "id" => "5c2dc64e03aa675aa263f1ac",
        //                     "address" => "******************************************",
        //                     "memo" => "",
        //                     "currency" => "ETH",
        //                     "amount" => 1.0000000,
        //                     "fee" => 0.0100000,
        //                     "walletTxId" => "3e2414d82acce78d38be7fe9",
        //                     "isInner" => false,
        //                     "status" => "FAILURE",
        //                     "createdAt" => 1546503758000,
        //                     "updatedAt" => 1546504603000
        //                 ),
        //                 ...
        //             )
        //         }
        //     }
        //
        $responseData = $response['data']['items'];
        return $this->parse_transactions($responseData, $currency, $since, $limit, array( 'type' => 'withdrawal' ));
    }

    public function fetch_market_leverage_tiers(string $symbol, $params = array ()): array {
        /**
         * retrieve information on the maximum leverage, and maintenance margin for trades of varying trade sizes for a single $market
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/risk-limit/get-futures-risk-limit-level
         *
         * @param {string} $symbol unified $market $symbol
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} a ~@link https://docs.ccxt.com/#/?id=leverage-tiers-structure leverage tiers structure~
         */
        $this->load_markets();
        $market = $this->market($symbol);
        if (!$market['contract']) {
            throw new BadRequest($this->id . ' fetchMarketLeverageTiers() supports contract markets only');
        }
        $request = array(
            'symbol' => $market['id'],
        );
        $response = $this->futuresPublicGetContractsRiskLimitSymbol ($this->extend($request, $params));
        //
        //    {
        //        "code" => "200000",
        //        "data" => array(
        //            array(
        //                "symbol" => "ETHUSDTM",
        //                "level" => 1,
        //                "maxRiskLimit" => 300000,
        //                "minRiskLimit" => 0,
        //                "maxLeverage" => 100,
        //                "initialMargin" => 0.01********,
        //                "maintainMargin" => 0.0050000000
        //            ),
        //            ...
        //        )
        //    }
        //
        $data = $this->safe_list($response, 'data', array());
        return $this->parse_market_leverage_tiers($data, $market);
    }

    public function parse_market_leverage_tiers($info, ?array $market = null): array {
        /**
         * @ignore
         * @param {array} $info Exchange $market response for 1 $market
         * @param {array} $market CCXT $market
         */
        //
        //    {
        //        "symbol" => "ETHUSDTM",
        //        "level" => 1,
        //        "maxRiskLimit" => 300000,
        //        "minRiskLimit" => 0,
        //        "maxLeverage" => 100,
        //        "initialMargin" => 0.01********,
        //        "maintainMargin" => 0.0050000000
        //    }
        //
        $tiers = array();
        for ($i = 0; $i < count($info); $i++) {
            $tier = $info[$i];
            $marketId = $this->safe_string($tier, 'symbol');
            $tiers[] = array(
                'tier' => $this->safe_number($tier, 'level'),
                'symbol' => $this->safe_symbol($marketId, $market, null, 'contract'),
                'currency' => $market['base'],
                'minNotional' => $this->safe_number($tier, 'minRiskLimit'),
                'maxNotional' => $this->safe_number($tier, 'maxRiskLimit'),
                'maintenanceMarginRate' => $this->safe_number($tier, 'maintainMargin'),
                'maxLeverage' => $this->safe_number($tier, 'maxLeverage'),
                'info' => $tier,
            );
        }
        return $tiers;
    }

    public function fetch_funding_rate_history(?string $symbol = null, ?int $since = null, ?int $limit = null, $params = array ()) {
        /**
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/funding-fees/get-public-funding-history#$request-url
         *
         * fetches historical funding rate prices
         * @param {string} $symbol unified $symbol of the $market to fetch the funding rate history for
         * @param {int} [$since] not used by kucuoinfutures
         * @param {int} [$limit] the maximum amount of ~@link https://docs.ccxt.com/#/?id=funding-rate-history-structure funding rate structures~ to fetch
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @param {int} [$params->until] end time in ms
         * @return {array[]} a list of ~@link https://docs.ccxt.com/#/?id=funding-rate-history-structure funding rate structures~
         */
        if ($symbol === null) {
            throw new ArgumentsRequired($this->id . ' fetchFundingRateHistory() requires a $symbol argument');
        }
        $this->load_markets();
        $market = $this->market($symbol);
        $request = array(
            'symbol' => $market['id'],
            'from' => 0,
            'to' => $this->milliseconds(),
        );
        $until = $this->safe_integer($params, 'until');
        $params = $this->omit($params, array( 'until' ));
        if ($since !== null) {
            $request['from'] = $since;
            if ($until === null) {
                $request['to'] = $since + 1000 * 8 * 60 * 60 * 100;
            }
        }
        if ($until !== null) {
            $request['to'] = $until;
            if ($since === null) {
                $request['to'] = $until - 1000 * 8 * 60 * 60 * 100;
            }
        }
        $response = $this->futuresPublicGetContractFundingRates ($this->extend($request, $params));
        //
        //     {
        //         "code" => "200000",
        //         "data" => array(
        //             {
        //                 "symbol" => "IDUSDTM",
        //                 "fundingRate" => 2.26E-4,
        //                 "timepoint" => 1702296000000
        //             }
        //         )
        //     }
        //
        $data = $this->safe_list($response, 'data', array());
        return $this->parse_funding_rate_histories($data, $market, $since, $limit);
    }

    public function parse_funding_rate_history($info, ?array $market = null) {
        $timestamp = $this->safe_integer($info, 'timepoint');
        $marketId = $this->safe_string($info, 'symbol');
        return array(
            'info' => $info,
            'symbol' => $this->safe_symbol($marketId, $market),
            'fundingRate' => $this->safe_number($info, 'fundingRate'),
            'timestamp' => $timestamp,
            'datetime' => $this->iso8601($timestamp),
        );
    }

    public function close_position(string $symbol, ?string $side = null, $params = array ()): array {
        /**
         * closes open positions for a $market
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/orders/place-order
         *
         * @param {string} $symbol Unified CCXT $market $symbol
         * @param {string} $side not used by kucoinfutures closePositions
         * @param {array} [$params] extra parameters specific to the okx api endpoint
         * @param {string} [$params->clientOrderId] client order id of the order
         * @return {array[]} ~@link https://docs.ccxt.com/#/?id=position-structure A list of position structures~
         */
        $this->load_markets();
        $market = $this->market($symbol);
        $clientOrderId = $this->safe_string($params, 'clientOrderId');
        $testOrder = $this->safe_bool($params, 'test', false);
        $params = $this->omit($params, array( 'test', 'clientOrderId' ));
        if ($clientOrderId === null) {
            $clientOrderId = $this->number_to_string($this->nonce());
        }
        $request = array(
            'symbol' => $market['id'],
            'closeOrder' => true,
            'clientOid' => $clientOrderId,
            'type' => 'market',
        );
        $response = null;
        if ($testOrder) {
            $response = $this->futuresPrivatePostOrdersTest ($this->extend($request, $params));
        } else {
            $response = $this->futuresPrivatePostOrders ($this->extend($request, $params));
        }
        return $this->parse_order($response, $market);
    }

    public function fetch_trading_fee(string $symbol, $params = array ()): array {
        /**
         * fetch the trading fees for a $market
         *
         * @see https://www.kucoin.com/docs/rest/funding/trade-fee/trading-pair-actual-fee-futures
         *
         * @param {string} $symbol unified $market $symbol
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} a ~@link https://docs.ccxt.com/#/?id=fee-structure fee structure~
         */
        $this->load_markets();
        $market = $this->market($symbol);
        $request = array(
            'symbols' => $market['id'],
        );
        $response = $this->privateGetTradeFees ($this->extend($request, $params));
        //
        //  {
        //      "code" => "200000",
        //      "data" => {
        //        "symbol" => "XBTUSDTM",
        //        "takerFeeRate" => "0.0006",
        //        "makerFeeRate" => "0.0002"
        //      }
        //  }
        //
        $data = $this->safe_list($response, 'data', array());
        $first = $this->safe_dict($data, 0);
        $marketId = $this->safe_string($first, 'symbol');
        return array(
            'info' => $response,
            'symbol' => $this->safe_symbol($marketId, $market),
            'maker' => $this->safe_number($first, 'makerFeeRate'),
            'taker' => $this->safe_number($first, 'takerFeeRate'),
            'percentage' => true,
            'tierBased' => true,
        );
    }

    public function fetch_margin_mode(string $symbol, $params = array ()): array {
        /**
         * fetches the margin mode of a trading pair
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/positions/get-margin-mode
         *
         * @param {string} $symbol unified $symbol of the $market to fetch the margin mode for
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} a ~@link https://docs.ccxt.com/#/?id=margin-mode-structure margin mode structure~
         */
        $this->load_markets();
        $market = $this->market($symbol);
        $request = array(
            'symbol' => $market['id'],
        );
        $response = $this->futuresPrivateGetPositionGetMarginMode ($this->extend($request, $params));
        //
        //     {
        //         "code" => "200000",
        //         "data" => {
        //             "symbol" => "XBTUSDTM",
        //             "marginMode" => "ISOLATED"
        //         }
        //     }
        //
        $data = $this->safe_dict($response, 'data', array());
        return $this->parse_margin_mode($data, $market);
    }

    public function parse_margin_mode(array $marginMode, $market = null): array {
        $marginType = $this->safe_string($marginMode, 'marginMode');
        $marginType = ($marginType === 'ISOLATED') ? 'isolated' : 'cross';
        return array(
            'info' => $marginMode,
            'symbol' => $market['symbol'],
            'marginMode' => $marginType,
        );
    }

    public function set_margin_mode(string $marginMode, ?string $symbol = null, $params = array ()) {
        /**
         * set margin mode to 'cross' or 'isolated'
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/positions/modify-margin-mode
         *
         * @param {string} $marginMode 'cross' or 'isolated'
         * @param {string} $symbol unified $market $symbol
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} $response from the exchange
         */
        if ($symbol === null) {
            throw new ArgumentsRequired($this->id . ' setMarginMode() requires a $symbol argument');
        }
        $this->check_required_argument('setMarginMode', $marginMode, 'marginMode', array( 'cross', 'isolated' ));
        $this->load_markets();
        $market = $this->market($symbol);
        $request = array(
            'symbol' => $market['id'],
            'marginMode' => strtoupper($marginMode),
        );
        $response = $this->futuresPrivatePostPositionChangeMarginMode ($this->extend($request, $params));
        //
        //    {
        //        "code" => "200000",
        //        "data" => {
        //            "symbol" => "XBTUSDTM",
        //            "marginMode" => "ISOLATED"
        //        }
        //    }
        //
        $data = $this->safe_dict($response, 'data', array());
        return $this->parse_margin_mode($data, $market);
    }

    public function fetch_leverage(string $symbol, $params = array ()): array {
        /**
         * fetch the set leverage for a $market
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/positions/get-cross-margin-leverage
         *
         * @param {string} $symbol unified $market $symbol
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} a ~@link https://docs.ccxt.com/#/?id=leverage-structure leverage structure~
         */
        $marginMode = null;
        list($marginMode, $params) = $this->handle_margin_mode_and_params($symbol, $params);
        if ($marginMode !== 'cross') {
            throw new NotSupported($this->id . ' fetchLeverage() currently supports only $params["marginMode"] = "cross"');
        }
        $this->load_markets();
        $market = $this->market($symbol);
        $request = array(
            'symbol' => $market['id'],
        );
        $response = $this->futuresPrivateGetGetCrossUserLeverage ($this->extend($request, $params));
        //
        //    {
        //        "code" => "200000",
        //        "data" => {
        //            "symbol" => "XBTUSDTM",
        //            "leverage" => "3"
        //        }
        //    }
        //
        $data = $this->safe_dict($response, 'data', array());
        $parsed = $this->parse_leverage($data, $market);
        return $this->extend($parsed, array(
            'marginMode' => $marginMode,
        ));
    }

    public function set_leverage(int $leverage, ?string $symbol = null, $params = array ()) {
        /**
         * set the level of $leverage for a $market
         *
         * @see https://www.kucoin.com/docs/rest/futures-trading/positions/modify-cross-margin-$leverage
         *
         * @param {float} $leverage the rate of $leverage
         * @param {string} $symbol unified $market $symbol
         * @param {array} [$params] extra parameters specific to the exchange API endpoint
         * @return {array} $response from the exchange
         */
        $marginMode = null;
        list($marginMode, $params) = $this->handle_margin_mode_and_params($symbol, $params);
        if ($marginMode !== 'cross') {
            throw new NotSupported($this->id . ' setLeverage() currently supports only $params["marginMode"] = "cross"');
        }
        $this->load_markets();
        $market = $this->market($symbol);
        $request = array(
            'symbol' => $market['id'],
            'leverage' => (string) $leverage,
        );
        $response = $this->futuresPrivatePostChangeCrossUserLeverage ($this->extend($request, $params));
        //
        //    {
        //        "code" => "200000",
        //        "data" => true
        //    }
        //
        return $this->parse_leverage($response, $market);
    }

    public function parse_leverage(array $leverage, ?array $market = null): array {
        $marketId = $this->safe_string($leverage, 'symbol');
        $market = $this->safe_market($marketId, $market);
        $leverageNum = $this->safe_integer($leverage, 'leverage');
        return array(
            'info' => $leverage,
            'symbol' => $market['symbol'],
            'marginMode' => null,
            'longLeverage' => $leverageNum,
            'shortLeverage' => $leverageNum,
        );
    }
}
