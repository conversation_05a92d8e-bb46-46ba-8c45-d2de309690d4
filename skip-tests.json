{"alpaca": {"skip": "private endpoints, todo", "skipWs": "private endpoints, todo"}, "apex": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "messed", "currency": "messed"}, "fetchCurrencies": {"precision": "messed"}, "ticker": {"compareQuoteVolumeBaseVolume": "not aligned with quoteVolume calcs"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "orderBook": {"spread": "bid > ask issue https://github.com/ccxt/ccxt/actions/runs/14901691674/job/41855075107?pr=25907#step:9:389"}, "watchTrades": {"timestamp": "order is messed"}}}, "ascendex": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "broken currencies"}, "fetchCurrencies": {"currencyIdAndCode": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269432480#L3364", "type": "todo", "withdraw": "not provided for empty chains", "deposit": "not provided for empty chains", "maxInactiveCurrenciesPercentage": 60}, "ticker": {"compareOHLC": "broken by API: https://github.com/ccxt/ccxt/actions/runs/***********/job/***********#step:9:214"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "watchOrderBook": {"spread": " bid > ask issue, https://github.com/ccxt/ccxt/actions/runs/14316810634/job/40125014500?pr=25623#step:11:420"}, "watchOrderBookForSymbols": {"spread": "same"}}}, "bequant": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "https://app.travis-ci.com/github/ccxt/ccxt/builds/264802937#L2194"}, "fetchCurrencies": {"fee": "not provided", "currencyIdAndCode": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L2455", "type": "todo", "activeMajorCurrencies": "todo"}, "ticker": {"compareOHLC": "broken by API: https://github.com/ccxt/ccxt/actions/runs/***********/job/***********#step:9:187"}, "fetchTickers": {"checkActiveSymbols": "todo"}}}, "binance": {"httpsProxy": "http://***************:3128", "wsProxy": "http://***************:3128", "skipMethods": {"loadMarkets": {"currencyIdAndCode": "i.e. binance does not have currency code BCC"}, "orderBook": {"timestamp": "not present https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L3539 & https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L4119"}, "fetchTickers": {"checkActiveSymbols": "todo"}}}, "binanceus": {"skipMethods": {"ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing, https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L2466"}, "orderBook": {"timestamp": "not present https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L3458 https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L3866"}}}, "binancecoinm": {"httpsProxy": "http://***************:3128", "skipMethods": {"ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing, https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L2414"}, "fetchTickers": {"checkActiveSymbols": "todo"}}}, "binanceusdm": {"httpsProxy": "http://***************:3128", "wsProxy": "http://***************:3128", "skipMethods": {"fetchLedger": {"account": "empty", "status": "not provided", "before": "not provided", "after": "not provided", "fee": "not provided", "code": "not provided", "referenceId": "not provided"}, "watchOrderBook": {"timestamp": "not present https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L3539"}, "watchOrderBookForSymbols": {"timestamp": "same"}}}, "bingx": {"skipMethods": {"loadMarkets": {"taker": "is undefined", "maker": "is undefined", "currencyIdAndCode": "not all currencies are available"}, "ticker": {"bidVolume": "weird values eg -1.1641532182693481e-10 https://github.com/ccxt/ccxt/actions/runs/***********/job/***********?pr=25190#step:9:213", "askVolume": "weird value https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L3904", "compareQuoteVolumeBaseVolume": "not supported", "spread": "same bid-ask https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L3581", "compareOHLC": "broken by API: https://github.com/ccxt/ccxt/actions/runs/***********/job/***********#step:9:253", "maxIncrease": "todo"}, "fetchTickers": "tmp skip, todo", "fetchCurrencies": {"limits": "min/max essup temrporarily"}, "orderBook": {"compareToNextItem": "multiple bid prices are equal https://app.travis-ci.com/github/ccxt/ccxt/builds/265172859#L2745"}, "fetchPositions": {"marginRatio": "undefined", "stopLossPrice": "undefined", "takeProfitPrice": "undefined", "initialMarginPercentage": "undefined", "hedged": "undefined", "timestamp": "undefined", "datetime": "undefined", "lastUpdateTimestamp": "undefined", "maintenanceMargin": "undefined", "contractSize": "undefined", "markPrice": "undefined", "lastPrice": "undefined", "percentage": "undefined", "liquidationPrice": "undefined"}}}, "bit2c": {"skipMethods": {"loadMarkets": {"precision": "not provided", "active": "not provided", "taker": "not provided", "maker": "not provided", "info": "null"}, "fetchCurrencies": "todo, returns only 1", "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L4034", "compareToZero": "sometimes equals to zero: https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L2540"}}}, "bitbank": {"skipMethods": {"fetchCurrencies": {"activeMajorCurrencies": "todo"}}}, "bitbns": {"skipMethods": {"loadMarkets": {"limits": "market limits have min>max ", "currencyIdAndCode": "broken"}, "fetchCurrencies": {"activeMajorCurrencies": "todo"}, "ticker": {"spread": "ask equals to bid", "compareOHLC": "broken by API: https://github.com/ccxt/ccxt/actions/runs/***********/job/***********#step:9:291"}, "fetchTickers": "todo fix parsing"}}, "bitfinex": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "broken currencies"}, "fetchCurrencies": {"withdraw": "not provided", "deposit": "not provided", "type": "todo", "activeMajorCurrencies": "todo"}, "ticker": {"spread": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L3651", "maxIncrease": "todo", "compareOHLC": "todo recheck implementation"}, "orderBook": {"compareToNextItem": "multiple bids might have same value"}, "watchOrderBook": "complete skip (todo fix) because of frequent checksum mismatches: https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L3553 and also for this https://app.travis-ci.com/github/ccxt/ccxt/builds/269129438#L3999", "fetchTickers": {"checkActiveSymbols": "todo"}}}, "bitflyer": {"skipMethods": {"loadMarkets": {"contractSize": "not defined when contract", "settle": "set hardcoded, not settleid provided", "settleId": "not provided", "currencyIdAndCode": "messed"}, "fetchCurrencies": {"activeMajorCurrencies": "todo"}, "orderBook": {"timestamp": "not provided from endoint"}}}, "bitget": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "broken currencies"}, "fetchCurrencies": {"currencyIdAndCode": "broken currencies", "skipCurrenciesWithoutNetworks": "those currencies deosnt have data", "maxInactiveCurrenciesPercentage": 60}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing", "maxIncrease": "todo", "compareOHLC": "broken from API: https://github.com/ccxt/ccxt/actions/runs/***********/job/***********#step:9:318"}, "fetchTickers": {"checkActiveSymbols": "todo"}}}, "bithumb": {"skipMethods": {"ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing"}, "fetchCurrencies": {"activeMajorCurrencies": "todo"}, "orderBook": {"compareToZero": "frequently, zero amount is set in some orderbook entry"}}}, "bitmart": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "broken currencies", "taker": "missing", "maker": "missing"}, "fetchCurrencies": {"currencyIdAndCode": "broken currencies", "precision": "not provided", "maxInactiveCurrenciesPercentage": 70}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing", "quoteVolume": "incorrect", "compareOHLC": "todo"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "watchOrderBook": {"spread": "bid>ask issue, https://github.com/ccxt/ccxt/actions/runs/14356838751/job/40248419588?pr=25638#step:9:437"}, "watchOrderBookForSymbols": {"spread": "same"}}}, "bitmex": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "broken currencies"}, "fetchOHLCV": "https://github.com/ccxt/ccxt/pull/21356#issuecomment-1969565862", "watchOHLCV": "same as above, needs key fix", "fetchPositions": {"stopLossPrice": "undefined", "takeProfitPrice": "undefined", "marginRatio": "undefined", "lastPrice": "undefined", "collateral": "undefined", "hedged": "undefined", "lastUpdateTimestamp": "undefined", "entryPrice": "undefined", "markPrice": "undefined", "leverage": "undefined", "initialMargin": "undefined", "maintenanceMargin": "can be zero for default position", "notional": "can be zero for default position", "contracts": "contracts", "unrealizedPnl": "undefined", "realizedPnl": "undefined", "liquidationPrice": "can be 0", "percentage": "might be 0"}, "fetchMyTrades": {"side": "sometimes side is not available"}, "fetchLedger": {"referenceId": "undefined", "amount": "undefined", "before": "not provided", "tag": "undefined", "tagFrom": "undefined", "tagTo": "undefined", "type": "unmapped types", "timestamp": "default value might be invalid"}, "fetchDepositsWithdrawals": {"currency": "undefined", "currencyIdAndCode": "messes codes"}, "fetchTransactions": "skip", "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L4458"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "ticker": {"compareOHLC": "broken by API: https://github.com/ccxt/ccxt/actions/runs/***********/job/***********#step:9:437"}}}, "bitopro": {"skipMethods": {"fetchCurrencies": {"precision": "not provided", "networks": "missing", "type": "todo"}, "loadMarkets": {"currencyIdAndCode": "broken currencies"}, "fetchOrderBook": {"timestamp": "not provided"}, "watchTicker": {"compareOHLC": "broken https://github.com/ccxt/ccxt/actions/runs/***********/job/***********#step:9:1178"}, "watchTickers": {"compareOHLC": "same"}, "ticker": {"compareOHLC": "todo fix rounding"}}}, "bitrue": {"skip": "tmp", "until": "2025-07-10", "skipMethods": {"loadMarkets": {"currencyIdAndCode": "broken currencies", "limits": "max is below min"}, "fetchCurrencies": {"precision": "not provided", "currencyIdAndCode": "broken currencies"}, "ticker": {"compareQuoteVolumeBaseVolume": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269454230#L4164"}, "fetchTickers": {"checkActiveSymbols": "todo"}}}, "bitso": {"skipWs": true, "skipMethods": {"loadMarkets": {"active": "not provided"}, "fetchCurrencies": {"activeMajorCurrencies": "todo"}, "orderBook": {"compareToZero": "tmp", "compareToNextItem": "tmp", "spread": "tmp"}}}, "bitstamp": {"skipMethods": {"fetchCurrencies": {"withdraw": "not provided", "deposit": "not provided", "activeMajorCurrencies": "todo"}, "orderBook": {"compareToZero": "bid/ask might be 0", "spread": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269693760#L3648"}, "ticker": {"spread": "greater than ask https://app.travis-ci.com/github/ccxt/ccxt/builds/264241638#L3027", "compareQuoteVolumeBaseVolume": "baseVolume * low = 8.43e-6 * 3692.59081464 = 0.03112854056 < 0.0311285405674152"}, "fetchTickers": {"checkActiveSymbols": "todo"}}}, "bitteam": {"skipMethods": {"loadMarkets": {"taker": "is undefined", "maker": "is undefined"}, "fetchCurrencies": {"deposit": "not provided", "withdraw": "not provided", "activeMajorCurrencies": "todo"}, "ticker": {"compareOHLC": "todo fix implementation"}}}, "bitvavo": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "broken currencies", "taker": "is undefined", "maker": "is undefined"}, "fetchCurrencies": {"activeMajorCurrencies": "todo"}, "ticker": {"spread": "broken bid-ask", "compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing https://app.travis-ci.com/github/ccxt/ccxt/builds/266144312#L2220"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L4685"}}}, "blockchaincom": {"skipMethods": {"loadMarkets": {"taker": "not provided", "maker": "not provided"}, "fetchCurrencies": {"activeMajorCurrencies": "todo"}, "orderBook": {"compareToNextItem": "sometimes bid equals to next bid", "timestamp": "not provided"}}}, "btcbox": {"skipMethods": {"loadMarkets": {"precision": "is undefined"}, "fetchCurrencies": {"activeMajorCurrencies": "todo"}, "orderBook": {"spread": "bids[0][0] (3787971.0) should be < than asks[0][0] (3787971.0)", "timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L4820"}, "ticker": {"spread": "broken bid-ask"}}}, "btcalpha": {"skipMethods": {"fetchCurrencies": {"activeMajorCurrencies": "todo"}, "orderBook": {"spread": "bids[0][0] is not < asks[0][0]", "timestamp": ""}, "ticker": {"spread": "messed bid-ask", "compareOHLC": "fix implementation"}, "fetchTickers": {"checkActiveSymbols": "todo"}}}, "btcmarkets": {"skipMethods": {"fetchCurrencies": {"activeMajorCurrencies": "todo"}, "orderBook": {"compareToNextItem": "sometimes bid equals to next bid"}}}, "btcturk": {"skipMethods": {"fetchCurrencies": {"activeMajorCurrencies": "todo"}, "orderBook": {"compareToZero": "https://app.travis-ci.com/github/ccxt/ccxt/builds/263287870#L2201", "compareToNextItem": "sometimes bid equals to next bid"}}}, "bybit": {"httpsProxy": "http://***************:3128", "wsProxy": "http://***************:3128", "skipMethods": {"loadMarkets": {"currencyIdAndCode": "temp skip"}, "fetchCurrencies": {"currencyIdAndCode": "temp skip"}, "ticker": {"compareQuoteVolumeBaseVolume": "incorrect calcs: https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L4452"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "fetchPositions": "currently returns a lot of default/non open positions", "fetchLedger": {"account": "account is not provided", "status": "status is not provided", "fee": "undefined"}, "fetchOpenInterestHistory": {"openInterestAmount": "openInterestAmount is not provided"}, "fetchBorrowRate": "does not work with unified account", "orderBook": {"compareToNextItem": "messed https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L3794"}}}, "bigone": {"skipMethods": {"loadMarkets": {"taker": "not provided", "maker": "not provided", "currencyIdAndCode": true}, "fetchCurrencies": {"withdrawForNonCrypto": "not available", "depositForNonCrypto": "not available"}, "ticker": {"spread": "broken bid-ask", "baseVolume": "negative value", "compareQuoteVolumeBaseVolume": "todo"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "orderBook": {"timestamp": "not present"}}}, "coincheck": {"skipWs": true, "skipMethods": {"loadMarkets": {"info": "not provided", "precision": "not provided", "active": "is undefined", "taker": "is undefined", "maker": "is undefined"}, "fetchCurrencies": "todo: doesnt return more than 1 coins", "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L4893"}}}, "coinbase": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "some curr not available"}, "fetchCurrencies": {"precision": "not provided", "networks": "not provided", "deposit": "not provided", "withdraw": "not provided", "activeMajorCurrencies": "todo"}, "watchOrderBook": {"spread": "broken bid-ask: https://github.com/ccxt/ccxt/actions/runs/14995105728/job/42127573821?pr=25943#step:9:382"}, "watchOrderBookForSymbols": {"spread": "broken bid-ask: https://github.com/ccxt/ccxt/actions/runs/14995105728/job/42127573821?pr=25943#step:9:382"}, "watchTickers": "closes connection", "fetchTickers": {"checkActiveSymbols": "todo"}}}, "coinbaseexchange": {"skipWs": "needs auth", "skipMethods": {"loadMarkets": {"currencyIdAndCode": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269568596#L3551"}, "fetchStatus": "request timeout", "fetchCurrencies": {"currencyIdAndCode": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269568596#L3551", "deposit": "not provided", "withdraw": "not provided", "activeMajorCurrencies": "not provided"}, "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L5064"}, "fetchTickers": {"checkActiveSymbols": "todo"}}}, "coinbaseinternational": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "i.e. coinbase does not have currency code TIA", "max": "zero values"}, "fetchCurrencies": {"precision": "not provided", "networks": "not provided", "withdraw": "not provided", "deposit": "not provided", "type": "todo", "activeMajorCurrencies": "todo"}}}, "coincatch": {"skipMethods": {"fetchCurrencies": {"precision": "not provided", "type": "todo", "withdraw": "todo, needs fix", "deposit": "todo, needs fix", "active": "todo, needs fix", "activeMajorCurrencies": "todo", "activeCurrenciesQuota": "activeCurrenciesQuota"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "watchTickers": {"compareOHLC": "broken by API: https://github.com/ccxt/ccxt/actions/runs/16077301099/job/45375495181?pr=26334#step:9:438"}, "loadMarkets": "linear and inverse values are same"}}, "coinmetro": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "some currencies show in fetchMarkets but not in fetchCurrencies"}, "fetchTrades": {"side": "side is undefined"}, "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L5064"}, "ticker": {"spread": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269687705#L3745", "compareOHLC": "todo"}, "fetchTickers": {"checkActiveSymbols": "tmp VXV issue"}}}, "coinone": {"skipWs": true, "skipMethods": {"loadMarkets": {"active": "is undefined"}, "ticker": {"compareQuoteVolumeBaseVolume": "quote scale isn't right"}}}, "coinspot": {"skip": "temp", "skipMethods": {"loadMarkets": {"precision": "not provided", "taker": "is undefined", "makert": "is undefined"}, "ticker": {"spread": "broken bid-ask", "compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing"}}}, "coinsph": {"skipMethods": {"loadMarkets": {"taker": "messed", "maker": "messed"}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L5210"}}}, "cex": {"preferredSpotSymbol": "BTC/USD", "skipMethods": {"proxies": "probably they do not permit our proxy location", "loadMarkets": {"taker": "unavailable", "maker": "unavailable", "active": "undefined", "limits": "zero min/max", "currencyIdAndCode": "messes codes"}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing", "compareOHLC": "todo"}, "fetchOHLCV": "cant be tested because of additional required param", "watchOHLCV": "does not work for 1min"}}, "coinex": {"skipWs": "timeouts", "skipMethods": {"loadMarkets": {"currencyIdAndCode": "broken", "active": "is undefined"}, "fetchCurrencies": {"precision": "not provided", "networks": "something implementation glitch", "type": "todo"}, "fetchTickers": {"checkActiveSymbols": "todo"}}}, "coinmate": {"skip": "clourder<PERSON>r error", "skipMethods": {"loadMarkets": {"active": "is undefined"}, "orderBook": {"compareToNextItem": "ask should be less than next ask"}}}, "cryptocom": {"skipMethods": {"proxies": "probably they do not permit our proxy", "loadMarkets": {"currencyIdAndCode": "from travis location (USA) these webapi endpoints cant be loaded"}, "fetchCurrencies": {"precision": "not provided"}, "ticker": {"compareQuoteVolumeBaseVolume": "https://github.com/ccxt/ccxt/actions/runs/15016394551/job/42195664486?pr=25955#step:11:304"}, "fetchPositions": {"entryPrice": "entryPrice is not provided", "markPrice": "undefined", "notional": "undefined", "leverage": "undefined", "liquidationPrice": "undefined", "marginMode": "undefined", "percentage": "undefined", "marginRatio": "undefined", "stopLossPrice": "undefined", "takeProfitPrice": "undefined", "maintenanceMargin": "undefined", "initialMarginPercentage": "undefined", "maintenanceMarginPercentage": "undefined", "hedged": "undefined", "side": "undefined", "contracts": "undefined"}, "fetchAccounts": {"type": "type is not provided", "code": "not provided"}}}, "cryptomus": {"skipMethods": {"fetchCurrencies": {"precision": "not provided", "type": "todo"}, "loadMarkets": {"active": "not provided", "limits": "not provided", "currencyIdAndCode": "messed codes"}, "fetchOrderBook": {"compareBidAsk": "sometimes bid is greater than ask"}, "fetchTrades": {"price": "sometimes is not set", "cost": "same", "amount": "sometimes not present, only cost present sometimes"}, "fetchTickers": {"checkActiveSymbols": "todo"}}}, "currencycom": {"skipMethods": {"loadMarkets": {"type": "unexpected market type", "contractSize": "not defined when contract", "settle": "not defined when contract", "settleId": "not defined when contract"}, "ticker": {"spread": "not above bid https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L2163", "compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing"}, "watchTrades": {"fees": "missing https://app.travis-ci.com/github/ccxt/ccxt/builds/269365378#L3601"}, "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L5510"}}}, "defx": {"skipMethods": {"ohlcv": {"1": "distorted open: https://github.com/ccxt/ccxt/actions/runs/14648217027/job/41107795879?pr=25786#step:9:458"}, "ticker": {"compareOHLC": "broken by API: https://github.com/ccxt/ccxt/actions/runs/16075871000/job/45371060374?pr=26334#step:9:298"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "watchBidsAsks": "temporary skip, because of null values being returned https://github.com/ccxt/ccxt/actions/runs/15016394551/job/42195664486?pr=25955#step:11:505"}}, "delta": {"skipCSharp": "frequent timeouts https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L4301 https://app.travis-ci.com/github/ccxt/ccxt/builds/269533613#L3622", "skipMethods": {"loadMarkets": {"contractSize": "todo", "expiry": "wrng format"}, "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L5295"}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing", "bid": "failing the test", "compareOHLC": "broken by API: https://github.com/ccxt/ccxt/actions/runs/16059129227/job/45320923643?pr=26334#step:9:291"}, "fetchOHLCV": {"4": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269542746#L3512"}, "fetchCurrencies": {"type": "todo", "activeMajorCurrencies": "todo", "activeCurrenciesQuota": "todo"}}}, "deribit": {"skipMethods": {"fetchCurrencies": {"networks": "not provided", "withdraw": "not provided", "deposit": "not provided", "type": "todo", "activeMajorCurrencies": "todo"}, "loadMarkets": "strike is set when option is not true", "fetchBalance": "does not add up", "fetchPositions": {"percentage": "undefined", "hedged": "undefined", "stopLossPrice": "undefined", "takeProfitPrice": "undefined", "lastPrice": "undefined", "collateral": "undefined", "marginMode": "undefined", "marginRatio": "undefined", "contracts": "undefined", "id": "undefined"}, "fetchDeposits": {"id": "undefined", "network": "undefined", "addressFrom": "undefined", "tag": "undefined", "tagTo": "undefined", "tagFrom": "undefined", "fee": "undefined"}, "ticker": {"close": "might be negative https://app.travis-ci.com/github/ccxt/ccxt/builds/269484317#L4371", "open": "same", "low": "same", "high": "same", "bid": "same", "ask": "same", "average": "same"}, "fetchTickers": "requires custom param"}}, "derive": {"skipMethods": {"loadMarkets": {"currency": "messed", "currencyIdAndCode": "messed"}}}, "digifinex": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "messed codes"}, "fetchCurrencies": {"precision": "messed", "type": "todo"}, "fetchTicker": "unexpected symbol is being returned | safeMarket() requires a fourth argument for BTC_USDT to disambiguate between different markets with the same market id", "fetchTickers": "unexpected symbol is being returned | safeMarket() requires a fourth argument for BTC_USDT to disambiguate between different markets with the same market id", "fetchLeverageTiers": {"minNotional": "undefined", "currencyIdAndCode": "messed codes", "currency": "messed"}, "fetchBorrowRates": {"currencyIdAndCode": "messed codes", "currency": "messed"}, "fetchBorrowInterest": "symbol is messed", "fetchPositions": {"percentage": "undefined", "stopLossPrice": "undefined", "takeProfitPrice": "undefined", "collateral": "undefined", "initialMargin": "undefined", "initialMarginPercentage": "undefined", "hedged": "undefined", "id": "undefined", "notional": "undefined"}, "fetchBalance": "tmp skip"}}, "exmo": {"skipMethods": {"loadMarkets": {"active": "is undefined"}, "fetchCurrencies": {"withdraw": "not provided", "deposit": "not provided", "activeMajorCurrencies": "todo"}, "ticker": {"compareQuoteVolumeBaseVolume": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269352042#L3690", "compareOHLC": "broken by API: https://github.com/ccxt/ccxt/actions/runs/16075871000/job/45371060374?pr=26334#step:9:324"}, "watchOrderBook": {"nonce": "missing https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L4807"}, "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L5420"}}}, "ellipx": {"skipMethods": {"fetchOrderBook": {"spread": "messed bid-ask https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L4611"}, "fetchCurrencies": {"type": "todo", "withdraw": "not provided", "deposit": "not provided", "activeMajorCurrencies": "todo"}}}, "fmfwio": {"skipMethods": {"fetchCurrencies": {"fee": "not provided", "type": "todo"}, "ticker": {"compareOHLC": "broken from API: https://github.com/ccxt/ccxt/actions/runs/***********/job/***********#step:9:528"}}}, "gate": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "messed codes", "limits": "max should be above min"}, "fetchCurrencies": {"currencyIdAndCode": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L2559"}, "ticker": {"compareQuoteVolumeBaseVolume": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L3138", "compareOHLC": "todo"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "fetchPositions": "currently returns a lot of default/non open positions", "fetchLedger": {"currency": "undefined", "status": "undefined", "fee": "undefined", "account": "undefined", "referenceAccount": "undefined", "referenceId": "undefined"}, "fetchTradingFees": "sandbox does not have this endpoint", "fetchDeposits": "sandbox does not have this endpoint", "fetchWithdrawals": "sandbox does not have this endpoint"}}, "gemini": {"skip": "temporary USDC symbol issue in their API engine for swaps", "until": "2025-08-31", "skipPhpAsync": "tmp", "skipCSharp": "tmp", "skipMethods": {"loadMarkets": {"currencyIdAndCode": "messed codes", "active": "not provided"}, "fetchCurrencies": {"withdraw": "not provided", "deposit": "not provided", "activeMajorCurrencies": "todo"}, "watchOrderBook": {"nonce": "missing https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L4833"}, "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L5654"}}}, "hashkey": {"httpsProxy": "http://***************:3128", "wsProxy": "http://***************:3128", "skipMethods": {"fetchCurrencies": {"precision": "not provided", "type": "todo"}, "fetchTrades": {"side": "not provided"}, "watchTrades": {"side": "not provided"}, "fetchTickers": {"checkActiveSymbols": "todo"}}}, "hibachi": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "only have USDT"}, "fetchCurrencies": {"amountOfCurrencies": "exchange has only 1 currency"}, "fetchLedger": {"before": "not provided", "after": "not provided", "fee": "not provided for transaction"}, "fetchDeposits": {"fee": "not provided for deposit", "updated": "not provided"}, "fetchWithdrawals": {"fee": "not provided for withdrawal", "updated": "not provided"}}}, "hitbtc": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "messed codes"}, "fetchCurrencies": {"fee": "not provided", "currencyIdAndCode": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L2455", "type": "todo", "activeMajorCurrencies": "todo"}, "ticker": {"bid": "messed bid-ask", "compareOHLC": "todo"}, "watchOrderBook": {"spread": "tmp", "compareToNextItem": "tmp"}, "watchTickers": {"compareOHLC": "broken by exchange https://github.com/ccxt/ccxt/actions/runs/16057416332/job/45315454452?pr=26334#step:11:2028"}}}, "hyperliquid": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "different"}, "fetchTrades": "private", "fetchCurrencies": {"id": "skip", "precision": "skip", "networks": "not provided", "limits": "not provided", "deposit": "not provided", "withdraw": "not provided", "type": "todo", "activeMajorCurrencies": "todo"}, "watchTrades": {"currency": "not provided"}}}, "hollaex": {"skipWs": "temp", "skipMethods": {"watchOrderBook": {"nonce": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L4957"}, "fetchCurrencies": {"type": "todo"}, "fetchTickers": {"checkActiveSymbols": "todo"}}}, "htx": {"skipMethods": {"loadMarkets": {"limits": "messed", "currencyIdAndCode": "messed codes"}, "fetchCurrencies": {"withdraw": "not provided", "deposit": "not provided", "precision": "is undefined", "limits": "broken somewhere", "type": "todo"}, "ticker": {"compareQuoteVolumeBaseVolume": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L4860", "bid": "messed bid-ask"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "orderBook": {"symbol": "undefined, todo fix https://app.travis-ci.com/github/ccxt/ccxt/builds/269484317#L3670"}, "watchOrderBook": {"timestamp": "not provided"}, "watchOrderBookForSymbols": {"timestamp": "not provided"}}}, "bittrade": {"skipWs": "timeouts", "skipMethods": {"loadMarkets": {"limits": "messed"}, "fetchCurrencies": {"fee": "not defined", "networks": "missing"}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "fetchTrades": {"fees": "missing"}}}, "indodax": {"skipMethods": {"fetchCurrencies": {"activeMajorCurrencies": "todo"}, "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L5881"}}}, "independentreserve": {"skipWs": "timeouts", "skipMethods": {"loadMarkets": {"active": "is undefined"}, "fetchCurrencies": {"activeMajorCurrencies": "todo"}, "fetchTrades": {"side": "side is undefined"}, "fetchTickers": "negative values", "orderBook": {"compareToNextItem": "sometimes bid equals to next bid", "compareToZero": "https://app.travis-ci.com/github/ccxt/ccxt/builds/263629640#L2165"}}}, "kraken": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "https://app.travis-ci.com/github/ccxt/ccxt/builds/267515280#L2314"}, "fetchCurrencies": {"withdraw": "undefined", "deposit": "undefined", "currencyIdAndCode": "same as in loadMarkets", "type": "todo", "activeMajorCurrencies": "todo"}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume <= baseVolume * high is failing", "compareOHLC": "todo: fix implementation"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L5965"}}}, "krakenfutures": {"skipMethods": {"loadMarkets": {"active": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269484317#L4502", "contractSize": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269640396#L3857", "currencyIdAndCode": "messed"}, "fetchCurrencies": {"currencyIdAndCode": "messed", "withdraw": "undefined", "deposit": "undefined", "activeMajorCurrencies": "todo"}, "watchBidsAsks": {"bidVolume": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269878519#L3759", "askVolume": "same"}, "watchOrderBook.cs": {"spread": "", "compareToNextItem": ""}, "watchOrderBookForSymbols.cs": {"spread": "same as above", "compareToNextItem": "same as above"}, "fetchTickers": {"checkActiveSymbols": "todo"}}, "timeout": 120000}, "kucoin": {"skipMethods": {"fetchCurrencies": {"depositForNonCrypto": "not provided", "withdrawForNonCrypto": "not provided", "limits": "is not provided", "currencyIdAndCode": "messed https://app.travis-ci.com/github/ccxt/ccxt/builds/271028497#L5338"}, "loadMarkets": {"taker": "temporary, until NFT-TRX appears in https://api.kucoin.com/api/v1/market/allTickers", "maker": "same", "currencyIdAndCode": "messed"}, "ticker": {"bid": "messed bid-ask", "compareQuoteVolumeBaseVolume": "quoteVolume <= baseVolume * high https://app.travis-ci.com/github/ccxt/ccxt/builds/263304041#L2190", "maxIncrease": "todo", "compareOHLC": "broken by API: https://github.com/ccxt/ccxt/actions/runs/16059129227/job/45320923643?pr=26334#step:9:317"}, "fetchTickers": {"checkActiveSymbols": "todo"}}}, "kucoinfutures": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "messed"}, "fetchPositions": {"percentage": "percentage is not provided"}, "fetchTickers": {"compareQuoteVolumeBaseVolume": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269634788#L1898"}, "watchOrderBook": {"spread": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269407124#L3464"}, "ticker": {"compareOHLC": "todo: fix implementation"}}}, "latoken": {"skipMethods": {"loadMarkets": {"precision": "messed just for one pair https://app.travis-ci.com/github/ccxt/ccxt/builds/269924468#L4006", "currency": "messed", "currencyIdAndCode": "messed"}, "fetchCurrencies": {"currency": "messed", "currencyIdAndCode": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269188556#L4337", "withdraw": "not provided", "deposit": "not provided", "activeMajorCurrencies": "todo"}, "ticker": {"open": "negative values", "high": "negative values", "low": "negative values", "close": "negative values", "bid": "negative values", "bidVolume": "negative values https://app.travis-ci.com/github/ccxt/ccxt/builds/271032316#L5285", "ask": "negative values", "askVolume": "negative values https://app.travis-ci.com/github/ccxt/ccxt/builds/271032316#L5285", "average": "negative values", "vwap": "zero values https://github.com/ccxt/ccxt/actions/runs/***********/job/***********?pr=25233", "maxIncrease": "too high increases https://github.com/ccxt/ccxt/actions/runs/***********/job/***********#step:9:833"}, "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L6049"}}}, "luno": {"skipWs": "temp", "skipMethods": {"fetchCurrencies": {"activeMajorCurrencies": "todo"}, "orderBook": {"compareToNextItem": "messed"}}}, "lbank": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "messed codes"}, "fetchCurrencies": {"activeMajorCurrencies": "todo", "deposit": "not provider", "type": "not provided"}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing", "compareOHLC": "todo test tolerance"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "watchTrades": {"timestamp": "ts several hours ahead in in future :)"}, "orderBook": {"spread": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L3612", "compareToNextItem": "frequently failing: https://github.com/ccxt/ccxt/actions/runs/***********/job/***********?pr=26044#step:10:29"}, "ohlcv": {"1": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L4669"}, "watchOHLCV": "some timestamp issues"}}, "mercado": {"skipMethods": {"loadMarkets": "needs migration to v4, as raw info is not being used. granular can be used for skipping 'info'", "ticker": {"spread": "sometimes bid is not lower than ask"}, "orderBook": {"spread": "bid-ask crossing", "timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L6133"}, "fetchCurrencies": {"info": "key is missing", "activeMajorCurrencies": "todo"}}}, "mexc": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "messed"}, "fetchCurrencies": {"limits": "max above min", "precision": "is undefined"}, "fetchTrades": {"side": "side is not buy/sell"}, "ticker": {"compareQuoteVolumeBaseVolume": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L6610", "spread": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L3916", "vwap": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L5664", "maxIncrease": "too high gains"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "fetchAccounts": {"type": "type is not provided"}, "fetchLeverageTiers": "swap only supported", "orderBook": {"spread": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L3916"}}}, "modetrade": {"skipMethods": {"loadMarkets": {"active": "undefined", "currencyIdAndCode": "lev curr"}, "fetchCurrencies": {"amountOfCurrencies": "exchange has only 1 currency atm", "withdraw": "not provided", "deposit": "not provided", "activeMajorCurrencies": "todo", "type": "todo"}}}, "novadax": {"skipMethods": {"fetchCurrencies": {"activeMajorCurrencies": "todo"}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing", "bid": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********"}, "fetchTickers": {"checkActiveSymbols": "todo"}}}, "ndax": {"skipWs": "timeouts", "skipMethods": {"fetchCurrencies": {"withdraw": "not provided", "deposit": "not provided", "activeMajorCurrencies": "todo"}, "fetchOrderBook": {"timestamp": "undefined https://app.travis-ci.com/github/ccxt/ccxt/builds/272800616#L3898"}}}, "oceanex": {"skipMethods": {"loadMarkets": {"active": "is undefined"}, "fetchCurrencies": {"activeMajorCurrencies": "todo"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "fetchOrderBooks": "fetchOrderBooks returned 0 length"}}, "okcoin": {"skipWs": "temp", "skipMethods": {"ticker": {"symbol": "missing https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L6721", "compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing"}}}, "oxfun": {"skip": "cloudflare captcha block", "skipWs": "same", "httpsProxy": "http://**************:8911", "wsProxy": "http://**************:8911", "skipMethods": {"loadMarkets": {"currencyIdAndCode": "messed https://app.travis-ci.com/github/ccxt/ccxt/builds/270314325"}, "orderBook": {"spread": "bid ask crossing, https://app.travis-ci.com/github/ccxt/ccxt/builds/270991459#L5901"}}}, "onetrading": {"skip": "API was changed: https://github.com/ccxt/ccxt/pull/21991, also our regular proxy does not seem to work, need smth fix", "skipWs": "same", "skipMethods": {"fetchCurrencies": {"withdraw": "not provided", "deposit": "not provided"}, "orderBook": {"compareToNextItem": "messed bids sequence", "timestamp": "not provided"}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing"}}}, "okx": {"skipCSharp": true, "skipMethods": {"loadMarkets": {"currencyIdAndCode": "messed", "contractSize": "broken for some markets"}, "fetchCurrencies": {"precision": "undefined", "currencyIdAndCode": "temp skip"}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume <= baseVolume * high : https://app.travis-ci.com/github/ccxt/ccxt/builds/263319874#L2132"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "fetchBorrowRate": "some fields that we can't skip missing", "fetchBorrowRates": "same", "watchOrderBook": {"nonce": "missing https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L6721"}}}, "okxus": {"skipCSharp": true, "skipMethods": {"fetchTickers": {"checkActiveSymbols": "todo"}}}, "myokx": {"skipCSharp": true, "skipMethods": {"fetchTickers": {"checkActiveSymbols": "todo"}}}, "paymium": {"skip": "exchange is down", "skipMethods": {"loadMarkets": {"precision": "not provided", "active": "not provided", "info": "null", "taker": "is undefined", "maker": "is undefined"}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing"}}}, "probit": {"skipWs": "timeouts", "skipMethods": {"loadMarkets": "needs fixing", "fetchCurrencies": {"limits": "messed", "type": "todo"}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing", "compareOHLC": "broken from API: https://github.com/ccxt/ccxt/actions/runs/***********/job/***********#step:9:898"}, "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L6363"}}}, "paradex": {"skipMethods": {"loadMarkets": "perp options not handled well by tests", "orderBook": {"_disabled_spread": "bid-ask crossing"}, "ticker": {"bid": "equal to ask https://github.com/ccxt/ccxt/actions/runs/15595034293/job/43923530003?pr=26177#step:9:504"}, "fetchTickers": {"checkActiveSymbols": "todo", "average": "sometimes zero"}}}, "p2b": {"skip": "temp issues", "skipWs": "flaky", "httpsProxy": "http://**************:8911", "skipMethods": {"loadMarkets": "invalid URL", "fetchTrades": "requires order id"}}, "poloniex": {"skipWs": "temporarily skip before new WS api merged", "skipMethods": {"loadMarkets": {"currencyIdAndCode": "some currencies does not exist in currencies", "created": "buggy timestamps https://app.travis-ci.com/github/ccxt/ccxt/builds/270243654#L5259", "taker": "not provided", "maker": "same"}, "fetchCurrencies": {"currencyIdAndCode": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269956260#L4357", "withdraw": "undefined", "deposit": "undefined", "networks": "networks key is missing", "precision": "not provided", "type": "todo", "maxInactiveCurrenciesPercentage": 70}, "fetchTrades": {"side": "side is not buy/sell"}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume <= baseVolume * high | https://app.travis-ci.com/github/ccxt/ccxt/builds/263884643#L2462", "maxIncrease": "very distant bid/ask"}, "fetchTickers": {"checkActiveSymbols": "todo"}, "watchOrderBook": {"nonce": "missing https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L6909"}}}, "phemex": {"skipPhpAsync": true, "skipCSharp": true, "skipMethods": {"loadMarkets": {"contractSize": "broken for some markets", "active": "not provided", "currencyIdAndCode": "messed", "taker": "null", "maker": "null"}, "fetchCurrencies": {"withdraw": "not provided", "deposit": "not provided", "type": "todo", "activeMajorCurrencies": "todo", "networks": "not provided"}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing", "bid": "messed bid-ask"}, "fetchTickers": {"checkActiveSymbols": "todo"}}}, "tradeogre": {"skip": "flaky", "skipWs": "down", "skipMethods": {"watchOrderBook": {"timestamp": "not provided"}}}, "timex": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "messed"}, "fetchCurrencies": {"fee": "is undefined", "networks": "key not present", "type": "todo"}, "fetchTrades": {"fees": "missingn from structure"}, "fetchTickers": "temporary issues"}}, "tokocrypto": {"httpsProxy": "http://***************:3128", "skipMethods": {"orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L6195"}}}, "upbit": {"skipMethods": {"fetchCurrencies": {"activeMajorCurrencies": "todo"}, "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing"}, "orderBook": {"compareToZero": "some of them mare zero", "compareToNextItem": "several rows are same zero"}}}, "vertex": {"httpsProxy": "http://***************:3128", "wsProxy": "http://***************:3128", "skipPhpAsync": "for some reason it shows invalid headers - https://app.travis-ci.com/github/ccxt/ccxt/builds/272619476#L4519 but cant reproduce locally", "skipCSharp": "same as above", "skipMethods": {"fetchCurrencies": "incomplete", "loadMarkets": {"currencyIdAndCode": "different", "amount": "handled seperately", "precision": "handled seperately"}, "watchTrades": {"side": "not set"}, "watchOrderBook": {"spread": "cross"}, "watchOrderBookForSymbols": {"spread": "same"}}}, "wavesexchange": {"skipMethods": {"loadMarkets": "missing key", "fetchOHLCV": "index 1 (open price) is undefined", "ticker": {"compareQuoteVolumeBaseVolume": "quoteVolume >= baseVolume * low is failing"}, "fetchCurrencies": {"activeMajorCurrencies": "todo"}}}, "whitebit": {"skipWs": "timeouts", "skipMethods": {"loadMarkets": "contractSize must be undefined when contract is false", "fetchCurrencies": {"info": "missing key", "precision": "not provided", "fee": "is undefined", "networks": "missing", "limits": "broken for some markets", "type": "todo"}}}, "woo": {"//skipWs": "requires auth", "skipMethods": {"loadMarkets": {"active": "undefined", "currencyIdAndCode": "messed"}, "fetchCurrencies": {"withdraw": "undefined", "deposit": "undefined", "type": "todo", "activeMajorCurrencies": "todo"}, "fetchPositions": {"leverage": "undefined", "percentage": "undefined", "hedged": "undefined", "stopLossPrice": "undefined", "takeProfitPrice": "undefined", "id": "undefined", "marginRatio": "undefined", "collateral": "undefined"}, "watchBidsAsks": "newUpdates is true", "watchTickers": {"baseVolume": "PURSE/USDT has weird ticker", "quoteVolume": "PURSE/USDT has weird ticker"}}}, "woofipro": {"skipMethods": {"loadMarkets": {"active": "undefined", "currencyIdAndCode": "messed"}, "fetchCurrencies": "doesnt return currencies, todo"}}, "wazirx": {"skipWs": "timeouts", "skipMethods": {"loadMarkets": "private", "fetchCurrencies": "private", "ticker": {"bid": "https://app.travis-ci.com/github/ccxt/ccxt/builds/269200021#L4048"}}}, "xt": {"skipMethods": {"loadMarkets": {"taker": "is undefined", "maker": "is undefined", "currencyIdAndCode": "different", "limits": "incorrect for some currencies"}, "fetchTrades": {"side": "not set"}, "watchTickers": {"compareOHLC": "broken by exchange https://github.com/ccxt/ccxt/actions/runs/16057416332/job/45315454452?pr=26334#step:11:2055"}, "ticker": {"maxIncrease": "too much increases https://github.com/ccxt/ccxt/actions/runs/***********/job/***********#step:9:925", "compareOHLC": "broken by API: https://github.com/ccxt/ccxt/actions/runs/16059129227/job/45320923643?pr=26334#step:9:317"}, "fetchTickers": {"ask": "weird values", "bid": "weird values", "checkActiveSymbols": "todo"}, "watchOrderBook": {"timestamp": "not provided", "spread": "bid-ask crossing"}, "fetchCurrencies": {"type": "todo"}}}, "yobit": {"skipMethods": {"loadMarkets": {"currencyIdAndCode": "messed"}, "fetchCurrencies": {"activeMajorCurrencies": "todo"}, "fetchTickers": "all tickers request exceedes max url length", "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L6425"}}}, "zaif": {"skipMethods": {"fetchCurrencies": {"info": "key is missing", "activeMajorCurrencies": "todo"}, "loadMarkets": {"active": "is undefined"}, "orderBook": {"timestamp": "https://app.travis-ci.com/github/ccxt/ccxt/builds/*********#L6279"}}}, "zonda": {"skipMethods": {"loadMarkets": {"active": "is undefined"}, "fetchCurrencies": {"activeMajorCurrencies": "todo"}}}}