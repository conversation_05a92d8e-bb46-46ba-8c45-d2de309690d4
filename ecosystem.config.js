const path = require('path');

module.exports = {
  apps: [{
    name: 'crypto-monitor',
    script: './out/index.js',        // Your compiled entry point
    node_args: '--trace-warnings',  // Node.js arguments from your 'start' script

    env: {
      NODE_ENV: 'production', // PM2 Overrides the .env file variables
      // Define any other production-specific environment variables here
      // E.g., API_KEY: "your_production_api_key"
      // PORT: 8080 (if your app respects process.env.PORT)
    },
 
    // instances: 'max',            // Use all available CPU cores
    instances: 1,                   // Use 1 CPU core

    // Enable cluster mode for multi-core utilization
    exec_mode: 'cluster',
    
    // --- Restart Configuration ---
    // Restart on file change
    watch: false,
    // Restart if memory exceeds this.
    max_memory_restart: '600M',
    // increase incrementally the time between restarts,
    // if you get a lot of restarts in a short time
    exp_backoff_restart_delay: 100,
    
    // --- Logging Configuration ---
    // Paths for logs within your project's "Logs" folder
    // PM2 will append instance numbers in cluster mode (e.g., out-0.log)
    out_file: path.resolve(__dirname, './logs/out.log'),  // Standard output log
    error_file: path.resolve(__dirname, './logs/err.log'), // Error log
    // if each cluster should have its own 'err' and 'out' log files (when false) 
    // or 'err' and 'out' should be merged into one file (when true)
    merge_logs: true, // all clusters' 'err' and 'out' files should be merged into a single file
    // Default is false. Keep stdout and stderr separate.
    // If true, stderr would also go to out_file.
    combine_logs: false,
    // (records format inside the files) ISO 8601 format with timezone
    // log_date_format: 'YYYY-MM-DD HH:mm:ss Z', 
  }]
};