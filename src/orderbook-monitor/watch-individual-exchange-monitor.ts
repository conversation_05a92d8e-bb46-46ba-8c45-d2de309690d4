import * as ccxt from 'ccxt';
import { BaseOrderbookMonitor } from './base-orderbook-monitor';
import { logger } from '../utils/pinno-logger';
import { MonitoredRequest } from '../core/monitored-request';
import { ExchangeHelper } from '../utils/exchange-helper';
import { getConnectionsHelper } from '../utils/exchange-connections-helpers';
import { LINQ } from '../utils/linq';
import WsClient from 'ccxt/js/src/base/ws/WsClient';
import { getWatchHelper } from '../utils/exchange-watch-sheduler';
import { MonitoredSymbolsRegistry } from '../utils/monitored-symbols-registry';
import { exchanges } from '../core/exchanges-provider';

export class WatchIndividualExchangeMonitor extends BaseOrderbookMonitor {
  private static idCounter = 1;

  id: number = WatchIndividualExchangeMonitor.idCounter++;
  runningLoops = new Map<string, boolean>();
  onMonitoringFinish: () => void;

  async initialize(): Promise<void> {
    await super.initialize();
    if (this.configs.individualMonitorLoopMethod === 'closeExchangeLoop') {
      // coinbase only allows 30 symbols per connection and only 1 connection.
      // so we need to create a new exchange instance for each group.
      this.exchange = await exchanges.createExchange(this.exchangeName);
    }

    getConnectionsHelper(this.exchange).onClosingConnectionForSymbols.push((toCloseSubscriptions: string[][]) => {
      for (const subscriptions of toCloseSubscriptions) {
        if (subscriptions.length > 2) { // check only connection with no more than 2 symbols
          continue;
        }
        if (LINQ.any(subscriptions, (symbol) => this.hasSubscribers(symbol))) {
          logger.warn(`[${subscriptions.join(', ')}] has subscribers in individual monitor`);
          return false; // veto
        }
      }

      // logger.info(`Closing connection is safe for: [${symbols.join(', ')}]`);
      return true; // allow
    });
  }

  override subscribe(item: MonitoredRequest): boolean {
    const result = super.subscribe(item);
    if (!result) {
      return false;
    }

    if (!this.runningLoops.get(item.symbols)) {
      this.runningLoops.set(item.symbols, true);
      this.loop(item.symbols)
        .catch((error) => logger.error(error, 'WatchIndividualExchangeMonitor symbolLoop error:'))
        .finally(() => {
          this.runningLoops.delete(item.symbols);
          if (this.runningLoops.size === 0 && this.onMonitoringFinish != undefined) {
            this.onMonitoringFinish();
          }
        });
    }

    return result;
  }

  private loop: (symbol: string) => Promise<void> = this.getLoopMethod().bind(this);
  private getLoopMethod(): (symbol: string) => Promise<void> {
    const methodName = this.configs.individualMonitorLoopMethod
    logger.info(`[${this.exchangeName}] WatchIndividualExchangeMonitor '${this.id}' use '${methodName}'`);
    return this[methodName] as (symbol: string) => Promise<void>;
    //
    // if (this.exchange.has['unWatchOrderBook']) {
    //   logger.info(`[${this.exchangeName}] WatchIndividualExchangeMonitor '${this.id}' use 'normalUnwatchLoop'`);
    //   return this.normalUnwatchLoop;
    // }
    //
    // if (ExchangeHelper.exchangeUsesMultipleClients(this.exchange)) {
    //   logger.info(`[${this.exchangeName}] WatchIndividualExchangeMonitor '${this.id}' use 'closeConnectionLoop'`);
    //   return this.closeConnectionLoop;
    // }
    //
    // if (ExchangeHelper.canForceUnWatchOrderBook(this.exchange)) {
    //   logger.info(`[${this.exchangeName}] WatchIndividualExchangeMonitor '${this.id}' use 'forceUnwatchLoop'`);
    //   return this.forceUnwatchLoop;
    // }
    //
    // logger.info(`[${this.exchangeName}] WatchIndividualExchangeMonitor '${this.id}' use 'closeExchangeLoop'`);
    // return this.closeExchangeLoop;
  }

  private async normalUnwatchLoop(symbol: string): Promise<void> {
    const symbols = [symbol];
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    await getWatchHelper(this.exchange).startWatchSpreadDelay(symbol);
    while (this.hasSubscribers(symbol)) {
      const orderbook = await this.watchSymbol(symbol);
    }
    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);

    await this.unwatchSymbol(symbol);
    this.removeSymbolWithoutSubscribers(symbol);
  }

  private async closeConnectionLoop(symbol: string): Promise<void> {
    const symbols = [symbol];
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    const beforeEstablishedConnectionClients = LINQ.toArray(Object.values(this.exchange.clients));

    // first update
    let orderbook: ccxt.OrderBook | Error = new Error('not successful start watching');
    while (orderbook instanceof Error) {
      orderbook = await this.watchSymbol(symbol);
    }

    const connectionHelper = ExchangeHelper.getConnectionsHelper(this.exchange);
    let client = connectionHelper.findClientServingSymbols(symbols, beforeEstablishedConnectionClients);
    if (client) {
      connectionHelper.stopClosingConnection(client, symbols);
    } else {
      throw new Error(`WatchIndividualExchangeMonitor failed to find a client responsible for '${symbol}'`);
    }

    while (this.hasSubscribers(symbol)) {
      const orderbook = await this.watchSymbol(symbol);
    }
    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);
    await this.closeConnectionForSymbol(client, symbol);
  }

  private async forceUnwatchLoop(symbol: string): Promise<void> {
    const symbols = [symbol];
    logger.info(`'${symbol}' forceUnwatchLoop 1`);
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    logger.info(`'${symbol}' forceUnwatchLoop 2`);
    await getWatchHelper(this.exchange).startWatchSpreadDelay(symbol);
    logger.info(`'${symbol}' forceUnwatchLoop 3`);
    while (this.hasSubscribers(symbol)) {
      const orderbook = await this.watchSymbol(symbol);
    }
    logger.info(`'${symbol}' forceUnwatchLoop 4`);
    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);
    logger.info(`'${symbol}' forceUnwatchLoop 5`);
    
    if (!MonitoredSymbolsRegistry.isMonitored(this.exchange, symbol)) {
      logger.info(`'${symbol}' forceUnwatchLoop 6`);
      await this.unwatchSymbol(symbol);
      logger.info(`'${symbol}' forceUnwatchLoop 7`);
    }
    this.removeSymbolWithoutSubscribers(symbol);
    logger.info(`'${symbol}' forceUnwatchLoop 8`);
  }

  private async closeExchangeLoop(symbol: string): Promise<void> {
    const symbols = [symbol];
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    while (this.hasSubscribers()) {
      const orderbook = await this.watchSymbol(symbol);
    }
    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);

    // mark as unwatched, because either way the corresponding 
    // client (ws connection) will be closed now or later.
    // closing client fails in case it is shared between several symbols.
    this.removeSymbolWithoutSubscribers(symbol);

    logger.info(`Closing exchange for individual monitor (${this.id})`);
    await this.exchange.close();
  }

  private async watchSymbol(symbol: string): Promise<ccxt.OrderBook | Error> {
    try {
      const orderbook = await this.exchange.watchOrderBook(symbol);
      this.notifyUpdateSymbol(symbol, orderbook);
      return orderbook;
    } catch (error) {
      // if (error instanceof ExchangeClosedByUser) {
      //   // the underlying connection was closed, we should not watch these symbols anymore
      //   logger.warn(`WatchIndividualExchangeMonitor (${symbol}) connection closed, but we're still watching it`);
      //   return error;
      // }

      if (error instanceof ccxt.ChecksumError) {
        // ChecksumError is a typical error, no need to log it. 
        // Usually it signalizes that server has high trading activity at the moment, 
        // and our app cannot keep up with updates during subscription phase, 
        // or node server is overloaded and cannot process updates fast enough.
        // Either way, our retry logic will handle it.
        logger.warn(`WatchIndividualExchangeMonitor 'watchSymbol' ${symbol} ChecksumError.`);
      } else {
        logger.error(error, `WatchIndividualExchangeMonitor 'watchSymbol' ${symbol} error:`);
      }

      this.notifyUpdateSymbol(symbol, error);

      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return error;
    }
  }

  private async unwatchSymbol(symbol: string): Promise<boolean> {
    try {
      // For bitget, this will throw UnsubscribeError on success, which is caught below.
      const result = await this.exchange.unWatchOrderBook(symbol);
      if (typeof result === 'boolean' && !result) {
        logger.warn(`WatchIndividualExchangeMonitor. Failed to unwatch '${symbol}'`);
        return false;
      }

      logger.info(`WatchIndividualExchangeMonitor. Unwatched '${symbol}'`);

      if (symbol in this.exchange.orderbooks) {
        delete this.exchange.orderbooks[symbol];
        logger.info(`Manually deleted orderbook cache for ${symbol}.`);
      }

      const helper =getConnectionsHelper(this.exchange);
      const clients = Object.values(this.exchange.clients || {});
      for (const client of clients) {
        // @ts-ignore
        if (client && client.subscriptions && subMessageHash in client.subscriptions) {
          // @ts-ignore
          delete client.subscriptions[subMessageHash];
          logger.info(`Manually deleted subscription future for ${symbol} from client.`);
        }
      }

      // only mark it as unwatched when it's not monitored.
      // unwatch failing is really an edge case.
      this.removeSymbolWithoutSubscribers(symbol);
      return true;
    } catch (error) {
      logger.error(error, `WatchIndividualExchangeMonitor 'unwatchSymbol' '${symbol}' error:`);
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }

  private async closeConnectionForSymbol(client: WsClient, symbol: string): Promise<boolean> {
    // mark it as unwatched, because either way the corresponding 
    // client (ws connection) will be closed now or later.
    // closing client fails in case it is shared between several symbols.
    this.removeSymbolWithoutSubscribers(symbol);

    try {
      const symbols = [symbol];
      return await ExchangeHelper.getConnectionsHelper(this.exchange).tryToCloseConnection(client, symbols);
    } catch (error) {
      logger.error(error, `WatchIndividualExchangeMonitor 'closeConnectionForSymbol' ${symbol} error:`);
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }
}
