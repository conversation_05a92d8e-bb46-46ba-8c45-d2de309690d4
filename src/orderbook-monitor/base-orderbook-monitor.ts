import * as ccxt from 'ccxt';
import { logger } from '../utils/pinno-logger';
import { MonitoredRequest } from '../core/monitored-request';
import { orderbookCache } from '../core/orderbook-cache';
import { updateNotifier } from '../core/orderbook-update-notifier';
import { exchanges } from '../core/exchanges-provider';

export class BaseOrderbookMonitor {
  exchangeName: string;
  exchange: ccxt.Exchange;
  protected symbolSubscribers = new Map<string, Set<MonitoredRequest>>();

  constructor(exchangeName: string) {
    this.exchangeName = exchangeName;
    this.exchange = exchanges.get(exchangeName);
  }
  
  public async initialize(): Promise<void> {
    return ;
  }
  
  public subscribe(item: MonitoredRequest): boolean {
    let subscribers = this.symbolSubscribers.get(item.symbols);
    if (!subscribers) {
      subscribers = new Set<MonitoredRequest>();
      this.symbolSubscribers.set(item.symbols, subscribers);
    }

    if (subscribers.has(item)) {
      return false;
    }

    subscribers.add(item);
    return true;
  }

  public unsubscribe(item: MonitoredRequest): boolean {
    const subscribers = this.symbolSubscribers.get(item.symbols);
    if (!subscribers) {
      return false;
    }

    return subscribers.delete(item);
  }

  // CAUTION! it can be monitored even if there are no subscribers, but monitoring 
  // finishing is still in the process, so you might still receive updates.
  public isMonitored(symbol: string): boolean {
    return this.symbolSubscribers.get(symbol) !== undefined;
  }

  public hasSubscribers(symbol?: string): boolean {
    if(symbol === undefined){
      for (const subscribers of this.symbolSubscribers.values()){
        if(subscribers.size > 0){
          return true;
        }
      }
      return false;
    }
    
    return this.symbolSubscribers.get(symbol)?.size > 0;
  }

  public *getSubscribers(symbol: string): IterableIterator<MonitoredRequest> {
    const set = this.symbolSubscribers.get(symbol);
    if(!set){
      return;
    }
    
    for(const request of set){
      yield request;
    }
  }
  
  public getMonitoredSymbols(): IterableIterator<string> {
    return this.symbolSubscribers.keys();
  }

  protected removeSymbolWithoutSubscribers(symbol: string) {
    const subscribers = this.symbolSubscribers.get(symbol);
    if (subscribers && subscribers.size === 0) {
      this.symbolSubscribers.delete(symbol);
    }
  }

  public notifyUpdateSymbol(symbol: string, orderbook: ccxt.OrderBook | Error) {
    orderbookCache.updateRecord(this.exchange.id, symbol, orderbook);

    const subscribers = this.symbolSubscribers.get(symbol);
    if (subscribers) { // notify monitored requests if there are any still
      for (const monitoredRequest of subscribers) {
        monitoredRequest.updateData(orderbook);
      }
    }

    updateNotifier.notify(this.exchange.id, symbol, orderbook);
  }

  protected errorToDelay(error: Error) {
    if (error instanceof ccxt.ChecksumError) {
      return 1000;
    }
    
    if (error instanceof ccxt.OperationFailed) {
      return 3000;
    }

    if (error instanceof ccxt.ExchangeError) {
      return 10_000;
    }

    return 3000;
  }
}