import { logger, featureLogger } from './utils/pinno-logger';

import * as dotenv from 'dotenv';
dotenv.config();
logger.info(`env: ${process.env.NODE_ENV}`);

import * as ccxt from 'ccxt';
logger.info(`CCXT version: ${ccxt.version}`);

import * as os from 'node:os';
import http, { IncomingMessage, ServerResponse } from 'node:http';

import { LINQ } from './utils/linq';
import { getAppPerformanceMetrics } from './utils/app-performance';
const requestTrackingLogger = featureLogger('requestTracking');
import { exchanges } from './core/exchanges-provider';
import { monitoring } from './core/orderbook-monitoring-aggregator';
import { MonitoredRequest } from './core/monitored-request';
import { MonitoringResponse } from './core/monitoring-response';
import { requestExecutionTracking, RequestTracker } from './utils/request-tracking';
import { orderbookCache } from './core/orderbook-cache';


const port = process.env.PORT || 3001;

enum RequestStages {
  STARTED = 'STARTED',
  PREVIOUS_RESPONSE = 'PREVIOUS_RESPONSE',
  INITIALIZATION = 'INITIALIZATION',
  CACHED_RESPONSE = 'CACHED_RESPONSE',
  WAITING_FOR_FIRST_UPDATE = 'WAITING_FOR_FIRST_UPDATE',
  OK_RESPONSE = 'OK_RESPONSE',
  ERROR_RESPONSE = 'ERROR_RESPONSE',
  FATAL_ERROR_RESPONSE = 'FATAL_ERROR_RESPONSE',
  COMPLETED = 'COMPLETED'
}

setInterval(async () => {
  const { appUtilization, eventLoopUtilization, p99Delay, avg5EventLoopLag } = await getAppPerformanceMetrics();
  if (appUtilization > 60) {
    logger.warn(`High app utilization: ${appUtilization}%! eventLoopUtilization:${eventLoopUtilization}% p99Delay:${p99Delay}ms avgLag:${avg5EventLoopLag}ms`);
  }
}, 30_000);

async function main() {
  await exchanges.createAllExchanges();
  // await exchanges.getOrCreate('binance');
  await monitoring.initialize(exchanges);

  // print currently monitored (updated) symbols
  if (process.env.NODE_ENV !== 'production') {
    setInterval(printMonitoredSymbols, 5000);
  }

  const server = http.createServer(async (req: IncomingMessage, res: ServerResponse) => {
    if (await tryProcessDebugRequest(req, res)) {
      return;
    }

    // decline '/favicon.ico' requests
    if (req.url.length === 12 && req.url.codePointAt(1) === 102) { // 'f' in '/favicon.ico'
      res.writeHead(204); // No Content response (minimal processing)
      res.end();
      return;
    }

    // Create request tracker
    const tracker = requestExecutionTracking.createTracker(req.url);
    try {
      const parsedRequest = await parse(req);
      if (parsedRequest.lastRequestTime) {
        const newPeriod = Date.now() - parsedRequest.lastRequestTime;
        parsedRequest.emaRequestPeriod = MonitoredRequest.EMA_ALPHA * newPeriod +
          (1 - MonitoredRequest.EMA_ALPHA) * parsedRequest.emaRequestPeriod;
        // logger.info(`[${parsedRequest.cacheKey}] newPeriod: ${newPeriod}ms, emaRequestPeriod: ${parsedRequest.emaRequestPeriod}ms`);
      }

      parsedRequest.lastRequestTime = Date.now();

      if (isPrevResponseStillRelevant(parsedRequest, parsedRequest.prevResponse)) {
        tracker.updateStage(RequestStages.PREVIOUS_RESPONSE);
        tracker.updateStage(RequestStages.CACHED_RESPONSE);
        if (parsedRequest.prevResponse.status === 200) {
          tracker.updateStage(RequestStages.OK_RESPONSE);
        } else {
          tracker.updateStage(RequestStages.ERROR_RESPONSE);
        }
        parsedRequest.prevResponse.writeToResponse(res);
        const processTime = (Date.now() - tracker.startTime) / 1000;
        requestTrackingLogger.info(`[${req.url}] ${processTime}s return old ${parsedRequest.prevResponse.text}`);
        requestExecutionTracking.completeTracking(tracker);
        return;
      }

      if (!parsedRequest.symbols) {
        tracker.updateStage(RequestStages.INITIALIZATION);

        const wasTheseSymbolsMonitored = parsedRequest.initialize(); // start monitoring
        if (wasTheseSymbolsMonitored) {
          // Some orderbooks have very low trading volatility (nobody trades them), 
          // so pause between updates might be longer than 30s.
          // Here we don't check lastUpdateTime, because as long as we actively monitor the data,
          // we assume that we have the most recent.
          const orderbookRecord = orderbookCache.getRecord(parsedRequest.exchangeName, parsedRequest.symbols);
          if (orderbookRecord) {
            tracker.updateStage(RequestStages.CACHED_RESPONSE);
            parsedRequest.updateData(orderbookRecord.orderbook, orderbookRecord.lastUpdate);
          }
        }
      }

      if (parsedRequest.lastUpdateTime === undefined && !parsedRequest.error) {
        tracker.updateStage(RequestStages.WAITING_FOR_FIRST_UPDATE);
        // wait till the first update happens
        await parsedRequest.waitNextUpdate();
      }

      const newResponse = parsedRequest.buildResponse();
      if (newResponse.status === 200) {
        tracker.updateStage(RequestStages.OK_RESPONSE);
      } else {
        tracker.updateStage(RequestStages.ERROR_RESPONSE);
      }
      newResponse.writeToResponse(res);
      parsedRequest.prevResponse = newResponse;
      const processTime = (Date.now() - tracker.startTime) / 1000;
      requestTrackingLogger.info(`${req.url} ${processTime}s return new ${parsedRequest.prevResponse.text}`);
      requestExecutionTracking.completeTracking(tracker);

    } catch (error) {
      tracker.updateStage(RequestStages.FATAL_ERROR_RESPONSE);
      logger.error(error, `UNCAUGHT EXCEPTION:`);
      res.setHeader('Content-Type', 'application/json');
      res.writeHead(500);
      res.end('Internal Server Error');
      requestExecutionTracking.completeTracking(tracker);
    }
  });

  // keep connection open, to avoid exhausting cpu on each request connection reopening 
  server.keepAliveTimeout = 10_000;
  server.headersTimeout = server.keepAliveTimeout + 1000;

  server.listen(port, () => {
    logger.info(`Node.js HTTP server listening on port ${port}. Try visiting:`);
    logger.info(`http://localhost:${port}/binance/ETH:USDT:2:ETH`);
  });
}

main().catch((error) =>
  logger.error(error, `FINAL ERROR:`)
);

const cachedRequests = new Map<string, MonitoredRequest>();

async function parse(request: IncomingMessage): Promise<MonitoredRequest> {
  //binance/BTC:USdT:100:BTC
  const cacheKey = request.url;
  requestTrackingLogger.info(`[${cacheKey}] Starting parse operation`);

  let cachedRequest = cachedRequests.get(cacheKey);
  if (cachedRequest) {
    // was parsed, return previous result
    requestTrackingLogger.info(`[${cacheKey}] Returning cached request`);
    return cachedRequest;
  }

  requestTrackingLogger.info(`[${cacheKey}] Creating new MonitorRequest`);
  cachedRequest = new MonitoredRequest(cacheKey);
  cachedRequests.set(cacheKey, cachedRequest);

  try {
    requestTrackingLogger.info(`[${cacheKey}] Parsing URL components`);
    let [, exchangeName, pairParam] = cacheKey.split('/');
    exchangeName = exchangeName.toLowerCase();
    pairParam = pairParam.toUpperCase();

    let [symbol1, symbol2, volumeStr, volumeSymbol] = pairParam.split(':');
    if ((!exchangeName || !symbol1 || !symbol2)) {
      requestTrackingLogger.warn(`[${cacheKey}] Invalid URL format - missing required components`);
      cachedRequest.markAsUnSupported(new ccxt.BadRequest('Invalid URL. Should be \'/exchange/symbol1:symbol2:<volume>:<volumeSymbol>\'')); // More concise error handling
      return cachedRequest;
    }

    let volume: number = undefined;
    if (volumeStr) {
      if (!volumeSymbol) {
        cachedRequest.markAsUnSupported(new ccxt.BadRequest('Invalid URL. Should be \'/exchange/symbol1:symbol2:<volume>:<volumeSymbol>\'')); // More concise error handling
        return cachedRequest;
      } else if (volumeSymbol !== symbol1 && volumeSymbol !== symbol2) {
        cachedRequest.markAsUnSupported(new ccxt.BadRequest('Invalid URL. \'volumeSymbol\' must be either of the pair\'s symbols type\''));
        return cachedRequest;
      }

      volume = Number.parseFloat(volumeStr);
      if (Number.isNaN(volume)) {
        cachedRequest.markAsUnSupported(new ccxt.BadRequest('Invalid URL. \'volume\' must be a number')); // More concise error handling
        return cachedRequest;
      }
    }

    cachedRequest.setParsedValues(exchangeName, symbol1, symbol2, volume, volumeSymbol);
  } catch {
    cachedRequest.markAsUnSupported(new ccxt.BadRequest('Invalid URL. Should be \'/exchange/symbol1:symbol2:<volume>:<volumeSymbol>\'')); // More concise error handling
    return cachedRequest;
  }

  return cachedRequest;
}

function isPrevResponseStillRelevant(request: MonitoredRequest, prevResponse: MonitoringResponse): boolean {
  if (!prevResponse) {
    return false;
  }

  if (prevResponse.lastUpdateTime === request.lastUpdateTime) {
    // nothing is changed since prev request, so give back the same response.
    return true;
  }

  return false;
}


const cleanupInterval = 1000 * 30;
setInterval(removeUnusedRequests, cleanupInterval);
let prevRemoveTime = Date.now();

function removeUnusedRequests() {
  if (cachedRequests.size === 0) {
    return;
  }

  let removed = 0;
  logger.info(`removeUnusedRequests`);
  for (const [, request] of cachedRequests) {
    if (request.lastRequestTime < prevRemoveTime) {
      const tracker = requestExecutionTracking.getTracker(request.cacheKey);
      // if (tracker) {
      //   logger.info(`cleanup '${request.cacheKey}' lastRequestTime:${Date.now() - request.lastRequestTime}ms, currentStage:${tracker?.currentStage}, ${Date.now() - tracker?.stageStartTime}ms`);
      // } else {
      //   logger.info(`cleanup '${request.cacheKey}' lastRequestTime:${Date.now() - request.lastRequestTime}ms`);
      // }

      request.cleanup();
      cachedRequests.delete(request.cacheKey);
      removed++;
    }
  }
  logger.info(`removeUnusedRequests: ${removed}`);
  prevRemoveTime = Date.now();
}

async function tryProcessDebugRequest(req: IncomingMessage, res: ServerResponse): Promise<boolean> {
  if (req.url === '/getRequestTrackers') {
    res.setHeader('Content-Type', 'application/json');
    res.writeHead(200);
    res.end(JSON.stringify({
      active: requestExecutionTracking.activeRequests,
      deleted: requestExecutionTracking.completedRequests
    }));
    requestExecutionTracking.completedRequests.length = 0;
    return true;
  }

  if (req.url === '/removeUnusedRequests') {
    removeUnusedRequests();
    res.writeHead(204); // No Content response (minimal processing)
    res.end();
    return true;
  }


  return false;
}

const previousCompleteTrackers: RequestTracker[] = [];

function printMonitoredSymbols() {
  let monitoredRequests = 0;
  for (const request of cachedRequests.values()) {
    if (request.symbols !== 'unsupported') {
      monitoredRequests++;
    }
  }

  let firstUpdateCount = 0;
  for (const tracker of requestExecutionTracking.activeRequests.values()) {
    if (tracker.currentStage === RequestStages.WAITING_FOR_FIRST_UPDATE) {
      firstUpdateCount++;
    }
  }

  const { symbolTimeSinceUpdate, count, min, max, avg } = getMonitoredLastUpdateStats();
  // const formattedEntries = symbolTimeSinceUpdate.join(', ');
  // const lastUpdateStats = { count, min, max, avg };
  // const allRequestsStats = getCompletedRequestsStats(tracker => true);
  // const cachedResponseStats = getCompletedRequestsStats(tracker =>
  //   tracker.stageMetrics.some(metric => metric.stage === RequestStages.CACHED_RESPONSE));
  // const firstUpdateStats = getCompletedRequestsStats(tracker =>
  //   tracker.stageMetrics.some(metric => metric.stage === RequestStages.WAITING_FOR_FIRST_UPDATE));

  // const newResponseStats = getCompletedRequestsStats(tracker =>
  //   !previousCompleteTrackers.includes(tracker)
  // );
  //
  // const newFirstUpdateStats = getCompletedRequestsStats(tracker =>
  //   !previousCompleteTrackers.includes(tracker)
  //   && tracker.stageMetrics.some(metric => metric.stage === RequestStages.WAITING_FOR_FIRST_UPDATE));
  //
  // const newCachedResponseStats = getCompletedRequestsStats(tracker =>
  //   !previousCompleteTrackers.includes(tracker)
  //   && tracker.stageMetrics.some(metric => metric.stage === RequestStages.CACHED_RESPONSE));
  //
  // const newOkRequestsStats = getCompletedRequestsStats(tracker =>
  //   !previousCompleteTrackers.includes(tracker)
  //   && !tracker.stageMetrics.some(metric => metric.stage === RequestStages.WAITING_FOR_FIRST_UPDATE)
  //   && tracker.stageMetrics.some(metric => metric.stage === RequestStages.OK_RESPONSE));
  //
  // const newErrorRequestsStats = getCompletedRequestsStats(tracker =>
  //   !previousCompleteTrackers.includes(tracker)
  //   && tracker.stageMetrics.some(metric => metric.stage === RequestStages.ERROR_RESPONSE));

  previousCompleteTrackers.length = 0;
  // use for...of instead of ... 
  for (const tracker of requestExecutionTracking.completedRequests) {
    previousCompleteTrackers.push(tracker);
  }

  const firstExchange = exchanges.all().values().next().value;

  if (cachedRequests.size === 0
    && count === 0
    && Object.keys(firstExchange.clients).length === 0
  ) {
    // do not override previous logs with empty data
    return;
  }

  // logger.info(`
  // allCompletedRequestsStats: ${JSON.stringify(allRequestsStats)}
  // allCompletedFirstUpdateStats: ${JSON.stringify(firstUpdateStats)}
  // allCompletedCachedResponseStats: ${JSON.stringify(cachedResponseStats)}
  // correctMonitoredRequests: ${monitoredRequests}/${cachedRequests.size}, inFirstUpdateState: ${firstUpdateCount}, uniqueMonitoredSymbols: ${symbolTimeSinceUpdate.length} 
  // newFirstUpdateStats: ${JSON.stringify(newFirstUpdateStats)}
  // newOkRequestsStats: ${JSON.stringify(newOkRequestsStats)}
  // newErrorRequestsStats: ${JSON.stringify(newErrorRequestsStats)}
  // lastUpdateStatsForUniqueMonitoredSymbols: ${JSON.stringify(lastUpdateStats)}
  // monitoredSymbols: ${symbolTimeSinceUpdate.length}${os.EOL}${formattedEntries}`);

  logger.info(`
  correctMonitoredRequests: ${monitoredRequests}/${cachedRequests.size}, inFirstUpdateState: ${firstUpdateCount}, uniqueMonitoredSymbols: ${symbolTimeSinceUpdate.length} 
  clients:${Object.keys(firstExchange.clients).length}
  symbols: ${monitoring.getOrderbookMonitor(firstExchange.id).toString()}
  `);
}

function getMonitoredLastUpdateStats(): {
  symbolTimeSinceUpdate: string[],
  count: number,
  min: number,
  max: number,
  avg: number
} {
  // 1. Gather raw stats
  const stats: { label: string, time: number }[] = [];

  for (const exchangeName of exchanges.all().keys()) {
    const monitor = monitoring.getOrderbookMonitor(exchangeName);
    if (!monitor) continue;

    for (const symbol of monitor.getMonitoredSymbols()) {
      const record = orderbookCache.getRecord(monitor.exchange.id, symbol);
      const time = (record) ? (Date.now() - record.lastUpdate) : -1;
      const label = `${monitor.exchange.id}/${symbol}`;
      stats.push({ label, time });
    }
  }

  // 2. Compute metrics via LINQ
  // If stats is empty, min/max/avg default to 0
  const { count, min = 0, max = 0, avg = 0 } = LINQ.minMaxAvg(stats, s => s.time);

  // 3. Build and sort the output strings
  const symbolTimeSinceUpdate = stats
    .map(s => `${s.label}:${s.time}ms`)
    .sort();

  return { symbolTimeSinceUpdate, count, min, max, avg };
}

// use lambda for filtering by stage 
function getCompletedRequestsStats(selector: (tracker: RequestTracker) => boolean): {
  count: number,
  min: number,
  max: number,
  avg: number
} {
  const { count, min = 0, max = 0, avg = 0 } =
    LINQ.minMaxAvg(LINQ.where(requestExecutionTracking.completedRequests, selector),
      tracker => tracker.endTime - tracker.startTime);

  return { count, min, max, avg };
}