import { MonitoringResponse } from './monitoring-response';
import { logger } from '../utils/pinno-logger';
import { exchanges, ExchangesProvider } from './exchanges-provider';
import * as ccxt from 'ccxt';
import { monitoring } from './orderbook-monitoring-aggregator';
import { CancelToken } from '@esfx/canceltoken';
import { updateNotifier } from './orderbook-update-notifier';
import { ExchangeHelper } from '../utils/exchange-helper';

export class MonitoredRequest {
  public static EMA_ALPHA: number = 0.3;
  cacheKey: string;

  // main params
  createTime: number;
  exchangeName: string;
  symbol1: string;
  symbol2: string;
  lastRequestTime: number;
  // we assume request will be updated every 15 seconds by default
  emaRequestPeriod: number = 15_000;

  // optional params
  volume: number | undefined;
  volumeSymbol: string | undefined;
  volumeIsInSymbol1: boolean | undefined;

  // calculated
  symbols: string | 'unsupported'; // "symbol1/symbol2"

  // updated every time the orderbook is updated
  buy: number | undefined = undefined;
  sell: number | undefined = undefined;
  error: Error | undefined = undefined;
  lastUpdateTime: number = undefined;

  prevResponse: MonitoringResponse;

  constructor(cacheKey: string) {
    this.cacheKey = cacheKey;
    this.createTime = Date.now();

    // hack to make the subscribe\unsubscribe work same way as in c#
    this.updateData = this.updateData.bind(this);
  }

  public setParsedValues(
    exchangeName: string,
    symbol1: string,
    symbol2: string,
    volume: number | undefined,
    volumeSymbol: string | undefined) {
    this.exchangeName = exchangeName;
    this.symbol1 = symbol1;
    this.symbol2 = symbol2;
    this.volume = volume;
    this.volumeSymbol = volumeSymbol;
    if (this.volumeSymbol) {
      this.volumeIsInSymbol1 = this.volumeSymbol === this.symbol1;
    }
  }

  public initialize(): boolean {
    logger.debug(`[${this.cacheKey}] Starting initialization`);

    const exchange = exchanges.get(this.exchangeName);
    if (!exchange) {
      logger.warn(`[${this.cacheKey}] '${this.exchangeName}' is not supported`);
      const error = new ccxt.NotSupported(`'${this.exchangeName}' is not supported.`);
      this.markAsUnSupported(error);
      return false;
    }

    if (!this.symbols) {
      logger.debug(`[${this.cacheKey}] Finding pair name for ${this.symbol1}/${this.symbol2}`);
      const symbols = ExchangeHelper.findSupportedSymbols(exchange, this.symbol1, this.symbol2);
      if (!symbols) {
        logger.warn(`[${this.cacheKey}] ${this.symbol1}/${this.symbol2} is not supported on '${exchange.id}'`);
        const error = new ccxt.NotSupported(`'${exchange.id}' doesn't support ${this.symbol1}/${this.symbol2} pair.`);
        this.markAsUnSupported(error);
        return false;
      }

      this.symbols = symbols;
    }

    const wasTheseSymbolsMonitored = monitoring.start(this);
    logger.debug(`[${this.cacheKey}] Initialization completed`);
    return wasTheseSymbolsMonitored;
  }

  public cleanup() {
    if (this.symbols !== 'unsupported') {
      monitoring.stop(this);
    }

    if (this.nextUpdateCS) {
      this.nextUpdateCS.cancel();
      this.nextUpdateCS = undefined;
    }
  }

  private nextUpdateCS: { cancel(): void; token: CancelToken } | undefined;
  private nextUpdatePromise: Promise<ccxt.OrderBook | Error> | undefined;
  public async waitNextUpdate(): Promise<ccxt.OrderBook | Error> {
    // Cancel any existing request
    if (this.nextUpdatePromise) {
      return this.nextUpdatePromise;
    }
    
    // Create new cancellation source and start waiting for update
    this.nextUpdateCS = this.nextUpdateCS = CancelToken.source([
      CancelToken.timeout(60_000, new Error(`[${this.cacheKey}] Timeout waiting for first update`))
    ]);
    this.nextUpdatePromise = updateNotifier.waitNextUpdate(this.exchangeName, this.symbols, this.nextUpdateCS.token)
      .then(result => {
        // Clean up on completion
        this.nextUpdateCS = undefined;
        this.nextUpdatePromise = undefined;
        return result;
      })
      .catch(error => {
        // Clean up on error
        this.nextUpdateCS = undefined;
        this.nextUpdatePromise = undefined;
        // Return error as resolved value to match the expected return type
        return error;
      });

    return this.nextUpdatePromise;
  }

  public markAsUnSupported(error: ccxt.ExchangeError) {
    this.symbols = 'unsupported';
    this.error = error;
    this.lastUpdateTime = Date.now();
  }

  public updateData(orderbook: ccxt.OrderBook | Error, lastUpdateTime?: number) {
    this.lastUpdateTime = lastUpdateTime ?? Date.now();

    if (orderbook instanceof Error) {
      this.error = orderbook;
      return;
    }

    this.error = undefined;

    if (!this.volume) {
      // If no volume is specified, return the best bid and ask prices
      // BTC:USDT
      // user can buy some BTC at 'this.buy' price
      this.buy = orderbook.asks[0][0];
      // user can sell some BTC at 'this.sell' price
      this.sell = orderbook.bids[0][0];
      return;
    }

    if (this.volumeIsInSymbol1) {
      // User wants to buy/sell a specific amount of token1 (e.g., BTC)
      // BTC:USDT:2:BTC 
      // user can spend 'this.buy' of USDT to buy each of 2 BTC
      this.buy = this.calculatePriceInSymbol1(orderbook.asks, this.volume);
      // user can get 'this.sell' of USDT for selling each of 2 BTC
      this.sell = this.calculatePriceInSymbol1(orderbook.bids, this.volume);
    } else {
      // User wants to spend/receive a specific amount of token2 (e.g., USDT)
      // BTC:USDT:1500:USDT
      // user can buy 'this.buy' of BTC for 1500 USDT
      this.buy = this.calculatePriceInSymbol2(orderbook.asks, this.volume);
      // user can sell 'this.sell' of BTC for 1500 USDT
      this.sell = this.calculatePriceInSymbol2(orderbook.bids, this.volume);
    }
  }

  public calculatePriceInSymbol1(
    entries: [ccxt.Num, ccxt.Num][],
    volume: number
  ): number {
    let totalCost = 0;
    let remaining = volume;

    for (const [orderPrice, availableAmount] of entries) {
      const usedAmount = Math.min(remaining, availableAmount);

      totalCost += usedAmount * orderPrice;
      remaining -= usedAmount;

      if (remaining <= 0) {
        break;
      }
    }
    return remaining > 0 ? -1 : totalCost / volume;
  }

  public calculatePriceInSymbol2(
    entries: [ccxt.Num, ccxt.Num][],
    volume: number
  ): number {
    let totalTokens = 0;
    let remaining = volume;

    for (const [orderPrice, availableAmount] of entries) {
      const amountToBuy = Math.min(remaining / orderPrice, availableAmount);

      totalTokens += amountToBuy;
      remaining -= amountToBuy * orderPrice;

      if (remaining <= 0) {
        break;
      }
    }

    return remaining > 0 ? -1 : totalTokens;
  }

  public buildResponse(): MonitoringResponse {
    logger.debug(`[${this.cacheKey}] Building response`);

    if (this.error) {
      logger.debug(`[${this.cacheKey}] Building error response: ${this.error.message}`);
      if (this.error instanceof ccxt.ExchangeError) {
        // 400 Bad Request
        return MonitoringResponse.failed(this.error, this.lastUpdateTime, 400);
      }

      // 500 Internal Server Error
      return MonitoringResponse.failed(this.error, this.lastUpdateTime, 500);
    }

    const result = {
      buy: this.buy,
      sell: this.sell
    };

    const text = JSON.stringify(result);
    logger.debug(`[${this.cacheKey}] Built successful response: ${text}`);
    return MonitoringResponse.successful(text, this.lastUpdateTime);
  }
}
