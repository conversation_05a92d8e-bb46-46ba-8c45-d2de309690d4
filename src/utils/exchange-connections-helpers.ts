import * as ccxt from 'ccxt';
import WsClient from 'ccxt/js/src/base/ws/WsClient';
import { ExchangeClosedByUser } from 'ccxt';
import { logger } from './pinno-logger';
import { LINQ } from './linq';
import { ExchangeHelper } from './exchange-helper';

const exchangeClientsHelpers = new Map<ccxt.Exchange, ExchangeConnectionsHelper>();

export function getConnectionsHelper(exchange: ccxt.Exchange): ExchangeConnectionsHelper {
  let result = exchangeClientsHelpers.get(exchange);
  if (!result) {
    result = new ExchangeConnectionsHelper(exchange);
    exchangeClientsHelpers.set(exchange, result);
  }

  return result;
}

class ExchangeConnectionsHelper {
  private exchange: ccxt.Exchange;
  onClosingConnectionForSymbols: ((toCloseSubscriptions: string[][]) => boolean | undefined)[] = [];

  constructor(exchange: ccxt.Exchange) {
    this.exchange = exchange;
  }

  public findClientServingSymbols(symbols: string[], beforeEstablishedConnectionClients: WsClient[]): WsClient {
    const newClients = LINQ.toArray(LINQ.except(Object.values(this.exchange.clients), beforeEstablishedConnectionClients));

    if (newClients.length > 0) {
      // some new connection were created during the first update.
      // quite possibly that one of them is serving this symbols.
      // let's try to find it.
      let clientAndSubscriptions = this.getClientsWithExactSubscription(symbols, newClients);
      if (clientAndSubscriptions) {
        return clientAndSubscriptions.client;
      }

      // apparently new clients serve other symbols.
      // let's find what client from available is serving the symbols.
      // we can exclude newClients, because we've checked them already.
      const clients = LINQ.toArray(LINQ.except(Object.values(this.exchange.clients), newClients));
      clientAndSubscriptions = this.getClientsWithExactSubscription(symbols, clients);
      if (clientAndSubscriptions) {
        return clientAndSubscriptions.client;
      }
    }

    // no clients were created during the first update.
    // apparently some existing client is serving the symbols.
    // let's try to find which one.
    const clientAndSubscriptions = this.getClientsWithExactSubscription(symbols);
    if (clientAndSubscriptions) {
      return clientAndSubscriptions.client;
    }

    // pu pu pu... not well...
    return undefined;
  }
  
  public getClientsWithExactSubscription(symbols: string[], clientsToCheck?: Iterable<WsClient>)
    : { client: WsClient, subscriptions: string[][] } | undefined {
    const clientsAndSubscriptions = getClientsSubscriptions(this.exchange, clientsToCheck);
    if (clientsAndSubscriptions.length === 0) {
      return undefined;
    }

    const matchingClientsAndSubscriptions: { client: WsClient, subscriptions: string[][] }[] = [];

    // reverse order, so we check newly created clients first
    for (let i = clientsAndSubscriptions.length - 1; i >= 0; i--) {
      const { client, subscriptions } = clientsAndSubscriptions[i];
      for (const subscriptionSymbols of subscriptions) {
        if (!LINQ.equalList(subscriptionSymbols, symbols)) {
          continue;
        }

        matchingClientsAndSubscriptions.push({ client, subscriptions });
      }
    }

    if (matchingClientsAndSubscriptions.length === 0) {
      if (clientsAndSubscriptions.length === 1) {
        const { client, subscriptions } = clientsAndSubscriptions[0];
        const allSubscriptions = LINQ.toArray(LINQ.selectMany(subscriptions, (subscription) => subscription));

        if (!LINQ.equalList(allSubscriptions, symbols)) {
          return { client, subscriptions };
        }
      }

      return undefined;
    }

    if (matchingClientsAndSubscriptions.length > 1) {
      // there could be:
      // - subscriptions serving 1 individual symbol;
      // - subscriptions serving 2+ individual symbols;
      // - subscriptions serving 10+ grouped symbols;
      // - subscriptions serving mix of individual and grouped symbols;
      // so we need to find a connection that contains a subscription that is closest to the requested symbols.
      matchingClientsAndSubscriptions.sort((item1, item2) => {
        const allSymbols1 = LINQ.distinct(LINQ.selectMany(item1.subscriptions, (subscription) => subscription));
        const allSymbols2 = LINQ.distinct(LINQ.selectMany(item2.subscriptions, (subscription) => subscription));
        
        const item1SymbolsCountDiff = Math.abs(LINQ.count(allSymbols1) - symbols.length);
        const item2SymbolsCountDiff = Math.abs(LINQ.count(allSymbols2) - symbols.length);
        return item1SymbolsCountDiff - item2SymbolsCountDiff;
      });
    }
    
    const result = matchingClientsAndSubscriptions[0];

    // // warn that we're returning a multiplexed connection for a single symbol
    // if (symbols.length === 1 && result.subscriptions.length > 2) { // 2 paired symbols are ok
    //   const allSymbols = LINQ.toArray(LINQ.selectMany(result.subscriptions, (subscription) => subscription));
    //   logger.warn(`getClientsWithExactSubscription '${symbols[0]}' Returning multiplexed connection (${allSymbols.length}): [${allSymbols.sort().join(', ')}]`);
    //   const index = clientsAndSubscriptions.findIndex(item => item.client === result.client);
    //   clientsAndSubscriptions.splice(index, 1);
    //   const others =clientsAndSubscriptions.map((item) => item.subscriptions);
    //   logger.warn(`other subscriptions: '${JSON.stringify(others)}'`);
    // }

    return result;
  }

  pendingCloseSubscriptions = new Map<WsClient, string[][]>();

  public stopClosingConnection(client: WsClient, symbols: string[]) {
    // remove this subscription from 'to close' list if it was scheduled to
    let subscriptionsToClose = this.pendingCloseSubscriptions.get(client);
    if (subscriptionsToClose) {
      const indexToRemove = subscriptionsToClose.findIndex(
        (subscription) => LINQ.equalList(subscription, symbols));
      if (indexToRemove !== -1) {
        subscriptionsToClose.splice(indexToRemove, 1);
      }
    }
  }

  public async tryToCloseConnection(client: WsClient, symbols: string[]): Promise<boolean> {
    if (this.isPendingConnectionClose(client, symbols)) {
      const symbolsStr = symbols.length === 1 ? `'${symbols[0]}'` : `[${symbols.join(', ')}]`;
      throw new Error(`This subscription ${symbolsStr} is already scheduled to close`);
    }

    let subscriptionsToClose = this.pendingCloseSubscriptions.get(client);
    if (!subscriptionsToClose) {
      subscriptionsToClose = [];
      this.pendingCloseSubscriptions.set(client, subscriptionsToClose);
    }

    subscriptionsToClose.push(symbols);
    if (await this.tryToCloseConnections(client, subscriptionsToClose)) {
      subscriptionsToClose.length = 0;
    }

    return !this.isPendingConnectionClose(client, symbols);
  }

  private async tryToCloseConnections(client: WsClient, toCloseSubscriptions: string[][]): Promise<boolean> {
    const clientSubscriptions = getClientSubscriptions(this.exchange, client);
    if (clientSubscriptions.length === 0) {
      const symbolsArray = LINQ.toArray(LINQ.selectMany(toCloseSubscriptions, (subscription) => subscription)).sort();
      logger.warn(`Closing a not monitored client on '${this.exchange.id}'. symbolsArray: [${symbolsArray.join(', ')}]`);
      await closeClient(this.exchange, client);
      return true;
    }

    if (LINQ.all(clientSubscriptions, (subscriptions) =>
      LINQ.containsList(toCloseSubscriptions, subscriptions))) {
      await this.closeClient1(client, toCloseSubscriptions);
      return true;
    }

    return false;
  }

  public isPendingConnectionClose(client: WsClient, symbols: string[]): boolean {
    const toCloseSubscriptions = this.pendingCloseSubscriptions.get(client);
    if (!toCloseSubscriptions) {
      return false;
    }

    return LINQ.containsList(toCloseSubscriptions, symbols);
  }

  public hasAnySubscriptionWithSymbolPendingClose(symbol: string): boolean {
    for (const subscriptions of this.pendingCloseSubscriptions.values()) {
      for (const subscription of subscriptions) {
        if (subscription.includes(symbol)) {
          return true;
        }
      }
    }

    return false;
  }

  private async closeClient1(client: WsClient, toCloseSubscriptions: string[][]): Promise<void> {
    const symbolsArray = LINQ.toArray(LINQ.selectMany(toCloseSubscriptions, (subscription) => subscription));
    if (symbolsArray.length > 1) {
      logger.info(`Close multiplexed connection for: [${symbolsArray.sort().join(', ')}]`);
    } else {
      logger.info(`Close connection for: '${symbolsArray[0]}'`);
    }

    let anyAllowed = undefined;
    for (const handler of this.onClosingConnectionForSymbols) {
      anyAllowed = anyAllowed || handler(toCloseSubscriptions);
    }

    if (!anyAllowed) {
      logger.warn(`Closing connection was not approved by any handler`);
    }

    await closeClient(this.exchange, client);
  }
}

function findClientsExclusivelyServingSomeOfSymbols(exchange: ccxt.Exchange, symbolsToCheck: Set<string>)
  : { client: WsClient, monitoredSymbols: Set<string> }[] {
  const result: { client: WsClient, monitoredSymbols: Set<string> }[] = [];

  const connectionsBySymbols = getClientsSubscriptions(exchange);
  if (connectionsBySymbols.length === 0) {
    return result;
  }

  for (const { client, subscriptions } of connectionsBySymbols) {
    const monitoredSymbols = new Set<string>();

    // collect all symbols this client is monitoring
    for (const subscriptionSymbols of subscriptions) {
      for (const symbol of subscriptionSymbols) {
        monitoredSymbols.add(symbol);
      }
    }

    if (monitoredSymbols.size === 0) {
      // do not touch connections without subscriptions, it's likely an unWatch connection
      continue;
    }

    result.push({ client, monitoredSymbols });
  }

  for (let i = result.length - 1; i >= 0; i--) {
    // check if all monitoredSymbols are in symbolsToCheck (subset check).
    for (const symbol of result[i].monitoredSymbols) {
      if (!symbolsToCheck.has(symbol)) {
        // if not, remove this client from the result and move to the next one
        result.splice(i, 1);
        break;
      }
    }
  }

  return result;
}

function getClientsSubscriptions(exchange: ccxt.Exchange, clients?: Iterable<WsClient>)
  : { client: WsClient, subscriptions: string[][] }[] {
  if (clients === undefined) {
    clients = Object.values(exchange.clients);
  }

  const clientSubscriptions: { client: WsClient, subscriptions: string[][] }[] = [];
  for (const client of clients) {
    clientSubscriptions.push({ client, subscriptions: getClientSubscriptions(exchange, client) });
  }

  // sort by client.url to put newly created clients at the end
  clientSubscriptions.sort((a, b) => a.client.url.localeCompare(b.client.url));
  return clientSubscriptions;
}

function getClientSubscriptions(exchange: ccxt.Exchange, client: WsClient): string[][] {
  const result: string[][] = [];

  if (exchange.options.streamLimits?.spot > 1) {
    // exchange Uses Multiple Clients
    const processedSubscriptions = new Set();
    for (const [hash, subscription] of Object.entries(client.subscriptions)) {
      // binance uses the same subscription object for all symbols in a multiplexed connection.
      if (processedSubscriptions.has(subscription)) {
        continue;
      }

      if (!isOrderBookSubscription(hash)) {
        continue;
      }

      const subscriptionSymbols = extractSymbolsFromSubscription(exchange, hash, subscription);
      result.push(LINQ.toArray(subscriptionSymbols));
      processedSubscriptions.add(subscription);
    }
  } else {
    // exchanges like bybit use a single client for all symbols.
    // this client has a lot of individual subscriptions to serve all 
    // the requested symbols (1 subscription per symbol).
    const subscriptions = Object.keys(Object.values(exchange.clients)[0].subscriptions);
    result.push(subscriptions);
  }

  return result;
}

async function closeClientBySymbol(exchange: ccxt.Exchange, symbol: string): Promise<boolean> {
  return closeClientForSymbols(exchange, [symbol]);
}

async function closeClientForSymbols(exchange: ccxt.Exchange, symbols: string[]): Promise<boolean> {
  if (!exchange?.clients) {
    return false;
  }

  for (const client of Object.values(exchange.clients)) {
    const { orderbookSymbols, otherChannels } = summarizeClient(exchange, client);

    if (orderbookSymbols.size === 0) {
      continue; // do not touch connections without subscriptions, it's likely an unWatch connection
    }

    if (otherChannels.size > 0) {
      continue; // do not touch connections are serving some other channels (like trades)
    }

    if (orderbookSymbols.size !== symbols.length
      || !symbols.every((s) => orderbookSymbols.has(s))) {
      continue; // not 1:1 match
    }

    await closeClient(exchange, client);
    return true; // closed the exact-match connection
  }
  return false; // no exact-match connection found
}

function summarizeClient(exchange: ccxt.Exchange, client: WsClient): {
  orderbookSymbols: Set<string>;
  otherChannels: Set<string>;
} {
  const orderbookSymbols = new Set<string>();
  const otherChannels = new Set<string>();

  for (const [hash, subscription] of Object.entries(client.subscriptions)) {
    if (isOrderBookSubscription(hash)) {
      const subscriptionSymbols =
        extractSymbolsFromSubscription(exchange, hash, subscription);
      for (const symbol of subscriptionSymbols) {
        orderbookSymbols.add(symbol);
      }
    } else {
      otherChannels.add(hash);
    }
  }
  return {
    orderbookSymbols,
    otherChannels
  };
}

function isOrderBookSubscription(messageHash: string): boolean {
  messageHash = messageHash.toLowerCase();
  return messageHash.includes('orderbook')
    || messageHash.includes('books')
    || messageHash.includes('depth')
    || messageHash.includes('l2')
    || messageHash.includes('level2');
}

function extractSymbolsFromSubscription(
  exchange: ccxt.Exchange,
  messageHash: string,
  subscription
): Set<string> {
  const result = new Set<string>();

  // 1) Use explicit subscription hints when available
  if (subscription) {
    if (subscription.symbol && typeof subscription.symbol === 'string') {
      result.add(subscription.symbol);
    }

    if (subscription.symbols && Array.isArray(subscription.symbols)) {
      for (const symbol of subscription.symbols) {
        if (typeof symbol === 'string') {
          result.add(symbol);
        }
      }
    }

    if (subscription.market && typeof subscription.market.symbol === 'string') {
      result.add(subscription.market.symbol);
    }

    if (subscription.marketId) {
      if (Array.isArray(subscription.marketId)) {
        for (const id of subscription.marketId) {
          addSymbolsFromMarketId(exchange, id, result);
        }
      } else {
        addSymbolsFromMarketId(exchange, subscription.marketId, result);
      }
    }

    if (Array.isArray(subscription.marketIds)) {
      for (const id of subscription.marketIds) {
        addSymbolsFromMarketId(exchange, id, result);
      }
    }
  }

  // 2) Fallback: parse messageHash
  if (result.size === 0 && typeof messageHash === 'string') {
    const cleaned = messageHash.replaceAll(/:+/g, ' ');
    const tokens = cleaned.split(/[^\w./@-]+/).filter(Boolean);

    for (const token of tokens) {
      if (exchange.markets && exchange.markets[token]) {
        result.add(token);
        continue;
      }

      if (exchange.markets_by_id && exchange.markets_by_id[token]) {
        const entry = exchange.markets_by_id[token];
        if (Array.isArray(entry)) {
          for (const market of entry) {
            if (market && market.symbol) {
              result.add(market.symbol);
            }
          }
        } else {
          if (entry && entry.symbol) {
            result.add(entry.symbol);
          }
        }
        continue;
      }

      if (token.includes('-') || token.includes('_')) {
        const asUnified = token.replace('_', '/').replace('-', '/').toUpperCase();
        if (exchange.markets && exchange.markets[asUnified]) {
          result.add(asUnified);
        }
      }
    }
  }

  return result;
}

function addSymbolsFromMarketId(
  exchange: ccxt.Exchange,
  id: string,
  out: Set<string>
) {
  if (!id) {
    return;
  }

  const entry = exchange.markets_by_id[id];
  if (!entry) {
    return;
  }

  if (Array.isArray(entry)) {
    for (const m of entry) {
      if (m && m.symbol) {
        out.add(m.symbol);
      }
    }
  } else {
    if (entry.symbol) {
      out.add(entry.symbol);
    }
  }
}

// async function closeClient(exchange: ccxt.Exchange, client: WsClient): Promise<void> {
//   client.error = new ExchangeClosedByUser('Connection closed by user');
//   await client.close();
//   delete exchange.clients[client.url];
// }

async function closeClient(exchange: ccxt.Exchange, client: WsClient): Promise<void> {
  // 1. Check if connection is already closed or closing
  if (!client.connection || !client.isOpen()) {
    // Connection already closed, just clean up
    delete exchange.clients[client.url];
    return;
  }

  try {
    // 2. Mark client as closing to prevent new operations
    client.error = new ExchangeClosedByUser('Connection closed by user');

    // 3. Clear all subscriptions to prevent new messages from being processed
    client.subscriptions = {};

    // 4. Reject all pending futures to prevent hanging promises
    const pendingFutures = Object.keys(client.futures);
    for (const messageHash of pendingFutures) {
      if (client.futures[messageHash]) {
        client.futures[messageHash].reject(new ExchangeClosedByUser('Connection closed by user'));
        delete client.futures[messageHash];
      }
    }

    // 5. Clear any pending rejections
    client.rejections = {};

    // 6. Clear timers and intervals
    client.clearConnectionTimeout();
    client.clearPingInterval();

    // 7. Small grace period to let any in-flight messages complete
    await new Promise(resolve => setTimeout(resolve, 100));

    // 8. Close the WebSocket connection with timeout protection
    const closePromise = client.close();
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Close timeout')), 5000)
    );

    await Promise.race([closePromise, timeoutPromise]);

  } catch (error) {
    // If graceful close fails, force terminate the connection
    try {
      if (client.connection && client.connection.terminate) {
        client.connection.terminate();
      }
    } catch (termError) {
      logger.error(termError, 'Terminate connection error:');
    }
  } finally {
    // **COMPLETE STATE RESET - Always runs regardless of success/failure**
    client.startedConnecting = false;
    client.isConnected = false;
    client.error = undefined;
    client.connection = undefined;
    client.connectionStarted = undefined;
    client.connectionEstablished = undefined;
    client.connected = undefined;
    client.disconnected = undefined; // Reset disconnected promise too

    // Always clean up the client reference
    delete exchange.clients[client.url];

    if (Object.keys(exchange.clients).length === 0) {
      logger.info(`'${exchange.id}' Last connection is closed`);
    }
  }
}