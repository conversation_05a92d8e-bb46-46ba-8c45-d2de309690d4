// logger.ts
import pino, { Logger, LevelWithSilent } from 'pino';

const nodeEnv: string = process.env.NODE_ENV || 'development';

const logLevels: Map<string, LevelWithSilent> = process.env.FEATURE_LOG_LEVELS
  ? new Map(JSON.parse(process.env.FEATURE_LOG_LEVELS))
  : new Map();

// default/base logger setup
const DEFAULT_LOGGER_NAME = 'default';
logLevels.set(DEFAULT_LOGGER_NAME, (process.env.LOG_LEVEL as LevelWithSilent) || 'info');
export const logger: Logger = buildLogger(DEFAULT_LOGGER_NAME);
logger.info(`Default logger initialized. Level: ${logger.level}.`);


function buildLogger(loggerName: string): Logger {
  const logLevel = logLevels.get(loggerName) ?? 'info';

  if (nodeEnv === 'production') {
    return pino({
      level: logLevel,
      base: undefined,
      timestamp: pino.stdTimeFunctions.isoTime,
      formatters: {
        level: (label: string) => {
          return { level: label.toUpperCase() };
        }
      }
    });
  }

  const transport = pino.transport({
    targets: [
      {
        target: 'pino-pretty',
        options: { colorize: true, ignore: 'pid,hostname', singleLine: true },
        level: logLevel
      }
    ]
  });

  return pino({ level: logLevel }, transport);
}

// here you can get a logger for a specific feature\system.
// they can be configured to be silent or have a different log level for debugging specific issues.
const featureLoggers: Map<string, Logger> = new Map<string, Logger>();
export function featureLogger(loggerName: string): Logger {
  let featureLogger: Logger | undefined = featureLoggers.get(loggerName);
  if (!featureLogger) {
    // featureLogger = buildLogger(loggerName);

    featureLogger = logger.child({ tag: loggerName });
    featureLogger.level = logLevels.get(loggerName) ?? 'warn';
  }
  return featureLogger;
}