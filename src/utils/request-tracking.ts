import { featureLogger } from './pinno-logger';
const requestTrackingLogger = featureLogger('requestTracking');

export interface TrackerStageMetrics {
  stage: string;
  duration: number;
}

export class RequestTracker {
  url: string;
  startTime: number;
  stageMetrics: TrackerStageMetrics[] = [];
  
  currentStage: string;
  stageStartTime: number;
  endTime: number;
  private longRunningTimeoutHandle?: NodeJS.Timeout;
  
  constructor(url: string) {
    this.url = url;
    this.startTime = Date.now();
    this.currentStage = 'STARTED';
    this.stageStartTime = this.startTime;
    this.warnLongTimeStages(15_000, 15_000);
  }

  public warnLongTimeStages(checkInterval = 15_000, warnThreshold = 15_000): void {
    const totalTime = (Date.now() - this.startTime) / 1000;
    const stageTime = (Date.now() - this.stageStartTime) / 1000;

    // // notify if the request is more than 15 seconds without any updates (is on the same stage)
    if (stageTime > warnThreshold)
      requestTrackingLogger.warn(`[${this.url}] SLOW Stage: '${this.currentStage}' (${stageTime}s), Total: ${totalTime}s`);

    // Continue monitoring every 15 seconds
    this.longRunningTimeoutHandle = setTimeout(() => {
      this.warnLongTimeStages();
    }, checkInterval);
  }

  public updateStage(stage: string): void {
    const duration = (Date.now() - this.stageStartTime);
    this.stageMetrics.push({ stage: this.currentStage, duration: duration });
    
    requestTrackingLogger.info(`[${this.url}] Stage '${this.currentStage}' completed in ${duration}ms, moving to '${stage}'`);
    
    this.currentStage = stage;
    this.stageStartTime = Date.now();
  }
  
  // use RequestTrackerManager.completeTracking() instead.
  public completedTracking(){
    if (this.longRunningTimeoutHandle) {
      clearTimeout(this.longRunningTimeoutHandle);
    }
    
    this.updateStage('COMPLETED');
    this.updateStage('');
    this.endTime = Date.now();
    // const totalTime = (Date.now() - this.startTime) / 1000;
    //requestTrackingLogger.info(`[${this.url}] Request completed - Total time: ${totalTime}s`);
  }
}

class RequestTrackerManager {
  activeRequests = new Map<string, RequestTracker>();
  completedRequests: RequestTracker[] = [];
  name: string;
  
  constructor(name: string) {
    this.name = name;
    this.reportActiveRequests = this.reportActiveRequests.bind(this);
  }

  public createTracker(url: string): RequestTracker {
    const tracker = new RequestTracker(url);
    this.activeRequests.set(url, tracker);
    //requestTrackingLogger.info(`[${url}] Request started - tracking initiated`);
    return tracker;
  }

  public getTracker(url: string): RequestTracker | undefined {
    return this.activeRequests.get(url);
  }

  public completeTracking(tracker: RequestTracker): void {
    this.activeRequests.delete(tracker.url);
    tracker.completedTracking();
    this.completedRequests.push(tracker);
  }

  // Periodic status report for active requests
  public reportActiveRequests() {
    if (this.activeRequests.size > 0) {
      requestTrackingLogger.info(`${this.name} requests: ${this.activeRequests.size}`);
      // for (const [url, tracker] of this.activeRequests) {
      //   const totalTime = (Date.now() - tracker.startTime) / 1000;
      //   const stageTime = (Date.now() - tracker.stageStartTime) / 1000;
      //   requestTrackingLogger.info(`  [${url}] '${tracker.currentStage}' ${stageTime}s, Total: ${stageTime}s`);
      // }
    }
  }
}

export const requestExecutionTracking = new RequestTrackerManager('allRequestsTracker');

