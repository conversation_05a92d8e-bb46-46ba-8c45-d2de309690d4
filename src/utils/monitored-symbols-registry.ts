import * as ccxt from 'ccxt';
import { LINQ } from './linq';

export class MonitoredSymbolsRegistry {
 private static monitoredSubscriptions = new Map<ccxt.Exchange, string[][]>();

  public static register(exchange: ccxt.Exchange, symbols: string[]) {
    let monitoredSubscriptions = MonitoredSymbolsRegistry.monitoredSubscriptions.get(exchange);
    if (!monitoredSubscriptions) {
      monitoredSubscriptions = [];
      MonitoredSymbolsRegistry.monitoredSubscriptions.set(exchange, monitoredSubscriptions);
    }

    monitoredSubscriptions.push(symbols);
  }

  public static unregister(exchange: ccxt.Exchange, symbols: string[]) {
    const monitoredSubscriptions = MonitoredSymbolsRegistry.monitoredSubscriptions.get(exchange);
    const indexToRemove = monitoredSubscriptions.findIndex(
      (subscription) => LINQ.equalList(subscription, symbols));
    monitoredSubscriptions.splice(indexToRemove, 1);
  }

  public static isMonitored(client: ccxt.Exchange, symbol: string): boolean {
    const monitoredSubscriptions = MonitoredSymbolsRegistry.monitoredSubscriptions.get(client);
    return monitoredSubscriptions && LINQ.any(monitoredSubscriptions, (subscription) => subscription.includes(symbol));
  }
}