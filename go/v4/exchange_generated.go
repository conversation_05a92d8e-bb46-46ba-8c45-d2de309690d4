package ccxt

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

func (this *Exchange) Describe() interface{} {
	return map[string]interface{}{
		"id":              nil,
		"name":            nil,
		"countries":       nil,
		"enableRateLimit": true,
		"rateLimit":       2000,
		"timeout":         this.Timeout,
		"certified":       false,
		"pro":             false,
		"alias":           false,
		"dex":             false,
		"has": map[string]interface{}{
			"publicAPI":                              true,
			"privateAPI":                             true,
			"CORS":                                   nil,
			"sandbox":                                nil,
			"spot":                                   nil,
			"margin":                                 nil,
			"swap":                                   nil,
			"future":                                 nil,
			"option":                                 nil,
			"addMargin":                              nil,
			"borrowCrossMargin":                      nil,
			"borrowIsolatedMargin":                   nil,
			"borrowMargin":                           nil,
			"cancelAllOrders":                        nil,
			"cancelAllOrdersWs":                      nil,
			"cancelOrder":                            true,
			"cancelOrderWs":                          nil,
			"cancelOrders":                           nil,
			"cancelOrdersWs":                         nil,
			"closeAllPositions":                      nil,
			"closePosition":                          nil,
			"createDepositAddress":                   nil,
			"createLimitBuyOrder":                    nil,
			"createLimitBuyOrderWs":                  nil,
			"createLimitOrder":                       true,
			"createLimitOrderWs":                     nil,
			"createLimitSellOrder":                   nil,
			"createLimitSellOrderWs":                 nil,
			"createMarketBuyOrder":                   nil,
			"createMarketBuyOrderWs":                 nil,
			"createMarketBuyOrderWithCost":           nil,
			"createMarketBuyOrderWithCostWs":         nil,
			"createMarketOrder":                      true,
			"createMarketOrderWs":                    true,
			"createMarketOrderWithCost":              nil,
			"createMarketOrderWithCostWs":            nil,
			"createMarketSellOrder":                  nil,
			"createMarketSellOrderWs":                nil,
			"createMarketSellOrderWithCost":          nil,
			"createMarketSellOrderWithCostWs":        nil,
			"createOrder":                            true,
			"createOrderWs":                          nil,
			"createOrders":                           nil,
			"createOrderWithTakeProfitAndStopLoss":   nil,
			"createOrderWithTakeProfitAndStopLossWs": nil,
			"createPostOnlyOrder":                    nil,
			"createPostOnlyOrderWs":                  nil,
			"createReduceOnlyOrder":                  nil,
			"createReduceOnlyOrderWs":                nil,
			"createStopLimitOrder":                   nil,
			"createStopLimitOrderWs":                 nil,
			"createStopLossOrder":                    nil,
			"createStopLossOrderWs":                  nil,
			"createStopMarketOrder":                  nil,
			"createStopMarketOrderWs":                nil,
			"createStopOrder":                        nil,
			"createStopOrderWs":                      nil,
			"createTakeProfitOrder":                  nil,
			"createTakeProfitOrderWs":                nil,
			"createTrailingAmountOrder":              nil,
			"createTrailingAmountOrderWs":            nil,
			"createTrailingPercentOrder":             nil,
			"createTrailingPercentOrderWs":           nil,
			"createTriggerOrder":                     nil,
			"createTriggerOrderWs":                   nil,
			"deposit":                                nil,
			"editOrder":                              "emulated",
			"editOrders":                             nil,
			"editOrderWs":                            nil,
			"fetchAccounts":                          nil,
			"fetchBalance":                           true,
			"fetchBalanceWs":                         nil,
			"fetchBidsAsks":                          nil,
			"fetchBorrowInterest":                    nil,
			"fetchBorrowRate":                        nil,
			"fetchBorrowRateHistories":               nil,
			"fetchBorrowRateHistory":                 nil,
			"fetchBorrowRates":                       nil,
			"fetchBorrowRatesPerSymbol":              nil,
			"fetchCanceledAndClosedOrders":           nil,
			"fetchCanceledOrders":                    nil,
			"fetchClosedOrder":                       nil,
			"fetchClosedOrders":                      nil,
			"fetchClosedOrdersWs":                    nil,
			"fetchConvertCurrencies":                 nil,
			"fetchConvertQuote":                      nil,
			"fetchConvertTrade":                      nil,
			"fetchConvertTradeHistory":               nil,
			"fetchCrossBorrowRate":                   nil,
			"fetchCrossBorrowRates":                  nil,
			"fetchCurrencies":                        "emulated",
			"fetchCurrenciesWs":                      "emulated",
			"fetchDeposit":                           nil,
			"fetchDepositAddress":                    nil,
			"fetchDepositAddresses":                  nil,
			"fetchDepositAddressesByNetwork":         nil,
			"fetchDeposits":                          nil,
			"fetchDepositsWithdrawals":               nil,
			"fetchDepositsWs":                        nil,
			"fetchDepositWithdrawFee":                nil,
			"fetchDepositWithdrawFees":               nil,
			"fetchFundingHistory":                    nil,
			"fetchFundingRate":                       nil,
			"fetchFundingRateHistory":                nil,
			"fetchFundingInterval":                   nil,
			"fetchFundingIntervals":                  nil,
			"fetchFundingRates":                      nil,
			"fetchGreeks":                            nil,
			"fetchIndexOHLCV":                        nil,
			"fetchIsolatedBorrowRate":                nil,
			"fetchIsolatedBorrowRates":               nil,
			"fetchMarginAdjustmentHistory":           nil,
			"fetchIsolatedPositions":                 nil,
			"fetchL2OrderBook":                       true,
			"fetchL3OrderBook":                       nil,
			"fetchLastPrices":                        nil,
			"fetchLedger":                            nil,
			"fetchLedgerEntry":                       nil,
			"fetchLeverage":                          nil,
			"fetchLeverages":                         nil,
			"fetchLeverageTiers":                     nil,
			"fetchLiquidations":                      nil,
			"fetchLongShortRatio":                    nil,
			"fetchLongShortRatioHistory":             nil,
			"fetchMarginMode":                        nil,
			"fetchMarginModes":                       nil,
			"fetchMarketLeverageTiers":               nil,
			"fetchMarkets":                           true,
			"fetchMarketsWs":                         nil,
			"fetchMarkOHLCV":                         nil,
			"fetchMyLiquidations":                    nil,
			"fetchMySettlementHistory":               nil,
			"fetchMyTrades":                          nil,
			"fetchMyTradesWs":                        nil,
			"fetchOHLCV":                             nil,
			"fetchOHLCVWs":                           nil,
			"fetchOpenInterest":                      nil,
			"fetchOpenInterests":                     nil,
			"fetchOpenInterestHistory":               nil,
			"fetchOpenOrder":                         nil,
			"fetchOpenOrders":                        nil,
			"fetchOpenOrdersWs":                      nil,
			"fetchOption":                            nil,
			"fetchOptionChain":                       nil,
			"fetchOrder":                             nil,
			"fetchOrderBook":                         true,
			"fetchOrderBooks":                        nil,
			"fetchOrderBookWs":                       nil,
			"fetchOrders":                            nil,
			"fetchOrdersByStatus":                    nil,
			"fetchOrdersWs":                          nil,
			"fetchOrderTrades":                       nil,
			"fetchOrderWs":                           nil,
			"fetchPosition":                          nil,
			"fetchPositionHistory":                   nil,
			"fetchPositionsHistory":                  nil,
			"fetchPositionWs":                        nil,
			"fetchPositionMode":                      nil,
			"fetchPositions":                         nil,
			"fetchPositionsWs":                       nil,
			"fetchPositionsForSymbol":                nil,
			"fetchPositionsForSymbolWs":              nil,
			"fetchPositionsRisk":                     nil,
			"fetchPremiumIndexOHLCV":                 nil,
			"fetchSettlementHistory":                 nil,
			"fetchStatus":                            nil,
			"fetchTicker":                            true,
			"fetchTickerWs":                          nil,
			"fetchTickers":                           nil,
			"fetchMarkPrices":                        nil,
			"fetchTickersWs":                         nil,
			"fetchTime":                              nil,
			"fetchTrades":                            true,
			"fetchTradesWs":                          nil,
			"fetchTradingFee":                        nil,
			"fetchTradingFees":                       nil,
			"fetchTradingFeesWs":                     nil,
			"fetchTradingLimits":                     nil,
			"fetchTransactionFee":                    nil,
			"fetchTransactionFees":                   nil,
			"fetchTransactions":                      nil,
			"fetchTransfer":                          nil,
			"fetchTransfers":                         nil,
			"fetchUnderlyingAssets":                  nil,
			"fetchVolatilityHistory":                 nil,
			"fetchWithdrawAddresses":                 nil,
			"fetchWithdrawal":                        nil,
			"fetchWithdrawals":                       nil,
			"fetchWithdrawalsWs":                     nil,
			"fetchWithdrawalWhitelist":               nil,
			"reduceMargin":                           nil,
			"repayCrossMargin":                       nil,
			"repayIsolatedMargin":                    nil,
			"setLeverage":                            nil,
			"setMargin":                              nil,
			"setMarginMode":                          nil,
			"setPositionMode":                        nil,
			"signIn":                                 nil,
			"transfer":                               nil,
			"watchBalance":                           nil,
			"watchMyTrades":                          nil,
			"watchOHLCV":                             nil,
			"watchOHLCVForSymbols":                   nil,
			"watchOrderBook":                         nil,
			"watchBidsAsks":                          nil,
			"watchOrderBookForSymbols":               nil,
			"watchOrders":                            nil,
			"watchOrdersForSymbols":                  nil,
			"watchPosition":                          nil,
			"watchPositions":                         nil,
			"watchStatus":                            nil,
			"watchTicker":                            nil,
			"watchTickers":                           nil,
			"watchTrades":                            nil,
			"watchTradesForSymbols":                  nil,
			"watchLiquidations":                      nil,
			"watchLiquidationsForSymbols":            nil,
			"watchMyLiquidations":                    nil,
			"unWatchOrders":                          nil,
			"unWatchTrades":                          nil,
			"unWatchTradesForSymbols":                nil,
			"unWatchOHLCVForSymbols":                 nil,
			"unWatchOrderBookForSymbols":             nil,
			"unWatchPositions":                       nil,
			"unWatchOrderBook":                       nil,
			"unWatchTickers":                         nil,
			"unWatchMyTrades":                        nil,
			"unWatchTicker":                          nil,
			"unWatchOHLCV":                           nil,
			"watchMyLiquidationsForSymbols":          nil,
			"withdraw":                               nil,
			"ws":                                     nil,
		},
		"urls": map[string]interface{}{
			"logo": nil,
			"api":  nil,
			"www":  nil,
			"doc":  nil,
			"fees": nil,
		},
		"api": nil,
		"requiredCredentials": map[string]interface{}{
			"apiKey":        true,
			"secret":        true,
			"uid":           false,
			"accountId":     false,
			"login":         false,
			"password":      false,
			"twofa":         false,
			"privateKey":    false,
			"walletAddress": false,
			"token":         false,
		},
		"markets":    nil,
		"currencies": map[string]interface{}{},
		"timeframes": nil,
		"fees": map[string]interface{}{
			"trading": map[string]interface{}{
				"tierBased":  nil,
				"percentage": nil,
				"taker":      nil,
				"maker":      nil,
			},
			"funding": map[string]interface{}{
				"tierBased":  nil,
				"percentage": nil,
				"withdraw":   map[string]interface{}{},
				"deposit":    map[string]interface{}{},
			},
		},
		"status": map[string]interface{}{
			"status":  "ok",
			"updated": nil,
			"eta":     nil,
			"url":     nil,
		},
		"exceptions": nil,
		"httpExceptions": map[string]interface{}{
			"422": ExchangeError,
			"418": DDoSProtection,
			"429": RateLimitExceeded,
			"404": ExchangeNotAvailable,
			"409": ExchangeNotAvailable,
			"410": ExchangeNotAvailable,
			"451": ExchangeNotAvailable,
			"500": ExchangeNotAvailable,
			"501": ExchangeNotAvailable,
			"502": ExchangeNotAvailable,
			"520": ExchangeNotAvailable,
			"521": ExchangeNotAvailable,
			"522": ExchangeNotAvailable,
			"525": ExchangeNotAvailable,
			"526": ExchangeNotAvailable,
			"400": ExchangeNotAvailable,
			"403": ExchangeNotAvailable,
			"405": ExchangeNotAvailable,
			"503": ExchangeNotAvailable,
			"530": ExchangeNotAvailable,
			"408": RequestTimeout,
			"504": RequestTimeout,
			"401": AuthenticationError,
			"407": AuthenticationError,
			"511": AuthenticationError,
		},
		"commonCurrencies": map[string]interface{}{
			"XBT":   "BTC",
			"BCHSV": "BSV",
		},
		"precisionMode": TICK_SIZE,
		"paddingMode":   NO_PADDING,
		"limits": map[string]interface{}{
			"leverage": map[string]interface{}{
				"min": nil,
				"max": nil,
			},
			"amount": map[string]interface{}{
				"min": nil,
				"max": nil,
			},
			"price": map[string]interface{}{
				"min": nil,
				"max": nil,
			},
			"cost": map[string]interface{}{
				"min": nil,
				"max": nil,
			},
		},
	}
}
func (this *Exchange) SafeBoolN(dictionaryOrList interface{}, keys interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @description safely extract boolean value from dictionary or list
	 * @returns {bool | undefined}
	 */
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	var value interface{} = this.SafeValueN(dictionaryOrList, keys, defaultValue)
	if IsTrue(IsBool(value)) {
		return value
	}
	return defaultValue
}
func (this *Exchange) SafeBool2(dictionary interface{}, key1 interface{}, key2 interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @description safely extract boolean value from dictionary or list
	 * @returns {bool | undefined}
	 */
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	return this.SafeBoolN(dictionary, []interface{}{key1, key2}, defaultValue)
}
func (this *Exchange) SafeBool(dictionary interface{}, key interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @description safely extract boolean value from dictionary or list
	 * @returns {bool | undefined}
	 */
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	return this.SafeBoolN(dictionary, []interface{}{key}, defaultValue)
}
func (this *Exchange) SafeDictN(dictionaryOrList interface{}, keys interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @description safely extract a dictionary from dictionary or list
	 * @returns {object | undefined}
	 */
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	var value interface{} = this.SafeValueN(dictionaryOrList, keys, defaultValue)
	if IsTrue(IsEqual(value, nil)) {
		return defaultValue
	}
	if IsTrue((IsObject(value))) {
		if !IsTrue(IsArray(value)) {
			return value
		}
	}
	return defaultValue
}
func (this *Exchange) SafeDict(dictionary interface{}, key interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @description safely extract a dictionary from dictionary or list
	 * @returns {object | undefined}
	 */
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	return this.SafeDictN(dictionary, []interface{}{key}, defaultValue)
}
func (this *Exchange) SafeDict2(dictionary interface{}, key1 interface{}, key2 interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @description safely extract a dictionary from dictionary or list
	 * @returns {object | undefined}
	 */
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	return this.SafeDictN(dictionary, []interface{}{key1, key2}, defaultValue)
}
func (this *Exchange) SafeListN(dictionaryOrList interface{}, keys interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @description safely extract an Array from dictionary or list
	 * @returns {Array | undefined}
	 */
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	var value interface{} = this.SafeValueN(dictionaryOrList, keys, defaultValue)
	if IsTrue(IsEqual(value, nil)) {
		return defaultValue
	}
	if IsTrue(IsArray(value)) {
		return value
	}
	return defaultValue
}
func (this *Exchange) SafeList2(dictionaryOrList interface{}, key1 interface{}, key2 interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @description safely extract an Array from dictionary or list
	 * @returns {Array | undefined}
	 */
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	return this.SafeListN(dictionaryOrList, []interface{}{key1, key2}, defaultValue)
}
func (this *Exchange) SafeList(dictionaryOrList interface{}, key interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @description safely extract an Array from dictionary or list
	 * @returns {Array | undefined}
	 */
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	return this.SafeListN(dictionaryOrList, []interface{}{key}, defaultValue)
}
func (this *Exchange) HandleDeltas(orderbook interface{}, deltas interface{}) {
	for i := 0; IsLessThan(i, GetArrayLength(deltas)); i++ {
		this.HandleDelta(orderbook, GetValue(deltas, i))
	}
}
func (this *Exchange) HandleDelta(bookside interface{}, delta interface{}) {
	panic(NotSupported(Add(this.Id, " handleDelta not supported yet")))
}
func (this *Exchange) HandleDeltasWithKeys(bookSide interface{}, deltas interface{}, optionalArgs ...interface{}) {
	priceKey := GetArg(optionalArgs, 0, 0)
	_ = priceKey
	amountKey := GetArg(optionalArgs, 1, 1)
	_ = amountKey
	countOrIdKey := GetArg(optionalArgs, 2, 2)
	_ = countOrIdKey
	for i := 0; IsLessThan(i, GetArrayLength(deltas)); i++ {
		var bidAsk interface{} = this.ParseBidAsk(GetValue(deltas, i), priceKey, amountKey, countOrIdKey)
		(bookSide.(*OrderBookSide)).StoreArray(bidAsk)
	}
}
func (this *Exchange) GetCacheIndex(orderbook interface{}, deltas interface{}) interface{} {
	// return the first index of the cache that can be applied to the orderbook or -1 if not possible
	return OpNeg(1)
}
func (this *Exchange) ArraysConcat(arraysOfArrays interface{}) interface{} {
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(arraysOfArrays)); i++ {
		result = this.ArrayConcat(result, GetValue(arraysOfArrays, i))
	}
	return result
}
func (this *Exchange) FindTimeframe(timeframe interface{}, optionalArgs ...interface{}) interface{} {
	timeframes := GetArg(optionalArgs, 0, nil)
	_ = timeframes
	if IsTrue(IsEqual(timeframes, nil)) {
		timeframes = this.Timeframes
	}
	var keys interface{} = ObjectKeys(timeframes)
	for i := 0; IsLessThan(i, GetArrayLength(keys)); i++ {
		var key interface{} = GetValue(keys, i)
		if IsTrue(IsEqual(GetValue(timeframes, key), timeframe)) {
			return key
		}
	}
	return nil
}
func (this *Exchange) CheckProxyUrlSettings(optionalArgs ...interface{}) interface{} {
	url := GetArg(optionalArgs, 0, nil)
	_ = url
	method := GetArg(optionalArgs, 1, nil)
	_ = method
	headers := GetArg(optionalArgs, 2, nil)
	_ = headers
	body := GetArg(optionalArgs, 3, nil)
	_ = body
	var usedProxies interface{} = []interface{}{}
	var proxyUrl interface{} = nil
	if IsTrue(!IsEqual(this.ProxyUrl, nil)) {
		AppendToArray(&usedProxies, "proxyUrl")
		proxyUrl = this.ProxyUrl
	}
	if IsTrue(!IsEqual(this.Proxy_url, nil)) {
		AppendToArray(&usedProxies, "proxy_url")
		proxyUrl = this.Proxy_url
	}
	if IsTrue(!IsEqual(this.ProxyUrlCallback, nil)) {
		AppendToArray(&usedProxies, "proxyUrlCallback")
		proxyUrl = this.callDynamically("proxyUrlCallback", url, method, headers, body)
	}
	if IsTrue(!IsEqual(this.Proxy_url_callback, nil)) {
		AppendToArray(&usedProxies, "proxy_url_callback")
		proxyUrl = this.callDynamically("proxy_url_callback", url, method, headers, body)
	}
	// backwards-compatibility
	if IsTrue(!IsEqual(this.Proxy, nil)) {
		AppendToArray(&usedProxies, "proxy")
		if IsTrue(IsFunction(this.Proxy)) {
			proxyUrl = this.callDynamically("proxy", url, method, headers, body)
		} else {
			proxyUrl = this.Proxy
		}
	}
	var length interface{} = GetArrayLength(usedProxies)
	if IsTrue(IsGreaterThan(length, 1)) {
		var joinedProxyNames interface{} = Join(usedProxies, ",")
		panic(InvalidProxySettings(Add(Add(Add(this.Id, " you have multiple conflicting proxy settings ("), joinedProxyNames), "), please use only one from : proxyUrl, proxy_url, proxyUrlCallback, proxy_url_callback")))
	}
	return proxyUrl
}
func (this *Exchange) UrlEncoderForProxyUrl(targetUrl interface{}) interface{} {
	// to be overriden
	var includesQuery interface{} = IsGreaterThanOrEqual(GetIndexOf(targetUrl, "?"), 0)
	var finalUrl interface{} = Ternary(IsTrue(includesQuery), this.EncodeURIComponent(targetUrl), targetUrl)
	return finalUrl
}
func (this *Exchange) CheckProxySettings(optionalArgs ...interface{}) interface{} {
	url := GetArg(optionalArgs, 0, nil)
	_ = url
	method := GetArg(optionalArgs, 1, nil)
	_ = method
	headers := GetArg(optionalArgs, 2, nil)
	_ = headers
	body := GetArg(optionalArgs, 3, nil)
	_ = body
	var usedProxies interface{} = []interface{}{}
	var httpProxy interface{} = nil
	var httpsProxy interface{} = nil
	var socksProxy interface{} = nil
	// httpProxy
	var isHttpProxyDefined interface{} = this.ValueIsDefined(this.HttpProxy)
	var isHttp_proxy_defined interface{} = this.ValueIsDefined(this.Http_proxy)
	if IsTrue(IsTrue(isHttpProxyDefined) || IsTrue(isHttp_proxy_defined)) {
		AppendToArray(&usedProxies, "httpProxy")
		httpProxy = Ternary(IsTrue(isHttpProxyDefined), this.HttpProxy, this.Http_proxy)
	}
	var ishttpProxyCallbackDefined interface{} = this.ValueIsDefined(this.HttpProxyCallback)
	var ishttp_proxy_callback_defined interface{} = this.ValueIsDefined(this.Http_proxy_callback)
	if IsTrue(IsTrue(ishttpProxyCallbackDefined) || IsTrue(ishttp_proxy_callback_defined)) {
		AppendToArray(&usedProxies, "httpProxyCallback")
		httpProxy = Ternary(IsTrue(ishttpProxyCallbackDefined), this.callDynamically("httpProxyCallback", url, method, headers, body), this.callDynamically("http_proxy_callback", url, method, headers, body))
	}
	// httpsProxy
	var isHttpsProxyDefined interface{} = this.ValueIsDefined(this.HttpsProxy)
	var isHttps_proxy_defined interface{} = this.ValueIsDefined(this.Https_proxy)
	if IsTrue(IsTrue(isHttpsProxyDefined) || IsTrue(isHttps_proxy_defined)) {
		AppendToArray(&usedProxies, "httpsProxy")
		httpsProxy = Ternary(IsTrue(isHttpsProxyDefined), this.HttpsProxy, this.Https_proxy)
	}
	var ishttpsProxyCallbackDefined interface{} = this.ValueIsDefined(this.HttpsProxyCallback)
	var ishttps_proxy_callback_defined interface{} = this.ValueIsDefined(this.Https_proxy_callback)
	if IsTrue(IsTrue(ishttpsProxyCallbackDefined) || IsTrue(ishttps_proxy_callback_defined)) {
		AppendToArray(&usedProxies, "httpsProxyCallback")
		httpsProxy = Ternary(IsTrue(ishttpsProxyCallbackDefined), this.callDynamically("httpsProxyCallback", url, method, headers, body), this.callDynamically("https_proxy_callback", url, method, headers, body))
	}
	// socksProxy
	var isSocksProxyDefined interface{} = this.ValueIsDefined(this.SocksProxy)
	var isSocks_proxy_defined interface{} = this.ValueIsDefined(this.Socks_proxy)
	if IsTrue(IsTrue(isSocksProxyDefined) || IsTrue(isSocks_proxy_defined)) {
		AppendToArray(&usedProxies, "socksProxy")
		socksProxy = Ternary(IsTrue(isSocksProxyDefined), this.SocksProxy, this.Socks_proxy)
	}
	var issocksProxyCallbackDefined interface{} = this.ValueIsDefined(this.SocksProxyCallback)
	var issocks_proxy_callback_defined interface{} = this.ValueIsDefined(this.Socks_proxy_callback)
	if IsTrue(IsTrue(issocksProxyCallbackDefined) || IsTrue(issocks_proxy_callback_defined)) {
		AppendToArray(&usedProxies, "socksProxyCallback")
		socksProxy = Ternary(IsTrue(issocksProxyCallbackDefined), this.callDynamically("socksProxyCallback", url, method, headers, body), this.callDynamically("socks_proxy_callback", url, method, headers, body))
	}
	// check
	var length interface{} = GetArrayLength(usedProxies)
	if IsTrue(IsGreaterThan(length, 1)) {
		var joinedProxyNames interface{} = Join(usedProxies, ",")
		panic(InvalidProxySettings(Add(Add(Add(this.Id, " you have multiple conflicting proxy settings ("), joinedProxyNames), "), please use only one from: httpProxy, httpsProxy, httpProxyCallback, httpsProxyCallback, socksProxy, socksProxyCallback")))
	}
	return []interface{}{httpProxy, httpsProxy, socksProxy}
}
func (this *Exchange) CheckWsProxySettings() interface{} {
	var usedProxies interface{} = []interface{}{}
	var wsProxy interface{} = nil
	var wssProxy interface{} = nil
	var wsSocksProxy interface{} = nil
	// ws proxy
	var isWsProxyDefined interface{} = this.ValueIsDefined(this.WsProxy)
	var is_ws_proxy_defined interface{} = this.ValueIsDefined(this.Ws_proxy)
	if IsTrue(IsTrue(isWsProxyDefined) || IsTrue(is_ws_proxy_defined)) {
		AppendToArray(&usedProxies, "wsProxy")
		wsProxy = Ternary(IsTrue((isWsProxyDefined)), this.WsProxy, this.Ws_proxy)
	}
	// wss proxy
	var isWssProxyDefined interface{} = this.ValueIsDefined(this.WssProxy)
	var is_wss_proxy_defined interface{} = this.ValueIsDefined(this.Wss_proxy)
	if IsTrue(IsTrue(isWssProxyDefined) || IsTrue(is_wss_proxy_defined)) {
		AppendToArray(&usedProxies, "wssProxy")
		wssProxy = Ternary(IsTrue((isWssProxyDefined)), this.WssProxy, this.Wss_proxy)
	}
	// ws socks proxy
	var isWsSocksProxyDefined interface{} = this.ValueIsDefined(this.WsSocksProxy)
	var is_ws_socks_proxy_defined interface{} = this.ValueIsDefined(this.Ws_socks_proxy)
	if IsTrue(IsTrue(isWsSocksProxyDefined) || IsTrue(is_ws_socks_proxy_defined)) {
		AppendToArray(&usedProxies, "wsSocksProxy")
		wsSocksProxy = Ternary(IsTrue((isWsSocksProxyDefined)), this.WsSocksProxy, this.Ws_socks_proxy)
	}
	// check
	var length interface{} = GetArrayLength(usedProxies)
	if IsTrue(IsGreaterThan(length, 1)) {
		var joinedProxyNames interface{} = Join(usedProxies, ",")
		panic(InvalidProxySettings(Add(Add(Add(this.Id, " you have multiple conflicting proxy settings ("), joinedProxyNames), "), please use only one from: wsProxy, wssProxy, wsSocksProxy")))
	}
	return []interface{}{wsProxy, wssProxy, wsSocksProxy}
}
func (this *Exchange) CheckConflictingProxies(proxyAgentSet interface{}, proxyUrlSet interface{}) {
	if IsTrue(IsTrue(proxyAgentSet) && IsTrue(proxyUrlSet)) {
		panic(InvalidProxySettings(Add(this.Id, " you have multiple conflicting proxy settings, please use only one from : proxyUrl, httpProxy, httpsProxy, socksProxy")))
	}
}
func (this *Exchange) CheckAddress(optionalArgs ...interface{}) interface{} {
	address := GetArg(optionalArgs, 0, nil)
	_ = address
	if IsTrue(IsEqual(address, nil)) {
		panic(InvalidAddress(Add(this.Id, " address is undefined")))
	}
	// check the address is not the same letter like 'aaaaa' nor too short nor has a space
	var uniqChars interface{} = (this.Unique(this.StringToCharsArray(address)))
	var length interface{} = GetArrayLength(uniqChars) // py transpiler trick
	if IsTrue(IsTrue(IsTrue(IsEqual(length, 1)) || IsTrue(IsLessThan(GetLength(address), this.MinFundingAddressLength))) || IsTrue(IsGreaterThan(GetIndexOf(address, " "), OpNeg(1)))) {
		panic(InvalidAddress(Add(Add(Add(Add(Add(this.Id, " address is invalid or has less than "), ToString(this.MinFundingAddressLength)), " characters: \""), ToString(address)), "\"")))
	}
	return address
}
func (this *Exchange) FindMessageHashes(client Client, element interface{}) interface{} {
	var result interface{} = []interface{}{}
	var messageHashes interface{} = ObjectKeys(client.Futures)
	for i := 0; IsLessThan(i, GetArrayLength(messageHashes)); i++ {
		var messageHash interface{} = GetValue(messageHashes, i)
		if IsTrue(IsGreaterThanOrEqual(GetIndexOf(messageHash, element), 0)) {
			AppendToArray(&result, messageHash)
		}
	}
	return result
}
func (this *Exchange) FilterByLimit(array interface{}, optionalArgs ...interface{}) interface{} {
	// array = ascending ? this.arraySlice (array, 0, limit) : this.arraySlice (array, -limit);
	// array = ascending ? this.arraySlice (array, -limit) : this.arraySlice (array, 0, limit);
	limit := GetArg(optionalArgs, 0, nil)
	_ = limit
	key := GetArg(optionalArgs, 1, "timestamp")
	_ = key
	fromStart := GetArg(optionalArgs, 2, false)
	_ = fromStart
	if IsTrue(this.ValueIsDefined(limit)) {
		var arrayLength interface{} = GetArrayLength(array)
		if IsTrue(IsGreaterThan(arrayLength, 0)) {
			var ascending interface{} = true
			if IsTrue((InOp(GetValue(array, 0), key))) {
				var first interface{} = GetValue(GetValue(array, 0), key)
				var last interface{} = GetValue(GetValue(array, Subtract(arrayLength, 1)), key)
				if IsTrue(IsTrue(!IsEqual(first, nil)) && IsTrue(!IsEqual(last, nil))) {
					ascending = IsLessThanOrEqual(first, last) // true if array is sorted in ascending order based on 'timestamp'
				}
			}
			if IsTrue(fromStart) {
				if IsTrue(IsGreaterThan(limit, arrayLength)) {
					limit = arrayLength
				}
				if IsTrue(ascending) {
					array = this.ArraySlice(array, 0, limit)
				} else {
					array = this.ArraySlice(array, OpNeg(limit))
				}
			} else {
				if IsTrue(ascending) {
					array = this.ArraySlice(array, OpNeg(limit))
				} else {
					array = this.ArraySlice(array, 0, limit)
				}
			}
		}
	}
	return array
}
func (this *Exchange) FilterBySinceLimit(array interface{}, optionalArgs ...interface{}) interface{} {
	since := GetArg(optionalArgs, 0, nil)
	_ = since
	limit := GetArg(optionalArgs, 1, nil)
	_ = limit
	key := GetArg(optionalArgs, 2, "timestamp")
	_ = key
	tail := GetArg(optionalArgs, 3, false)
	_ = tail
	var sinceIsDefined interface{} = this.ValueIsDefined(since)
	var parsedArray interface{} = this.ToArray(array)
	var result interface{} = parsedArray
	if IsTrue(sinceIsDefined) {
		result = []interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(parsedArray)); i++ {
			var entry interface{} = GetValue(parsedArray, i)
			var value interface{} = this.SafeValue(entry, key)
			if IsTrue(IsTrue(value) && IsTrue((IsGreaterThanOrEqual(value, since)))) {
				AppendToArray(&result, entry)
			}
		}
	}
	if IsTrue(IsTrue(tail) && IsTrue(!IsEqual(limit, nil))) {
		return this.ArraySlice(result, OpNeg(limit))
	}
	// if the user provided a 'since' argument
	// we want to limit the result starting from the 'since'
	var shouldFilterFromStart interface{} = !IsTrue(tail) && IsTrue(sinceIsDefined)
	return this.FilterByLimit(result, limit, key, shouldFilterFromStart)
}
func (this *Exchange) FilterByValueSinceLimit(array interface{}, field interface{}, optionalArgs ...interface{}) interface{} {
	value := GetArg(optionalArgs, 0, nil)
	_ = value
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	key := GetArg(optionalArgs, 3, "timestamp")
	_ = key
	tail := GetArg(optionalArgs, 4, false)
	_ = tail
	var valueIsDefined interface{} = this.ValueIsDefined(value)
	var sinceIsDefined interface{} = this.ValueIsDefined(since)
	var parsedArray interface{} = this.ToArray(array)
	var result interface{} = parsedArray
	// single-pass filter for both symbol and since
	if IsTrue(IsTrue(valueIsDefined) || IsTrue(sinceIsDefined)) {
		result = []interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(parsedArray)); i++ {
			var entry interface{} = GetValue(parsedArray, i)
			var entryFiledEqualValue interface{} = IsEqual(GetValue(entry, field), value)
			var firstCondition interface{} = Ternary(IsTrue(valueIsDefined), entryFiledEqualValue, true)
			var entryKeyValue interface{} = this.SafeValue(entry, key)
			var entryKeyGESince interface{} = IsTrue(IsTrue((entryKeyValue)) && IsTrue((!IsEqual(since, nil)))) && IsTrue((IsGreaterThanOrEqual(entryKeyValue, since)))
			var secondCondition interface{} = Ternary(IsTrue(sinceIsDefined), entryKeyGESince, true)
			if IsTrue(IsTrue(firstCondition) && IsTrue(secondCondition)) {
				AppendToArray(&result, entry)
			}
		}
	}
	if IsTrue(IsTrue(tail) && IsTrue(!IsEqual(limit, nil))) {
		return this.ArraySlice(result, OpNeg(limit))
	}
	return this.FilterByLimit(result, limit, key, sinceIsDefined)
}

/**
 * @method
 * @name Exchange#setSandboxMode
 * @description set the sandbox mode for the exchange
 * @param {boolean} enabled true to enable sandbox mode, false to disable it
 */
func (this *Exchange) SetSandboxMode(enabled interface{}) {
	if IsTrue(enabled) {
		if IsTrue(InOp(this.Urls, "test")) {
			if IsTrue(IsString(GetValue(this.Urls, "api"))) {
				AddElementToObject(this.Urls, "apiBackup", GetValue(this.Urls, "api"))
				AddElementToObject(this.Urls, "api", GetValue(this.Urls, "test"))
			} else {
				AddElementToObject(this.Urls, "apiBackup", this.Clone(GetValue(this.Urls, "api")))
				AddElementToObject(this.Urls, "api", this.Clone(GetValue(this.Urls, "test")))
			}
		} else {
			panic(NotSupported(Add(this.Id, " does not have a sandbox URL")))
		}
		// set flag
		this.IsSandboxModeEnabled = true
	} else if IsTrue(InOp(this.Urls, "apiBackup")) {
		if IsTrue(IsString(GetValue(this.Urls, "api"))) {
			AddElementToObject(this.Urls, "api", GetValue(this.Urls, "apiBackup"))
		} else {
			AddElementToObject(this.Urls, "api", this.Clone(GetValue(this.Urls, "apiBackup")))
		}
		var newUrls interface{} = this.Omit(this.Urls, "apiBackup")
		this.Urls = newUrls
		// set flag
		this.IsSandboxModeEnabled = false
	}
}
func (this *Exchange) Sign(path interface{}, optionalArgs ...interface{}) interface{} {
	api := GetArg(optionalArgs, 0, "public")
	_ = api
	method := GetArg(optionalArgs, 1, "GET")
	_ = method
	params := GetArg(optionalArgs, 2, map[string]interface{}{})
	_ = params
	headers := GetArg(optionalArgs, 3, nil)
	_ = headers
	body := GetArg(optionalArgs, 4, nil)
	_ = body
	return map[string]interface{}{}
}
func (this *Exchange) FetchAccounts(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchAccounts() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchTrades(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchTrades() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchTradesWs(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchTradesWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchLiquidations(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "watchLiquidationsForSymbols")) {

			retRes253319 := (<-this.WatchLiquidationsForSymbols([]interface{}{symbol}, since, limit, params))
			PanicOnError(retRes253319)
			ch <- retRes253319
			return nil
		}
		panic(NotSupported(Add(this.Id, " watchLiquidations() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchLiquidationsForSymbols(symbols interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchLiquidationsForSymbols() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchMyLiquidations(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "watchMyLiquidationsForSymbols")) {

			ch <- this.WatchMyLiquidationsForSymbols([]interface{}{symbol}, since, limit, params)
			return nil
		}
		panic(NotSupported(Add(this.Id, " watchMyLiquidations() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchMyLiquidationsForSymbols(symbols interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchMyLiquidationsForSymbols() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchTrades(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchTrades() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) UnWatchOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " unWatchOrders() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) UnWatchTrades(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " unWatchTrades() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchTradesForSymbols(symbols interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchTradesForSymbols() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) UnWatchTradesForSymbols(symbols interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " unWatchTradesForSymbols() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchMyTradesForSymbols(symbols interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchMyTradesForSymbols() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchOrdersForSymbols(symbols interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchOrdersForSymbols() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchOHLCVForSymbols(symbolsAndTimeframes interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchOHLCVForSymbols() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) UnWatchOHLCVForSymbols(symbolsAndTimeframes interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " unWatchOHLCVForSymbols() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchOrderBookForSymbols(symbols interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		limit := GetArg(optionalArgs, 0, nil)
		_ = limit
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchOrderBookForSymbols() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) UnWatchOrderBookForSymbols(symbols interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " unWatchOrderBookForSymbols() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) UnWatchPositions(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " unWatchPositions() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) UnWatchTicker(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " unWatchTicker() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchDepositAddresses(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		codes := GetArg(optionalArgs, 0, nil)
		_ = codes
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchDepositAddresses() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchOrderBook(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		limit := GetArg(optionalArgs, 0, nil)
		_ = limit
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchOrderBook() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchOrderBookWs(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		limit := GetArg(optionalArgs, 0, nil)
		_ = limit
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchOrderBookWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchMarginMode(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchMarginModes")) {

			marginModes := <-this.DerivedExchange.FetchMarginModes([]interface{}{symbol}, params)
			PanicOnError(marginModes)

			ch <- this.SafeDict(marginModes, symbol)
			return nil
		} else {
			panic(NotSupported(Add(this.Id, " fetchMarginMode() is not supported yet")))
		}

	}()
	return ch
}
func (this *Exchange) FetchMarginModes(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchMarginModes () is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchRestOrderBookSafe(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		limit := GetArg(optionalArgs, 0, nil)
		_ = limit
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		var fetchSnapshotMaxRetries interface{} = this.HandleOption("watchOrderBook", "maxRetries", 3)
		for i := 0; IsLessThan(i, fetchSnapshotMaxRetries); i++ {

			{
				func(this *Exchange) (ret_ interface{}) {
					defer func() {
						if e := recover(); e != nil {
							if e == "break" {
								return
							}
							ret_ = func(this *Exchange) interface{} {
								// catch block:
								if IsTrue(IsEqual((Add(i, 1)), fetchSnapshotMaxRetries)) {
									panic(e)
								}
								return nil
							}(this)
						}
					}()
					// try block:

					orderBook := <-this.DerivedExchange.FetchOrderBook(symbol, limit, params)
					PanicOnError(orderBook)

					ch <- orderBook
					return nil

				}(this)

			}
		}

		return nil

	}()
	return ch
}
func (this *Exchange) WatchOrderBook(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		limit := GetArg(optionalArgs, 0, nil)
		_ = limit
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchOrderBook() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) UnWatchOrderBook(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " unWatchOrderBook() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchTime(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchTime() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchTradingLimits(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchTradingLimits() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) ParseCurrency(rawCurrency interface{}) interface{} {
	panic(NotSupported(Add(this.Id, " parseCurrency() is not supported yet")))
}
func (this *Exchange) ParseCurrencies(rawCurrencies interface{}) interface{} {
	var result interface{} = map[string]interface{}{}
	var arr interface{} = this.ToArray(rawCurrencies)
	for i := 0; IsLessThan(i, GetArrayLength(arr)); i++ {

		var parsed interface{} = this.DerivedExchange.ParseCurrency(GetValue(arr, i))
		PanicOnError(parsed)
		var code interface{} = GetValue(parsed, "code")
		AddElementToObject(result, code, parsed)
	}
	return result
}
func (this *Exchange) ParseMarket(market interface{}) interface{} {
	panic(NotSupported(Add(this.Id, " parseMarket() is not supported yet")))
}
func (this *Exchange) ParseMarkets(markets interface{}) interface{} {
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(markets)); i++ {
		AppendToArray(&result, this.DerivedExchange.ParseMarket(GetValue(markets, i)))
	}
	return result
}
func (this *Exchange) ParseTicker(ticker interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseTicker() is not supported yet")))
}
func (this *Exchange) ParseDepositAddress(depositAddress interface{}, optionalArgs ...interface{}) interface{} {
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	panic(NotSupported(Add(this.Id, " parseDepositAddress() is not supported yet")))
}
func (this *Exchange) ParseTrade(trade interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseTrade() is not supported yet")))
}
func (this *Exchange) ParseTransaction(transaction interface{}, optionalArgs ...interface{}) interface{} {
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	panic(NotSupported(Add(this.Id, " parseTransaction() is not supported yet")))
}
func (this *Exchange) ParseTransfer(transfer interface{}, optionalArgs ...interface{}) interface{} {
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	panic(NotSupported(Add(this.Id, " parseTransfer() is not supported yet")))
}
func (this *Exchange) ParseAccount(account interface{}) interface{} {
	panic(NotSupported(Add(this.Id, " parseAccount() is not supported yet")))
}
func (this *Exchange) ParseLedgerEntry(item interface{}, optionalArgs ...interface{}) interface{} {
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	panic(NotSupported(Add(this.Id, " parseLedgerEntry() is not supported yet")))
}
func (this *Exchange) ParseOrder(order interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseOrder() is not supported yet")))
}
func (this *Exchange) FetchCrossBorrowRates(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchCrossBorrowRates() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchIsolatedBorrowRates(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchIsolatedBorrowRates() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) ParseMarketLeverageTiers(info interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseMarketLeverageTiers() is not supported yet")))
}
func (this *Exchange) FetchLeverageTiers(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchLeverageTiers() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) ParsePosition(position interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parsePosition() is not supported yet")))
}
func (this *Exchange) ParseFundingRateHistory(info interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseFundingRateHistory() is not supported yet")))
}
func (this *Exchange) ParseBorrowInterest(info interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseBorrowInterest() is not supported yet")))
}
func (this *Exchange) ParseIsolatedBorrowRate(info interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseIsolatedBorrowRate() is not supported yet")))
}
func (this *Exchange) ParseWsTrade(trade interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseWsTrade() is not supported yet")))
}
func (this *Exchange) ParseWsOrder(order interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseWsOrder() is not supported yet")))
}
func (this *Exchange) ParseWsOrderTrade(trade interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseWsOrderTrade() is not supported yet")))
}
func (this *Exchange) ParseWsOHLCV(ohlcv interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	return this.DerivedExchange.ParseOHLCV(ohlcv, market)
}
func (this *Exchange) FetchFundingRates(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchFundingRates() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchFundingIntervals(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchFundingIntervals() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchFundingRate(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchFundingRate() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchFundingRates(symbols interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchFundingRates() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchFundingRatesForSymbols(symbols interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes278515 := (<-this.WatchFundingRates(symbols, params))
		PanicOnError(retRes278515)
		ch <- retRes278515
		return nil

	}()
	return ch
}
func (this *Exchange) Transfer(code interface{}, amount interface{}, fromAccount interface{}, toAccount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " transfer() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) Withdraw(code interface{}, amount interface{}, address interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		tag := GetArg(optionalArgs, 0, nil)
		_ = tag
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " withdraw() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateDepositAddress(code interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " createDepositAddress() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) SetLeverage(leverage interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " setLeverage() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchLeverage(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchLeverages")) {

			leverages := <-this.DerivedExchange.FetchLeverages([]interface{}{symbol}, params)
			PanicOnError(leverages)

			ch <- this.SafeDict(leverages, symbol)
			return nil
		} else {
			panic(NotSupported(Add(this.Id, " fetchLeverage() is not supported yet")))
		}

	}()
	return ch
}
func (this *Exchange) FetchLeverages(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchLeverages() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) SetPositionMode(hedged interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " setPositionMode() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) AddMargin(symbol interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " addMargin() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) ReduceMargin(symbol interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " reduceMargin() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) SetMargin(symbol interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " setMargin() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchLongShortRatio(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		timeframe := GetArg(optionalArgs, 0, nil)
		_ = timeframe
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchLongShortRatio() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchLongShortRatioHistory(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		timeframe := GetArg(optionalArgs, 1, nil)
		_ = timeframe
		since := GetArg(optionalArgs, 2, nil)
		_ = since
		limit := GetArg(optionalArgs, 3, nil)
		_ = limit
		params := GetArg(optionalArgs, 4, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchLongShortRatioHistory() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchMarginAdjustmentHistory(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		typeVar := GetArg(optionalArgs, 1, nil)
		_ = typeVar
		since := GetArg(optionalArgs, 2, nil)
		_ = since
		limit := GetArg(optionalArgs, 3, nil)
		_ = limit
		params := GetArg(optionalArgs, 4, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchMarginAdjustmentHistory() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) SetMarginMode(marginMode interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " setMarginMode() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchDepositAddressesByNetwork(code interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchDepositAddressesByNetwork() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchOpenInterestHistory(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		timeframe := GetArg(optionalArgs, 0, "1h")
		_ = timeframe
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchOpenInterestHistory() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchOpenInterest(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchOpenInterest() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchOpenInterests(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchOpenInterests() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) SignIn(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " signIn() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchPaymentMethods(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchPaymentMethods() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) ParseToInt(number interface{}) interface{} {
	// Solve Common parseInt misuse ex: parseInt ((since / 1000).toString ())
	// using a number as parameter which is not valid in ts
	var stringifiedNumber interface{} = this.NumberToString(number)
	var convertedNumber interface{} = ParseFloat(stringifiedNumber)
	return ParseInt(convertedNumber)
}
func (this *Exchange) ParseToNumeric(number interface{}) interface{} {
	var stringVersion interface{} = this.NumberToString(number) // this will convert 1.0 and 1 to "1" and 1.1 to "1.1"
	// keep this in mind:
	// in JS:     1 === 1.0 is true
	// in Python: 1 == 1.0 is true
	// in PHP:    1 == 1.0 is true, but 1 === 1.0 is false.
	if IsTrue(IsGreaterThanOrEqual(GetIndexOf(stringVersion, "."), 0)) {
		return ParseFloat(stringVersion)
	}
	return ParseInt(stringVersion)
}
func (this *Exchange) IsRoundNumber(value interface{}) interface{} {
	// this method is similar to isInteger, but this is more loyal and does not check for types.
	// i.e. isRoundNumber(1.000) returns true, while isInteger(1.000) returns false
	var res interface{} = this.ParseToNumeric((Mod(value, 1)))
	return IsEqual(res, 0)
}
func (this *Exchange) SafeNumberOmitZero(obj interface{}, key interface{}, optionalArgs ...interface{}) interface{} {
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	var value interface{} = this.SafeString(obj, key)
	var final interface{} = this.ParseNumber(this.OmitZero(value))
	return Ternary(IsTrue((IsEqual(final, nil))), defaultValue, final)
}
func (this *Exchange) SafeIntegerOmitZero(obj interface{}, key interface{}, optionalArgs ...interface{}) interface{} {
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	var timestamp interface{} = this.SafeInteger(obj, key, defaultValue)
	if IsTrue(IsTrue(IsEqual(timestamp, nil)) || IsTrue(IsEqual(timestamp, 0))) {
		return nil
	}
	return timestamp
}
func (this *Exchange) AfterConstruct() {
	// networks
	this.CreateNetworksByIdObject()
	this.FeaturesGenerator()
	// init predefined markets if any
	if IsTrue(this.Markets) {
		this.SetMarkets(this.Markets)
	}
	// init the request rate limiter
	this.InitRestRateLimiter()
	// sanbox mode
	var isSandbox interface{} = this.SafeBool2(this.Options, "sandbox", "testnet", false)
	if IsTrue(isSandbox) {
		this.DerivedExchange.SetSandboxMode(isSandbox)
	}
}
func (this *Exchange) InitRestRateLimiter() {
	if IsTrue(IsTrue(IsEqual(this.RateLimit, nil)) || IsTrue((IsTrue(!IsEqual(this.Id, nil)) && IsTrue(IsEqual(this.RateLimit, OpNeg(1)))))) {
		panic(ExchangeError(Add(this.Id, ".rateLimit property is not configured")))
	}
	var refillRate interface{} = this.MAX_VALUE
	if IsTrue(IsGreaterThan(this.RateLimit, 0)) {
		refillRate = Divide(1, this.RateLimit)
	}
	var defaultBucket interface{} = map[string]interface{}{
		"delay":       0.001,
		"capacity":    1,
		"cost":        1,
		"maxCapacity": 1000,
		"refillRate":  refillRate,
	}
	var existingBucket interface{} = Ternary(IsTrue((IsEqual(this.TokenBucket, nil))), map[string]interface{}{}, this.TokenBucket)
	this.TokenBucket = this.Extend(defaultBucket, existingBucket)
	this.InitThrottler()
}
func (this *Exchange) FeaturesGenerator() {
	//
	// in the exchange-specific features can be something like this, where we support 'string' aliases too:
	//
	//     {
	//         'my' : {
	//             'createOrder' : {...},
	//         },
	//         'swap': {
	//             'linear': {
	//                 'extends': my',
	//             },
	//         },
	//     }
	//
	if IsTrue(IsEqual(this.Features, nil)) {
		return
	}
	// reconstruct
	var initialFeatures interface{} = this.Features
	this.Features = map[string]interface{}{}
	var unifiedMarketTypes interface{} = []interface{}{"spot", "swap", "future", "option"}
	var subTypes interface{} = []interface{}{"linear", "inverse"}
	// atm only support basic methods, eg: 'createOrder', 'fetchOrder', 'fetchOrders', 'fetchMyTrades'
	for i := 0; IsLessThan(i, GetArrayLength(unifiedMarketTypes)); i++ {
		var marketType interface{} = GetValue(unifiedMarketTypes, i)
		// if marketType is not filled for this exchange, don't add that in `features`
		if !IsTrue((InOp(initialFeatures, marketType))) {
			AddElementToObject(this.Features, marketType, nil)
		} else {
			if IsTrue(IsEqual(marketType, "spot")) {
				AddElementToObject(this.Features, marketType, this.FeaturesMapper(initialFeatures, marketType, nil))
			} else {
				AddElementToObject(this.Features, marketType, map[string]interface{}{})
				for j := 0; IsLessThan(j, GetArrayLength(subTypes)); j++ {
					var subType interface{} = GetValue(subTypes, j)
					AddElementToObject(GetValue(this.Features, marketType), subType, this.FeaturesMapper(initialFeatures, marketType, subType))
				}
			}
		}
	}
}
func (this *Exchange) FeaturesMapper(initialFeatures interface{}, marketType interface{}, optionalArgs ...interface{}) interface{} {
	subType := GetArg(optionalArgs, 0, nil)
	_ = subType
	var featuresObj interface{} = Ternary(IsTrue((!IsEqual(subType, nil))), GetValue(GetValue(initialFeatures, marketType), subType), GetValue(initialFeatures, marketType))
	// if exchange does not have that market-type (eg. future>inverse)
	if IsTrue(IsEqual(featuresObj, nil)) {
		return nil
	}
	var extendsStr interface{} = this.SafeString(featuresObj, "extends")
	if IsTrue(!IsEqual(extendsStr, nil)) {
		featuresObj = this.Omit(featuresObj, "extends")
		var extendObj interface{} = this.FeaturesMapper(initialFeatures, extendsStr)
		featuresObj = this.DeepExtend(extendObj, featuresObj)
	}
	//
	// ### corrections ###
	//
	// createOrder
	if IsTrue(InOp(featuresObj, "createOrder")) {
		var value interface{} = this.SafeDict(GetValue(featuresObj, "createOrder"), "attachedStopLossTakeProfit")
		AddElementToObject(GetValue(featuresObj, "createOrder"), "stopLoss", value)
		AddElementToObject(GetValue(featuresObj, "createOrder"), "takeProfit", value)
		if IsTrue(IsEqual(marketType, "spot")) {
			// default 'hedged': false
			AddElementToObject(GetValue(featuresObj, "createOrder"), "hedged", false)
			// default 'leverage': false
			if !IsTrue((InOp(GetValue(featuresObj, "createOrder"), "leverage"))) {
				AddElementToObject(GetValue(featuresObj, "createOrder"), "leverage", false)
			}
		}
		// default 'GTC' to true
		if IsTrue(IsEqual(this.SafeBool(GetValue(GetValue(featuresObj, "createOrder"), "timeInForce"), "GTC"), nil)) {
			AddElementToObject(GetValue(GetValue(featuresObj, "createOrder"), "timeInForce"), "GTC", true)
		}
	}
	// other methods
	var keys interface{} = ObjectKeys(featuresObj)
	for i := 0; IsLessThan(i, GetArrayLength(keys)); i++ {
		var key interface{} = GetValue(keys, i)
		var featureBlock interface{} = GetValue(featuresObj, key)
		if IsTrue(!IsTrue(this.InArray(key, []interface{}{"sandbox"})) && IsTrue(!IsEqual(featureBlock, nil))) {
			// default "symbolRequired" to false to all methods (except `createOrder`)
			if !IsTrue((InOp(featureBlock, "symbolRequired"))) {
				AddElementToObject(featureBlock, "symbolRequired", this.InArray(key, []interface{}{"createOrder", "createOrders", "fetchOHLCV"}))
			}
		}
	}
	return featuresObj
}
func (this *Exchange) OrderbookChecksumMessage(symbol interface{}) interface{} {
	return Add(Add(symbol, " : "), "orderbook data checksum validation failed. You can reconnect by calling watchOrderBook again or you can mute the error by setting exchange.options[\"watchOrderBook\"][\"checksum\"] = false")
}
func (this *Exchange) CreateNetworksByIdObject() {
	// automatically generate network-id-to-code mappings
	var networkIdsToCodesGenerated interface{} = this.InvertFlatStringDictionary(this.SafeValue(this.Options, "networks", map[string]interface{}{}))                  // invert defined networks dictionary
	AddElementToObject(this.Options, "networksById", this.Extend(networkIdsToCodesGenerated, this.SafeValue(this.Options, "networksById", map[string]interface{}{}))) // support manually overriden "networksById" dictionary too
}
func (this *Exchange) GetDefaultOptions() interface{} {
	return map[string]interface{}{
		"defaultNetworkCodeReplacements": map[string]interface{}{
			"ETH": map[string]interface{}{
				"ERC20": "ETH",
			},
			"TRX": map[string]interface{}{
				"TRC20": "TRX",
			},
			"CRO": map[string]interface{}{
				"CRC20": "CRONOS",
			},
			"BRC20": map[string]interface{}{
				"BRC20": "BTC",
			},
		},
	}
}
func (this *Exchange) SafeLedgerEntry(entry interface{}, optionalArgs ...interface{}) interface{} {
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	currency = this.SafeCurrency(nil, currency)
	var direction interface{} = this.SafeString(entry, "direction")
	var before interface{} = this.SafeString(entry, "before")
	var after interface{} = this.SafeString(entry, "after")
	var amount interface{} = this.SafeString(entry, "amount")
	if IsTrue(!IsEqual(amount, nil)) {
		if IsTrue(IsTrue(IsEqual(before, nil)) && IsTrue(!IsEqual(after, nil))) {
			before = Precise.StringSub(after, amount)
		} else if IsTrue(IsTrue(!IsEqual(before, nil)) && IsTrue(IsEqual(after, nil))) {
			after = Precise.StringAdd(before, amount)
		}
	}
	if IsTrue(IsTrue(!IsEqual(before, nil)) && IsTrue(!IsEqual(after, nil))) {
		if IsTrue(IsEqual(direction, nil)) {
			if IsTrue(Precise.StringGt(before, after)) {
				direction = "out"
			}
			if IsTrue(Precise.StringGt(after, before)) {
				direction = "in"
			}
		}
	}
	var fee interface{} = this.SafeValue(entry, "fee")
	if IsTrue(!IsEqual(fee, nil)) {
		AddElementToObject(fee, "cost", this.SafeNumber(fee, "cost"))
	}
	var timestamp interface{} = this.SafeInteger(entry, "timestamp")
	var info interface{} = this.SafeDict(entry, "info", map[string]interface{}{})
	return map[string]interface{}{
		"id":               this.SafeString(entry, "id"),
		"timestamp":        timestamp,
		"datetime":         this.Iso8601(timestamp),
		"direction":        direction,
		"account":          this.SafeString(entry, "account"),
		"referenceId":      this.SafeString(entry, "referenceId"),
		"referenceAccount": this.SafeString(entry, "referenceAccount"),
		"type":             this.SafeString(entry, "type"),
		"currency":         GetValue(currency, "code"),
		"amount":           this.ParseNumber(amount),
		"before":           this.ParseNumber(before),
		"after":            this.ParseNumber(after),
		"status":           this.SafeString(entry, "status"),
		"fee":              fee,
		"info":             info,
	}
}
func (this *Exchange) SafeCurrencyStructure(currency interface{}) interface{} {
	// derive data from networks: deposit, withdraw, active, fee, limits, precision
	var networks interface{} = this.SafeDict(currency, "networks", map[string]interface{}{})
	var keys interface{} = ObjectKeys(networks)
	var length interface{} = GetArrayLength(keys)
	if IsTrue(!IsEqual(length, 0)) {
		for i := 0; IsLessThan(i, length); i++ {
			var key interface{} = GetValue(keys, i)
			var network interface{} = GetValue(networks, key)
			var deposit interface{} = this.SafeBool(network, "deposit")
			var currencyDeposit interface{} = this.SafeBool(currency, "deposit")
			if IsTrue(IsTrue(IsEqual(currencyDeposit, nil)) || IsTrue(deposit)) {
				AddElementToObject(currency, "deposit", deposit)
			}
			var withdraw interface{} = this.SafeBool(network, "withdraw")
			var currencyWithdraw interface{} = this.SafeBool(currency, "withdraw")
			if IsTrue(IsTrue(IsEqual(currencyWithdraw, nil)) || IsTrue(withdraw)) {
				AddElementToObject(currency, "withdraw", withdraw)
			}
			// set network 'active' to false if D or W is disabled
			var active interface{} = this.SafeBool(network, "active")
			if IsTrue(IsEqual(active, nil)) {
				if IsTrue(IsTrue(deposit) && IsTrue(withdraw)) {
					AddElementToObject(GetValue(GetValue(currency, "networks"), key), "active", true)
				} else if IsTrue(IsTrue(!IsEqual(deposit, nil)) && IsTrue(!IsEqual(withdraw, nil))) {
					AddElementToObject(GetValue(GetValue(currency, "networks"), key), "active", false)
				}
			}
			active = this.SafeBool(GetValue(GetValue(currency, "networks"), key), "active") // dict might have been updated on above lines, so access directly instead of `network` variable
			var currencyActive interface{} = this.SafeBool(currency, "active")
			if IsTrue(IsTrue(IsEqual(currencyActive, nil)) || IsTrue(active)) {
				AddElementToObject(currency, "active", active)
			}
			// find lowest fee (which is more desired)
			var fee interface{} = this.SafeString(network, "fee")
			var feeMain interface{} = this.SafeString(currency, "fee")
			if IsTrue(IsTrue(IsEqual(feeMain, nil)) || IsTrue(Precise.StringLt(fee, feeMain))) {
				AddElementToObject(currency, "fee", this.ParseNumber(fee))
			}
			// find lowest precision (which is more desired)
			var precision interface{} = this.SafeString(network, "precision")
			var precisionMain interface{} = this.SafeString(currency, "precision")
			if IsTrue(IsTrue(IsEqual(precisionMain, nil)) || IsTrue(Precise.StringGt(precision, precisionMain))) {
				AddElementToObject(currency, "precision", this.ParseNumber(precision))
			}
			// limits
			var limits interface{} = this.SafeDict(network, "limits")
			var limitsMain interface{} = this.SafeDict(currency, "limits")
			if IsTrue(IsEqual(limitsMain, nil)) {
				AddElementToObject(currency, "limits", map[string]interface{}{})
			}
			// deposits
			var limitsDeposit interface{} = this.SafeDict(limits, "deposit")
			var limitsDepositMain interface{} = this.SafeDict(limitsMain, "deposit")
			if IsTrue(IsEqual(limitsDepositMain, nil)) {
				AddElementToObject(GetValue(currency, "limits"), "deposit", map[string]interface{}{})
			}
			var limitsDepositMin interface{} = this.SafeString(limitsDeposit, "min")
			var limitsDepositMax interface{} = this.SafeString(limitsDeposit, "max")
			var limitsDepositMinMain interface{} = this.SafeString(limitsDepositMain, "min")
			var limitsDepositMaxMain interface{} = this.SafeString(limitsDepositMain, "max")
			// find min
			if IsTrue(IsTrue(IsEqual(limitsDepositMinMain, nil)) || IsTrue(Precise.StringLt(limitsDepositMin, limitsDepositMinMain))) {
				AddElementToObject(GetValue(GetValue(currency, "limits"), "deposit"), "min", this.ParseNumber(limitsDepositMin))
			}
			// find max
			if IsTrue(IsTrue(IsEqual(limitsDepositMaxMain, nil)) || IsTrue(Precise.StringGt(limitsDepositMax, limitsDepositMaxMain))) {
				AddElementToObject(GetValue(GetValue(currency, "limits"), "deposit"), "max", this.ParseNumber(limitsDepositMax))
			}
			// withdrawals
			var limitsWithdraw interface{} = this.SafeDict(limits, "withdraw")
			var limitsWithdrawMain interface{} = this.SafeDict(limitsMain, "withdraw")
			if IsTrue(IsEqual(limitsWithdrawMain, nil)) {
				AddElementToObject(GetValue(currency, "limits"), "withdraw", map[string]interface{}{})
			}
			var limitsWithdrawMin interface{} = this.SafeString(limitsWithdraw, "min")
			var limitsWithdrawMax interface{} = this.SafeString(limitsWithdraw, "max")
			var limitsWithdrawMinMain interface{} = this.SafeString(limitsWithdrawMain, "min")
			var limitsWithdrawMaxMain interface{} = this.SafeString(limitsWithdrawMain, "max")
			// find min
			if IsTrue(IsTrue(IsEqual(limitsWithdrawMinMain, nil)) || IsTrue(Precise.StringLt(limitsWithdrawMin, limitsWithdrawMinMain))) {
				AddElementToObject(GetValue(GetValue(currency, "limits"), "withdraw"), "min", this.ParseNumber(limitsWithdrawMin))
			}
			// find max
			if IsTrue(IsTrue(IsEqual(limitsWithdrawMaxMain, nil)) || IsTrue(Precise.StringGt(limitsWithdrawMax, limitsWithdrawMaxMain))) {
				AddElementToObject(GetValue(GetValue(currency, "limits"), "withdraw"), "max", this.ParseNumber(limitsWithdrawMax))
			}
		}
	}
	return this.Extend(map[string]interface{}{
		"info":      nil,
		"id":        nil,
		"numericId": nil,
		"code":      nil,
		"precision": nil,
		"type":      nil,
		"name":      nil,
		"active":    nil,
		"deposit":   nil,
		"withdraw":  nil,
		"fee":       nil,
		"fees":      map[string]interface{}{},
		"networks":  map[string]interface{}{},
		"limits": map[string]interface{}{
			"deposit": map[string]interface{}{
				"min": nil,
				"max": nil,
			},
			"withdraw": map[string]interface{}{
				"min": nil,
				"max": nil,
			},
		},
	}, currency)
}
func (this *Exchange) SafeMarketStructure(optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var cleanStructure interface{} = map[string]interface{}{
		"id":             nil,
		"lowercaseId":    nil,
		"symbol":         nil,
		"base":           nil,
		"quote":          nil,
		"settle":         nil,
		"baseId":         nil,
		"quoteId":        nil,
		"settleId":       nil,
		"type":           nil,
		"spot":           nil,
		"margin":         nil,
		"swap":           nil,
		"future":         nil,
		"option":         nil,
		"index":          nil,
		"active":         nil,
		"contract":       nil,
		"linear":         nil,
		"inverse":        nil,
		"subType":        nil,
		"taker":          nil,
		"maker":          nil,
		"contractSize":   nil,
		"expiry":         nil,
		"expiryDatetime": nil,
		"strike":         nil,
		"optionType":     nil,
		"precision": map[string]interface{}{
			"amount": nil,
			"price":  nil,
			"cost":   nil,
			"base":   nil,
			"quote":  nil,
		},
		"limits": map[string]interface{}{
			"leverage": map[string]interface{}{
				"min": nil,
				"max": nil,
			},
			"amount": map[string]interface{}{
				"min": nil,
				"max": nil,
			},
			"price": map[string]interface{}{
				"min": nil,
				"max": nil,
			},
			"cost": map[string]interface{}{
				"min": nil,
				"max": nil,
			},
		},
		"marginModes": map[string]interface{}{
			"cross":    nil,
			"isolated": nil,
		},
		"created": nil,
		"info":    nil,
	}
	if IsTrue(!IsEqual(market, nil)) {
		var result interface{} = this.Extend(cleanStructure, market)
		// set undefined swap/future/etc
		if IsTrue(GetValue(result, "spot")) {
			if IsTrue(IsEqual(GetValue(result, "contract"), nil)) {
				AddElementToObject(result, "contract", false)
			}
			if IsTrue(IsEqual(GetValue(result, "swap"), nil)) {
				AddElementToObject(result, "swap", false)
			}
			if IsTrue(IsEqual(GetValue(result, "future"), nil)) {
				AddElementToObject(result, "future", false)
			}
			if IsTrue(IsEqual(GetValue(result, "option"), nil)) {
				AddElementToObject(result, "option", false)
			}
			if IsTrue(IsEqual(GetValue(result, "index"), nil)) {
				AddElementToObject(result, "index", false)
			}
		}
		return result
	}
	return cleanStructure
}
func (this *Exchange) SetMarkets(markets interface{}, optionalArgs ...interface{}) interface{} {
	currencies := GetArg(optionalArgs, 0, nil)
	_ = currencies
	var values interface{} = []interface{}{}
	this.Markets_by_id = this.CreateSafeDictionary()
	// handle marketId conflicts
	// we insert spot markets first
	var marketValues interface{} = this.SortBy(this.ToArray(markets), "spot", true, true)
	for i := 0; IsLessThan(i, GetArrayLength(marketValues)); i++ {
		var value interface{} = GetValue(marketValues, i)
		if IsTrue(InOp(this.Markets_by_id, GetValue(value, "id"))) {
			var marketsByIdArray interface{} = GetValue(this.Markets_by_id, GetValue(value, "id"))
			AppendToArray(&marketsByIdArray, value)
			AddElementToObject(this.Markets_by_id, GetValue(value, "id"), marketsByIdArray)
		} else {
			AddElementToObject(this.Markets_by_id, GetValue(value, "id"), []interface{}{value})
		}
		var market interface{} = this.DeepExtend(this.SafeMarketStructure(), map[string]interface{}{
			"precision": this.Precision,
			"limits":    this.Limits,
		}, GetValue(this.Fees, "trading"), value)
		if IsTrue(GetValue(market, "linear")) {
			AddElementToObject(market, "subType", "linear")
		} else if IsTrue(GetValue(market, "inverse")) {
			AddElementToObject(market, "subType", "inverse")
		} else {
			AddElementToObject(market, "subType", nil)
		}
		AppendToArray(&values, market)
	}
	this.Markets = this.MapToSafeMap(this.IndexBy(values, "symbol"))
	var marketsSortedBySymbol interface{} = this.Keysort(this.Markets)
	var marketsSortedById interface{} = this.Keysort(this.Markets_by_id)
	this.Symbols = ObjectKeys(marketsSortedBySymbol)
	this.Ids = ObjectKeys(marketsSortedById)
	if IsTrue(!IsEqual(currencies, nil)) {
		// currencies is always undefined when called in constructor but not when called from loadMarkets
		this.Currencies = this.MapToSafeMap(this.DeepExtend(this.Currencies, currencies))
	} else {
		var baseCurrencies interface{} = []interface{}{}
		var quoteCurrencies interface{} = []interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(values)); i++ {
			var market interface{} = GetValue(values, i)
			var defaultCurrencyPrecision interface{} = Ternary(IsTrue((IsEqual(this.PrecisionMode, DECIMAL_PLACES))), 8, this.ParseNumber("1e-8"))
			var marketPrecision interface{} = this.SafeDict(market, "precision", map[string]interface{}{})
			if IsTrue(InOp(market, "base")) {
				var currency interface{} = this.SafeCurrencyStructure(map[string]interface{}{
					"id":        this.SafeString2(market, "baseId", "base"),
					"numericId": this.SafeInteger(market, "baseNumericId"),
					"code":      this.SafeString(market, "base"),
					"precision": this.SafeValue2(marketPrecision, "base", "amount", defaultCurrencyPrecision),
				})
				AppendToArray(&baseCurrencies, currency)
			}
			if IsTrue(InOp(market, "quote")) {
				var currency interface{} = this.SafeCurrencyStructure(map[string]interface{}{
					"id":        this.SafeString2(market, "quoteId", "quote"),
					"numericId": this.SafeInteger(market, "quoteNumericId"),
					"code":      this.SafeString(market, "quote"),
					"precision": this.SafeValue2(marketPrecision, "quote", "price", defaultCurrencyPrecision),
				})
				AppendToArray(&quoteCurrencies, currency)
			}
		}
		baseCurrencies = this.SortBy(baseCurrencies, "code", false, "")
		quoteCurrencies = this.SortBy(quoteCurrencies, "code", false, "")
		this.BaseCurrencies = this.MapToSafeMap(this.IndexBy(baseCurrencies, "code"))
		this.QuoteCurrencies = this.MapToSafeMap(this.IndexBy(quoteCurrencies, "code"))
		var allCurrencies interface{} = this.ArrayConcat(baseCurrencies, quoteCurrencies)
		var groupedCurrencies interface{} = this.GroupBy(allCurrencies, "code")
		var codes interface{} = ObjectKeys(groupedCurrencies)
		var resultingCurrencies interface{} = []interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(codes)); i++ {
			var code interface{} = GetValue(codes, i)
			var groupedCurrenciesCode interface{} = this.SafeList(groupedCurrencies, code, []interface{}{})
			var highestPrecisionCurrency interface{} = this.SafeValue(groupedCurrenciesCode, 0)
			for j := 1; IsLessThan(j, GetArrayLength(groupedCurrenciesCode)); j++ {
				var currentCurrency interface{} = GetValue(groupedCurrenciesCode, j)
				if IsTrue(IsEqual(this.PrecisionMode, TICK_SIZE)) {
					highestPrecisionCurrency = Ternary(IsTrue((IsLessThan(GetValue(currentCurrency, "precision"), GetValue(highestPrecisionCurrency, "precision")))), currentCurrency, highestPrecisionCurrency)
				} else {
					highestPrecisionCurrency = Ternary(IsTrue((IsGreaterThan(GetValue(currentCurrency, "precision"), GetValue(highestPrecisionCurrency, "precision")))), currentCurrency, highestPrecisionCurrency)
				}
			}
			AppendToArray(&resultingCurrencies, highestPrecisionCurrency)
		}
		var sortedCurrencies interface{} = this.SortBy(resultingCurrencies, "code")
		this.Currencies = this.MapToSafeMap(this.DeepExtend(this.Currencies, this.IndexBy(sortedCurrencies, "code")))
	}
	this.Currencies_by_id = this.IndexBySafe(this.Currencies, "id")
	var currenciesSortedByCode interface{} = this.Keysort(this.Currencies)
	this.Codes = ObjectKeys(currenciesSortedByCode)
	return this.Markets
}
func (this *Exchange) GetDescribeForExtendedWsExchange(currentRestInstance Exchange, parentRestInstance Exchange, wsBaseDescribe interface{}) interface{} {
	var extendedRestDescribe interface{} = this.DeepExtend(parentRestInstance.Describe(), currentRestInstance.Describe())
	var superWithRestDescribe interface{} = this.DeepExtend(extendedRestDescribe, wsBaseDescribe)
	return superWithRestDescribe
}
func (this *Exchange) SafeBalance(balance interface{}) interface{} {
	var balances interface{} = this.Omit(balance, []interface{}{"info", "timestamp", "datetime", "free", "used", "total"})
	var codes interface{} = ObjectKeys(balances)
	AddElementToObject(balance, "free", map[string]interface{}{})
	AddElementToObject(balance, "used", map[string]interface{}{})
	AddElementToObject(balance, "total", map[string]interface{}{})
	var debtBalance interface{} = map[string]interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(codes)); i++ {
		var code interface{} = GetValue(codes, i)
		var total interface{} = this.SafeString(GetValue(balance, code), "total")
		var free interface{} = this.SafeString(GetValue(balance, code), "free")
		var used interface{} = this.SafeString(GetValue(balance, code), "used")
		var debt interface{} = this.SafeString(GetValue(balance, code), "debt")
		if IsTrue(IsTrue(IsTrue((IsEqual(total, nil))) && IsTrue((!IsEqual(free, nil)))) && IsTrue((!IsEqual(used, nil)))) {
			total = Precise.StringAdd(free, used)
		}
		if IsTrue(IsTrue(IsTrue((IsEqual(free, nil))) && IsTrue((!IsEqual(total, nil)))) && IsTrue((!IsEqual(used, nil)))) {
			free = Precise.StringSub(total, used)
		}
		if IsTrue(IsTrue(IsTrue((IsEqual(used, nil))) && IsTrue((!IsEqual(total, nil)))) && IsTrue((!IsEqual(free, nil)))) {
			used = Precise.StringSub(total, free)
		}
		AddElementToObject(GetValue(balance, code), "free", this.ParseNumber(free))
		AddElementToObject(GetValue(balance, code), "used", this.ParseNumber(used))
		AddElementToObject(GetValue(balance, code), "total", this.ParseNumber(total))
		AddElementToObject(GetValue(balance, "free"), code, GetValue(GetValue(balance, code), "free"))
		AddElementToObject(GetValue(balance, "used"), code, GetValue(GetValue(balance, code), "used"))
		AddElementToObject(GetValue(balance, "total"), code, GetValue(GetValue(balance, code), "total"))
		if IsTrue(!IsEqual(debt, nil)) {
			AddElementToObject(GetValue(balance, code), "debt", this.ParseNumber(debt))
			AddElementToObject(debtBalance, code, GetValue(GetValue(balance, code), "debt"))
		}
	}
	var debtBalanceArray interface{} = ObjectKeys(debtBalance)
	var length interface{} = GetArrayLength(debtBalanceArray)
	if IsTrue(length) {
		AddElementToObject(balance, "debt", debtBalance)
	}
	return balance
}
func (this *Exchange) SafeOrder(order interface{}, optionalArgs ...interface{}) interface{} {
	// parses numbers as strings
	// * it is important pass the trades as unparsed rawTrades
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var amount interface{} = this.OmitZero(this.SafeString(order, "amount"))
	var remaining interface{} = this.SafeString(order, "remaining")
	var filled interface{} = this.SafeString(order, "filled")
	var cost interface{} = this.SafeString(order, "cost")
	var average interface{} = this.OmitZero(this.SafeString(order, "average"))
	var price interface{} = this.OmitZero(this.SafeString(order, "price"))
	var lastTradeTimeTimestamp interface{} = this.SafeInteger(order, "lastTradeTimestamp")
	var symbol interface{} = this.SafeString(order, "symbol")
	var side interface{} = this.SafeString(order, "side")
	var status interface{} = this.SafeString(order, "status")
	var parseFilled interface{} = (IsEqual(filled, nil))
	var parseCost interface{} = (IsEqual(cost, nil))
	var parseLastTradeTimeTimestamp interface{} = (IsEqual(lastTradeTimeTimestamp, nil))
	var fee interface{} = this.SafeValue(order, "fee")
	var parseFee interface{} = (IsEqual(fee, nil))
	var parseFees interface{} = IsEqual(this.SafeValue(order, "fees"), nil)
	var parseSymbol interface{} = IsEqual(symbol, nil)
	var parseSide interface{} = IsEqual(side, nil)
	var shouldParseFees interface{} = IsTrue(parseFee) || IsTrue(parseFees)
	var fees interface{} = this.SafeList(order, "fees", []interface{}{})
	var trades interface{} = []interface{}{}
	var isTriggerOrSLTpOrder interface{} = (IsTrue((IsTrue(!IsEqual(this.SafeString(order, "triggerPrice"), nil)) || IsTrue((!IsEqual(this.SafeString(order, "stopLossPrice"), nil))))) || IsTrue((!IsEqual(this.SafeString(order, "takeProfitPrice"), nil))))
	if IsTrue(IsTrue(IsTrue(parseFilled) || IsTrue(parseCost)) || IsTrue(shouldParseFees)) {
		var rawTrades interface{} = this.SafeValue(order, "trades", trades)
		// const oldNumber = this.number;
		// we parse trades as strings here!
		// i don't think this is needed anymore
		// (this as any).number = String;
		var firstTrade interface{} = this.SafeValue(rawTrades, 0)
		// parse trades if they haven't already been parsed
		var tradesAreParsed interface{} = (IsTrue(IsTrue((!IsEqual(firstTrade, nil))) && IsTrue((InOp(firstTrade, "info")))) && IsTrue((InOp(firstTrade, "id"))))
		if !IsTrue(tradesAreParsed) {
			trades = this.ParseTrades(rawTrades, market)
		} else {
			trades = rawTrades
		}
		// this.number = oldNumber; why parse trades as strings if you read the value using `safeString` ?
		var tradesLength interface{} = 0
		var isArray interface{} = IsArray(trades)
		if IsTrue(isArray) {
			tradesLength = GetArrayLength(trades)
		}
		if IsTrue(IsTrue(isArray) && IsTrue((IsGreaterThan(tradesLength, 0)))) {
			// move properties that are defined in trades up into the order
			if IsTrue(IsEqual(GetValue(order, "symbol"), nil)) {
				AddElementToObject(order, "symbol", GetValue(GetValue(trades, 0), "symbol"))
			}
			if IsTrue(IsEqual(GetValue(order, "side"), nil)) {
				AddElementToObject(order, "side", GetValue(GetValue(trades, 0), "side"))
			}
			if IsTrue(IsEqual(GetValue(order, "type"), nil)) {
				AddElementToObject(order, "type", GetValue(GetValue(trades, 0), "type"))
			}
			if IsTrue(IsEqual(GetValue(order, "id"), nil)) {
				AddElementToObject(order, "id", GetValue(GetValue(trades, 0), "order"))
			}
			if IsTrue(parseFilled) {
				filled = "0"
			}
			if IsTrue(parseCost) {
				cost = "0"
			}
			for i := 0; IsLessThan(i, GetArrayLength(trades)); i++ {
				var trade interface{} = GetValue(trades, i)
				var tradeAmount interface{} = this.SafeString(trade, "amount")
				if IsTrue(IsTrue(parseFilled) && IsTrue((!IsEqual(tradeAmount, nil)))) {
					filled = Precise.StringAdd(filled, tradeAmount)
				}
				var tradeCost interface{} = this.SafeString(trade, "cost")
				if IsTrue(IsTrue(parseCost) && IsTrue((!IsEqual(tradeCost, nil)))) {
					cost = Precise.StringAdd(cost, tradeCost)
				}
				if IsTrue(parseSymbol) {
					symbol = this.SafeString(trade, "symbol")
				}
				if IsTrue(parseSide) {
					side = this.SafeString(trade, "side")
				}
				var tradeTimestamp interface{} = this.SafeValue(trade, "timestamp")
				if IsTrue(IsTrue(parseLastTradeTimeTimestamp) && IsTrue((!IsEqual(tradeTimestamp, nil)))) {
					if IsTrue(IsEqual(lastTradeTimeTimestamp, nil)) {
						lastTradeTimeTimestamp = tradeTimestamp
					} else {
						lastTradeTimeTimestamp = mathMax(lastTradeTimeTimestamp, tradeTimestamp)
					}
				}
				if IsTrue(shouldParseFees) {
					var tradeFees interface{} = this.SafeValue(trade, "fees")
					if IsTrue(!IsEqual(tradeFees, nil)) {
						for j := 0; IsLessThan(j, GetArrayLength(tradeFees)); j++ {
							var tradeFee interface{} = GetValue(tradeFees, j)
							AppendToArray(&fees, this.Extend(map[string]interface{}{}, tradeFee))
						}
					} else {
						var tradeFee interface{} = this.SafeValue(trade, "fee")
						if IsTrue(!IsEqual(tradeFee, nil)) {
							AppendToArray(&fees, this.Extend(map[string]interface{}{}, tradeFee))
						}
					}
				}
			}
		}
	}
	if IsTrue(shouldParseFees) {
		var reducedFees interface{} = Ternary(IsTrue(this.ReduceFees), this.ReduceFeesByCurrency(fees), fees)
		var reducedLength interface{} = GetArrayLength(reducedFees)
		for i := 0; IsLessThan(i, reducedLength); i++ {
			AddElementToObject(GetValue(reducedFees, i), "cost", this.SafeNumber(GetValue(reducedFees, i), "cost"))
			if IsTrue(InOp(GetValue(reducedFees, i), "rate")) {
				AddElementToObject(GetValue(reducedFees, i), "rate", this.SafeNumber(GetValue(reducedFees, i), "rate"))
			}
		}
		if IsTrue(!IsTrue(parseFee) && IsTrue((IsEqual(reducedLength, 0)))) {
			// copy fee to avoid modification by reference
			var feeCopy interface{} = this.DeepExtend(fee)
			AddElementToObject(feeCopy, "cost", this.SafeNumber(feeCopy, "cost"))
			if IsTrue(InOp(feeCopy, "rate")) {
				AddElementToObject(feeCopy, "rate", this.SafeNumber(feeCopy, "rate"))
			}
			AppendToArray(&reducedFees, feeCopy)
		}
		AddElementToObject(order, "fees", reducedFees)
		if IsTrue(IsTrue(parseFee) && IsTrue((IsEqual(reducedLength, 1)))) {
			AddElementToObject(order, "fee", GetValue(reducedFees, 0))
		}
	}
	if IsTrue(IsEqual(amount, nil)) {
		// ensure amount = filled + remaining
		if IsTrue(IsTrue(!IsEqual(filled, nil)) && IsTrue(!IsEqual(remaining, nil))) {
			amount = Precise.StringAdd(filled, remaining)
		} else if IsTrue(IsEqual(status, "closed")) {
			amount = filled
		}
	}
	if IsTrue(IsEqual(filled, nil)) {
		if IsTrue(IsTrue(!IsEqual(amount, nil)) && IsTrue(!IsEqual(remaining, nil))) {
			filled = Precise.StringSub(amount, remaining)
		} else if IsTrue(IsTrue(IsEqual(status, "closed")) && IsTrue(!IsEqual(amount, nil))) {
			filled = amount
		}
	}
	if IsTrue(IsEqual(remaining, nil)) {
		if IsTrue(IsTrue(!IsEqual(amount, nil)) && IsTrue(!IsEqual(filled, nil))) {
			remaining = Precise.StringSub(amount, filled)
		} else if IsTrue(IsEqual(status, "closed")) {
			remaining = "0"
		}
	}
	// ensure that the average field is calculated correctly
	var inverse interface{} = this.SafeBool(market, "inverse", false)
	var contractSize interface{} = this.NumberToString(this.SafeValue(market, "contractSize", 1))
	// inverse
	// price = filled * contract size / cost
	//
	// linear
	// price = cost / (filled * contract size)
	if IsTrue(IsEqual(average, nil)) {
		if IsTrue(IsTrue(IsTrue((!IsEqual(filled, nil))) && IsTrue((!IsEqual(cost, nil)))) && IsTrue(Precise.StringGt(filled, "0"))) {
			var filledTimesContractSize interface{} = Precise.StringMul(filled, contractSize)
			if IsTrue(inverse) {
				average = Precise.StringDiv(filledTimesContractSize, cost)
			} else {
				average = Precise.StringDiv(cost, filledTimesContractSize)
			}
		}
	}
	// similarly
	// inverse
	// cost = filled * contract size / price
	//
	// linear
	// cost = filled * contract size * price
	var costPriceExists interface{} = IsTrue((!IsEqual(average, nil))) || IsTrue((!IsEqual(price, nil)))
	if IsTrue(IsTrue(IsTrue(parseCost) && IsTrue((!IsEqual(filled, nil)))) && IsTrue(costPriceExists)) {
		var multiplyPrice interface{} = nil
		if IsTrue(IsEqual(average, nil)) {
			multiplyPrice = price
		} else {
			multiplyPrice = average
		}
		// contract trading
		var filledTimesContractSize interface{} = Precise.StringMul(filled, contractSize)
		if IsTrue(inverse) {
			cost = Precise.StringDiv(filledTimesContractSize, multiplyPrice)
		} else {
			cost = Precise.StringMul(filledTimesContractSize, multiplyPrice)
		}
	}
	// support for market orders
	var orderType interface{} = this.SafeValue(order, "type")
	var emptyPrice interface{} = IsTrue((IsEqual(price, nil))) || IsTrue(Precise.StringEquals(price, "0"))
	if IsTrue(IsTrue(emptyPrice) && IsTrue((IsEqual(orderType, "market")))) {
		price = average
	}
	// we have trades with string values at this point so we will mutate them
	for i := 0; IsLessThan(i, GetArrayLength(trades)); i++ {
		var entry interface{} = GetValue(trades, i)
		AddElementToObject(entry, "amount", this.SafeNumber(entry, "amount"))
		AddElementToObject(entry, "price", this.SafeNumber(entry, "price"))
		AddElementToObject(entry, "cost", this.SafeNumber(entry, "cost"))
		var tradeFee interface{} = this.SafeDict(entry, "fee", map[string]interface{}{})
		AddElementToObject(tradeFee, "cost", this.SafeNumber(tradeFee, "cost"))
		if IsTrue(InOp(tradeFee, "rate")) {
			AddElementToObject(tradeFee, "rate", this.SafeNumber(tradeFee, "rate"))
		}
		var entryFees interface{} = this.SafeList(entry, "fees", []interface{}{})
		for j := 0; IsLessThan(j, GetArrayLength(entryFees)); j++ {
			AddElementToObject(GetValue(entryFees, j), "cost", this.SafeNumber(GetValue(entryFees, j), "cost"))
		}
		AddElementToObject(entry, "fees", entryFees)
		AddElementToObject(entry, "fee", tradeFee)
	}
	var timeInForce interface{} = this.SafeString(order, "timeInForce")
	var postOnly interface{} = this.SafeValue(order, "postOnly")
	// timeInForceHandling
	if IsTrue(IsEqual(timeInForce, nil)) {
		if IsTrue(!IsTrue(isTriggerOrSLTpOrder) && IsTrue((IsEqual(this.SafeString(order, "type"), "market")))) {
			timeInForce = "IOC"
		}
		// allow postOnly override
		if IsTrue(postOnly) {
			timeInForce = "PO"
		}
	} else if IsTrue(IsEqual(postOnly, nil)) {
		// timeInForce is not undefined here
		postOnly = IsEqual(timeInForce, "PO")
	}
	var timestamp interface{} = this.SafeInteger(order, "timestamp")
	var lastUpdateTimestamp interface{} = this.SafeInteger(order, "lastUpdateTimestamp")
	var datetime interface{} = this.SafeString(order, "datetime")
	if IsTrue(IsEqual(datetime, nil)) {
		datetime = this.Iso8601(timestamp)
	}
	var triggerPrice interface{} = this.ParseNumber(this.SafeString2(order, "triggerPrice", "stopPrice"))
	var takeProfitPrice interface{} = this.ParseNumber(this.SafeString(order, "takeProfitPrice"))
	var stopLossPrice interface{} = this.ParseNumber(this.SafeString(order, "stopLossPrice"))
	return this.Extend(order, map[string]interface{}{
		"id":                  this.SafeString(order, "id"),
		"clientOrderId":       this.SafeString(order, "clientOrderId"),
		"timestamp":           timestamp,
		"datetime":            datetime,
		"symbol":              symbol,
		"type":                this.SafeString(order, "type"),
		"side":                side,
		"lastTradeTimestamp":  lastTradeTimeTimestamp,
		"lastUpdateTimestamp": lastUpdateTimestamp,
		"price":               this.ParseNumber(price),
		"amount":              this.ParseNumber(amount),
		"cost":                this.ParseNumber(cost),
		"average":             this.ParseNumber(average),
		"filled":              this.ParseNumber(filled),
		"remaining":           this.ParseNumber(remaining),
		"timeInForce":         timeInForce,
		"postOnly":            postOnly,
		"trades":              trades,
		"reduceOnly":          this.SafeValue(order, "reduceOnly"),
		"stopPrice":           triggerPrice,
		"triggerPrice":        triggerPrice,
		"takeProfitPrice":     takeProfitPrice,
		"stopLossPrice":       stopLossPrice,
		"status":              status,
		"fee":                 this.SafeValue(order, "fee"),
	})
}
func (this *Exchange) ParseOrders(orders interface{}, optionalArgs ...interface{}) interface{} {
	//
	// the value of orders is either a dict or a list
	//
	// dict
	//
	//     {
	//         'id1': { ... },
	//         'id2': { ... },
	//         'id3': { ... },
	//         ...
	//     }
	//
	// list
	//
	//     [
	//         { 'id': 'id1', ... },
	//         { 'id': 'id2', ... },
	//         { 'id': 'id3', ... },
	//         ...
	//     ]
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	params := GetArg(optionalArgs, 3, map[string]interface{}{})
	_ = params
	var results interface{} = []interface{}{}
	if IsTrue(IsArray(orders)) {
		for i := 0; IsLessThan(i, GetArrayLength(orders)); i++ {

			var parsed interface{} = this.DerivedExchange.ParseOrder(GetValue(orders, i), market)
			PanicOnError(parsed) // don't inline this call
			var order interface{} = this.Extend(parsed, params)
			AppendToArray(&results, order)
		}
	} else {
		var ids interface{} = ObjectKeys(orders)
		for i := 0; IsLessThan(i, GetArrayLength(ids)); i++ {
			var id interface{} = GetValue(ids, i)
			var idExtended interface{} = this.Extend(map[string]interface{}{
				"id": id,
			}, GetValue(orders, id))

			var parsedOrder interface{} = this.DerivedExchange.ParseOrder(idExtended, market)
			PanicOnError(parsedOrder) // don't  inline these calls
			var order interface{} = this.Extend(parsedOrder, params)
			AppendToArray(&results, order)
		}
	}
	results = this.SortBy(results, "timestamp")
	var symbol interface{} = Ternary(IsTrue((!IsEqual(market, nil))), GetValue(market, "symbol"), nil)
	return this.FilterBySymbolSinceLimit(results, symbol, since, limit)
}
func (this *Exchange) CalculateFeeWithRate(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, price interface{}, optionalArgs ...interface{}) interface{} {
	takerOrMaker := GetArg(optionalArgs, 0, "taker")
	_ = takerOrMaker
	feeRate := GetArg(optionalArgs, 1, nil)
	_ = feeRate
	params := GetArg(optionalArgs, 2, map[string]interface{}{})
	_ = params
	if IsTrue(IsTrue(IsEqual(typeVar, "market")) && IsTrue(IsEqual(takerOrMaker, "maker"))) {
		panic(ArgumentsRequired(Add(this.Id, " calculateFee() - you have provided incompatible arguments - \"market\" type order can not be \"maker\". Change either the \"type\" or the \"takerOrMaker\" argument to calculate the fee.")))
	}
	var market interface{} = GetValue(this.Markets, symbol)
	var feeSide interface{} = this.SafeString(market, "feeSide", "quote")
	var useQuote interface{} = nil
	if IsTrue(IsEqual(feeSide, "get")) {
		// the fee is always in the currency you get
		useQuote = IsEqual(side, "sell")
	} else if IsTrue(IsEqual(feeSide, "give")) {
		// the fee is always in the currency you give
		useQuote = IsEqual(side, "buy")
	} else {
		// the fee is always in feeSide currency
		useQuote = IsEqual(feeSide, "quote")
	}
	var cost interface{} = this.NumberToString(amount)
	var key interface{} = nil
	if IsTrue(useQuote) {
		var priceString interface{} = this.NumberToString(price)
		cost = Precise.StringMul(cost, priceString)
		key = "quote"
	} else {
		key = "base"
	}
	// for derivatives, the fee is in 'settle' currency
	if !IsTrue(GetValue(market, "spot")) {
		key = "settle"
	}
	// even if `takerOrMaker` argument was set to 'maker', for 'market' orders we should forcefully override it to 'taker'
	if IsTrue(IsEqual(typeVar, "market")) {
		takerOrMaker = "taker"
	}
	var rate interface{} = Ternary(IsTrue((!IsEqual(feeRate, nil))), this.NumberToString(feeRate), this.SafeString(market, takerOrMaker))
	cost = Precise.StringMul(cost, rate)
	return map[string]interface{}{
		"type":     takerOrMaker,
		"currency": GetValue(market, key),
		"rate":     this.ParseNumber(rate),
		"cost":     this.ParseNumber(cost),
	}
}
func (this *Exchange) CalculateFee(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, price interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @method
	 * @description calculates the presumptive fee that would be charged for an order
	 * @param {string} symbol unified market symbol
	 * @param {string} type 'market' or 'limit'
	 * @param {string} side 'buy' or 'sell'
	 * @param {float} amount how much you want to trade, in units of the base currency on most exchanges, or number of contracts
	 * @param {float} price the price for the order to be filled at, in units of the quote currency
	 * @param {string} takerOrMaker 'taker' or 'maker'
	 * @param {object} params
	 * @returns {object} contains the rate, the percentage multiplied to the order amount to obtain the fee amount, and cost, the total value of the fee in units of the quote currency, for the order
	 */
	takerOrMaker := GetArg(optionalArgs, 0, "taker")
	_ = takerOrMaker
	params := GetArg(optionalArgs, 1, map[string]interface{}{})
	_ = params
	return this.CalculateFeeWithRate(symbol, typeVar, side, amount, price, takerOrMaker, nil, params)
}
func (this *Exchange) SafeLiquidation(liquidation interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var contracts interface{} = this.SafeString(liquidation, "contracts")
	var contractSize interface{} = this.SafeString(market, "contractSize")
	var price interface{} = this.SafeString(liquidation, "price")
	var baseValue interface{} = this.SafeString(liquidation, "baseValue")
	var quoteValue interface{} = this.SafeString(liquidation, "quoteValue")
	if IsTrue(IsTrue(IsTrue(IsTrue((IsEqual(baseValue, nil))) && IsTrue((!IsEqual(contracts, nil)))) && IsTrue((!IsEqual(contractSize, nil)))) && IsTrue((!IsEqual(price, nil)))) {
		baseValue = Precise.StringMul(contracts, contractSize)
	}
	if IsTrue(IsTrue(IsTrue((IsEqual(quoteValue, nil))) && IsTrue((!IsEqual(baseValue, nil)))) && IsTrue((!IsEqual(price, nil)))) {
		quoteValue = Precise.StringMul(baseValue, price)
	}
	AddElementToObject(liquidation, "contracts", this.ParseNumber(contracts))
	AddElementToObject(liquidation, "contractSize", this.ParseNumber(contractSize))
	AddElementToObject(liquidation, "price", this.ParseNumber(price))
	AddElementToObject(liquidation, "baseValue", this.ParseNumber(baseValue))
	AddElementToObject(liquidation, "quoteValue", this.ParseNumber(quoteValue))
	return liquidation
}
func (this *Exchange) SafeTrade(trade interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var amount interface{} = this.SafeString(trade, "amount")
	var price interface{} = this.SafeString(trade, "price")
	var cost interface{} = this.SafeString(trade, "cost")
	if IsTrue(IsEqual(cost, nil)) {
		// contract trading
		var contractSize interface{} = this.SafeString(market, "contractSize")
		var multiplyPrice interface{} = price
		if IsTrue(!IsEqual(contractSize, nil)) {
			var inverse interface{} = this.SafeBool(market, "inverse", false)
			if IsTrue(inverse) {
				multiplyPrice = Precise.StringDiv("1", price)
			}
			multiplyPrice = Precise.StringMul(multiplyPrice, contractSize)
		}
		cost = Precise.StringMul(multiplyPrice, amount)
	}
	resultFeeresultFeesVariable := this.ParsedFeeAndFees(trade)
	resultFee := GetValue(resultFeeresultFeesVariable, 0)
	resultFees := GetValue(resultFeeresultFeesVariable, 1)
	AddElementToObject(trade, "fee", resultFee)
	AddElementToObject(trade, "fees", resultFees)
	AddElementToObject(trade, "amount", this.ParseNumber(amount))
	AddElementToObject(trade, "price", this.ParseNumber(price))
	AddElementToObject(trade, "cost", this.ParseNumber(cost))
	return trade
}
func (this *Exchange) CreateCcxtTradeId(optionalArgs ...interface{}) interface{} {
	// this approach is being used by multiple exchanges (mexc, woo, coinsbit, dydx, ...)
	timestamp := GetArg(optionalArgs, 0, nil)
	_ = timestamp
	side := GetArg(optionalArgs, 1, nil)
	_ = side
	amount := GetArg(optionalArgs, 2, nil)
	_ = amount
	price := GetArg(optionalArgs, 3, nil)
	_ = price
	takerOrMaker := GetArg(optionalArgs, 4, nil)
	_ = takerOrMaker
	var id interface{} = nil
	if IsTrue(!IsEqual(timestamp, nil)) {
		id = this.NumberToString(timestamp)
		if IsTrue(!IsEqual(side, nil)) {
			id = Add(id, Add("-", side))
		}
		if IsTrue(!IsEqual(amount, nil)) {
			id = Add(id, Add("-", this.NumberToString(amount)))
		}
		if IsTrue(!IsEqual(price, nil)) {
			id = Add(id, Add("-", this.NumberToString(price)))
		}
		if IsTrue(!IsEqual(takerOrMaker, nil)) {
			id = Add(id, Add("-", takerOrMaker))
		}
	}
	return id
}
func (this *Exchange) ParsedFeeAndFees(container interface{}) interface{} {
	var fee interface{} = this.SafeDict(container, "fee")
	var fees interface{} = this.SafeList(container, "fees")
	var feeDefined interface{} = !IsEqual(fee, nil)
	var feesDefined interface{} = !IsEqual(fees, nil)
	// parsing only if at least one of them is defined
	var shouldParseFees interface{} = (IsTrue(feeDefined) || IsTrue(feesDefined))
	if IsTrue(shouldParseFees) {
		if IsTrue(feeDefined) {
			fee = this.ParseFeeNumeric(fee)
		}
		if !IsTrue(feesDefined) {
			// just set it directly, no further processing needed
			fees = []interface{}{fee}
		}
		// 'fees' were set, so reparse them
		var reducedFees interface{} = Ternary(IsTrue(this.ReduceFees), this.ReduceFeesByCurrency(fees), fees)
		var reducedLength interface{} = GetArrayLength(reducedFees)
		for i := 0; IsLessThan(i, reducedLength); i++ {
			AddElementToObject(reducedFees, i, this.ParseFeeNumeric(GetValue(reducedFees, i)))
		}
		fees = reducedFees
		if IsTrue(IsEqual(reducedLength, 1)) {
			fee = GetValue(reducedFees, 0)
		} else if IsTrue(IsEqual(reducedLength, 0)) {
			fee = nil
		}
	}
	// in case `fee & fees` are undefined, set `fees` as empty array
	if IsTrue(IsEqual(fee, nil)) {
		fee = map[string]interface{}{
			"cost":     nil,
			"currency": nil,
		}
	}
	if IsTrue(IsEqual(fees, nil)) {
		fees = []interface{}{}
	}
	return []interface{}{fee, fees}
}
func (this *Exchange) ParseFeeNumeric(fee interface{}) interface{} {
	AddElementToObject(fee, "cost", this.SafeNumber(fee, "cost")) // ensure numeric
	if IsTrue(InOp(fee, "rate")) {
		AddElementToObject(fee, "rate", this.SafeNumber(fee, "rate"))
	}
	return fee
}
func (this *Exchange) FindNearestCeiling(arr interface{}, providedValue interface{}) interface{} {
	//  i.e. findNearestCeiling ([ 10, 30, 50],  23) returns 30
	var length interface{} = GetArrayLength(arr)
	for i := 0; IsLessThan(i, length); i++ {
		var current interface{} = GetValue(arr, i)
		if IsTrue(IsLessThanOrEqual(providedValue, current)) {
			return current
		}
	}
	return GetValue(arr, Subtract(length, 1))
}
func (this *Exchange) InvertFlatStringDictionary(dict interface{}) interface{} {
	var reversed interface{} = map[string]interface{}{}
	var keys interface{} = ObjectKeys(dict)
	for i := 0; IsLessThan(i, GetArrayLength(keys)); i++ {
		var key interface{} = GetValue(keys, i)
		var value interface{} = GetValue(dict, key)
		if IsTrue(IsString(value)) {
			AddElementToObject(reversed, value, key)
		}
	}
	return reversed
}
func (this *Exchange) ReduceFeesByCurrency(fees interface{}) interface{} {
	//
	// this function takes a list of fee structures having the following format
	//
	//     string = true
	//
	//     [
	//         { 'currency': 'BTC', 'cost': '0.1' },
	//         { 'currency': 'BTC', 'cost': '0.2'  },
	//         { 'currency': 'BTC', 'cost': '0.2', 'rate': '0.00123' },
	//         { 'currency': 'BTC', 'cost': '0.4', 'rate': '0.00123' },
	//         { 'currency': 'BTC', 'cost': '0.5', 'rate': '0.00456' },
	//         { 'currency': 'USDT', 'cost': '12.3456' },
	//     ]
	//
	//     string = false
	//
	//     [
	//         { 'currency': 'BTC', 'cost': 0.1 },
	//         { 'currency': 'BTC', 'cost': 0.2 },
	//         { 'currency': 'BTC', 'cost': 0.2, 'rate': 0.00123 },
	//         { 'currency': 'BTC', 'cost': 0.4, 'rate': 0.00123 },
	//         { 'currency': 'BTC', 'cost': 0.5, 'rate': 0.00456 },
	//         { 'currency': 'USDT', 'cost': 12.3456 },
	//     ]
	//
	// and returns a reduced fee list, where fees are summed per currency and rate (if any)
	//
	//     string = true
	//
	//     [
	//         { 'currency': 'BTC', 'cost': '0.4'  },
	//         { 'currency': 'BTC', 'cost': '0.6', 'rate': '0.00123' },
	//         { 'currency': 'BTC', 'cost': '0.5', 'rate': '0.00456' },
	//         { 'currency': 'USDT', 'cost': '12.3456' },
	//     ]
	//
	//     string  = false
	//
	//     [
	//         { 'currency': 'BTC', 'cost': 0.3  },
	//         { 'currency': 'BTC', 'cost': 0.6, 'rate': 0.00123 },
	//         { 'currency': 'BTC', 'cost': 0.5, 'rate': 0.00456 },
	//         { 'currency': 'USDT', 'cost': 12.3456 },
	//     ]
	//
	var reduced interface{} = map[string]interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(fees)); i++ {
		var fee interface{} = GetValue(fees, i)
		var code interface{} = this.SafeString(fee, "currency")
		var feeCurrencyCode interface{} = Ternary(IsTrue((!IsEqual(code, nil))), code, ToString(i))
		if IsTrue(!IsEqual(feeCurrencyCode, nil)) {
			var rate interface{} = this.SafeString(fee, "rate")
			var cost interface{} = this.SafeString(fee, "cost")
			if IsTrue(IsEqual(cost, nil)) {
				continue
			}
			if !IsTrue((InOp(reduced, feeCurrencyCode))) {
				AddElementToObject(reduced, feeCurrencyCode, map[string]interface{}{})
			}
			var rateKey interface{} = Ternary(IsTrue((IsEqual(rate, nil))), "", rate)
			if IsTrue(InOp(GetValue(reduced, feeCurrencyCode), rateKey)) {
				AddElementToObject(GetValue(GetValue(reduced, feeCurrencyCode), rateKey), "cost", Precise.StringAdd(GetValue(GetValue(GetValue(reduced, feeCurrencyCode), rateKey), "cost"), cost))
			} else {
				AddElementToObject(GetValue(reduced, feeCurrencyCode), rateKey, map[string]interface{}{
					"currency": code,
					"cost":     cost,
				})
				if IsTrue(!IsEqual(rate, nil)) {
					AddElementToObject(GetValue(GetValue(reduced, feeCurrencyCode), rateKey), "rate", rate)
				}
			}
		}
	}
	var result interface{} = []interface{}{}
	var feeValues interface{} = ObjectValues(reduced)
	for i := 0; IsLessThan(i, GetArrayLength(feeValues)); i++ {
		var reducedFeeValues interface{} = ObjectValues(GetValue(feeValues, i))
		result = this.ArrayConcat(result, reducedFeeValues)
	}
	return result
}
func (this *Exchange) SafeTicker(ticker interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var open interface{} = this.OmitZero(this.SafeString(ticker, "open"))
	var close interface{} = this.OmitZero(this.SafeString2(ticker, "close", "last"))
	var change interface{} = this.OmitZero(this.SafeString(ticker, "change"))
	var percentage interface{} = this.OmitZero(this.SafeString(ticker, "percentage"))
	var average interface{} = this.OmitZero(this.SafeString(ticker, "average"))
	var vwap interface{} = this.SafeString(ticker, "vwap")
	var baseVolume interface{} = this.SafeString(ticker, "baseVolume")
	var quoteVolume interface{} = this.SafeString(ticker, "quoteVolume")
	if IsTrue(IsEqual(vwap, nil)) {
		vwap = Precise.StringDiv(this.OmitZero(quoteVolume), baseVolume)
	}
	// calculate open
	if IsTrue(!IsEqual(change, nil)) {
		if IsTrue(IsTrue(IsEqual(close, nil)) && IsTrue(!IsEqual(average, nil))) {
			close = Precise.StringAdd(average, Precise.StringDiv(change, "2"))
		}
		if IsTrue(IsTrue(IsEqual(open, nil)) && IsTrue(!IsEqual(close, nil))) {
			open = Precise.StringSub(close, change)
		}
	} else if IsTrue(!IsEqual(percentage, nil)) {
		if IsTrue(IsTrue(IsEqual(close, nil)) && IsTrue(!IsEqual(average, nil))) {
			var openAddClose interface{} = Precise.StringMul(average, "2")
			// openAddClose = open * (1 + (100 + percentage)/100)
			var denominator interface{} = Precise.StringAdd("2", Precise.StringDiv(percentage, "100"))
			var calcOpen interface{} = Ternary(IsTrue((!IsEqual(open, nil))), open, Precise.StringDiv(openAddClose, denominator))
			close = Precise.StringMul(calcOpen, Precise.StringAdd("1", Precise.StringDiv(percentage, "100")))
		}
		if IsTrue(IsTrue(IsEqual(open, nil)) && IsTrue(!IsEqual(close, nil))) {
			open = Precise.StringDiv(close, Precise.StringAdd("1", Precise.StringDiv(percentage, "100")))
		}
	}
	// change
	if IsTrue(IsEqual(change, nil)) {
		if IsTrue(IsTrue(!IsEqual(close, nil)) && IsTrue(!IsEqual(open, nil))) {
			change = Precise.StringSub(close, open)
		} else if IsTrue(IsTrue(!IsEqual(close, nil)) && IsTrue(!IsEqual(percentage, nil))) {
			change = Precise.StringMul(Precise.StringDiv(percentage, "100"), Precise.StringDiv(close, "100"))
		} else if IsTrue(IsTrue(!IsEqual(open, nil)) && IsTrue(!IsEqual(percentage, nil))) {
			change = Precise.StringMul(open, Precise.StringDiv(percentage, "100"))
		}
	}
	// calculate things according to "open" (similar can be done with "close")
	if IsTrue(!IsEqual(open, nil)) {
		// percentage (using change)
		if IsTrue(IsTrue(IsEqual(percentage, nil)) && IsTrue(!IsEqual(change, nil))) {
			percentage = Precise.StringMul(Precise.StringDiv(change, open), "100")
		}
		// close (using change)
		if IsTrue(IsTrue(IsEqual(close, nil)) && IsTrue(!IsEqual(change, nil))) {
			close = Precise.StringAdd(open, change)
		}
		// close (using average)
		if IsTrue(IsTrue(IsEqual(close, nil)) && IsTrue(!IsEqual(average, nil))) {
			close = Precise.StringMul(average, "2")
		}
		// average
		if IsTrue(IsTrue(IsEqual(average, nil)) && IsTrue(!IsEqual(close, nil))) {
			var precision interface{} = 18
			if IsTrue(IsTrue(!IsEqual(market, nil)) && IsTrue(this.IsTickPrecision())) {
				var marketPrecision interface{} = this.SafeDict(market, "precision")
				var precisionPrice interface{} = this.SafeString(marketPrecision, "price")
				if IsTrue(!IsEqual(precisionPrice, nil)) {
					precision = this.PrecisionFromString(precisionPrice)
				}
			}
			average = Precise.StringDiv(Precise.StringAdd(open, close), "2", precision)
		}
	}
	// timestamp and symbol operations don't belong in safeTicker
	// they should be done in the derived classes
	var closeParsed interface{} = this.ParseNumber(this.OmitZero(close))
	return this.Extend(ticker, map[string]interface{}{
		"bid":           this.ParseNumber(this.OmitZero(this.SafeString(ticker, "bid"))),
		"bidVolume":     this.SafeNumber(ticker, "bidVolume"),
		"ask":           this.ParseNumber(this.OmitZero(this.SafeString(ticker, "ask"))),
		"askVolume":     this.SafeNumber(ticker, "askVolume"),
		"high":          this.ParseNumber(this.OmitZero(this.SafeString(ticker, "high"))),
		"low":           this.ParseNumber(this.OmitZero(this.SafeString(ticker, "low"))),
		"open":          this.ParseNumber(this.OmitZero(open)),
		"close":         closeParsed,
		"last":          closeParsed,
		"change":        this.ParseNumber(change),
		"percentage":    this.ParseNumber(percentage),
		"average":       this.ParseNumber(average),
		"vwap":          this.ParseNumber(vwap),
		"baseVolume":    this.ParseNumber(baseVolume),
		"quoteVolume":   this.ParseNumber(quoteVolume),
		"previousClose": this.SafeNumber(ticker, "previousClose"),
		"indexPrice":    this.SafeNumber(ticker, "indexPrice"),
		"markPrice":     this.SafeNumber(ticker, "markPrice"),
	})
}
func (this *Exchange) FetchBorrowRate(code interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchBorrowRate is deprecated, please use fetchCrossBorrowRate or fetchIsolatedBorrowRate instead")))

	}()
	return ch
}
func (this *Exchange) RepayCrossMargin(code interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " repayCrossMargin is not support yet")))

	}()
	return ch
}
func (this *Exchange) RepayIsolatedMargin(symbol interface{}, code interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " repayIsolatedMargin is not support yet")))

	}()
	return ch
}
func (this *Exchange) BorrowCrossMargin(code interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " borrowCrossMargin is not support yet")))

	}()
	return ch
}
func (this *Exchange) BorrowIsolatedMargin(symbol interface{}, code interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " borrowIsolatedMargin is not support yet")))

	}()
	return ch
}
func (this *Exchange) BorrowMargin(code interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " borrowMargin is deprecated, please use borrowCrossMargin or borrowIsolatedMargin instead")))

	}()
	return ch
}
func (this *Exchange) RepayMargin(code interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " repayMargin is deprecated, please use repayCrossMargin or repayIsolatedMargin instead")))

	}()
	return ch
}
func (this *Exchange) FetchOHLCV(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		timeframe := GetArg(optionalArgs, 0, "1m")
		_ = timeframe
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		var message interface{} = ""
		if IsTrue(GetValue(this.Has, "fetchTrades")) {
			message = ". If you want to build OHLCV candles from trade executions data, visit https://github.com/ccxt/ccxt/tree/master/examples/ and see \"build-ohlcv-bars\" file"
		}
		panic(NotSupported(Add(Add(this.Id, " fetchOHLCV() is not supported yet"), message)))

	}()
	return ch
}
func (this *Exchange) FetchOHLCVWs(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		timeframe := GetArg(optionalArgs, 0, "1m")
		_ = timeframe
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		var message interface{} = ""
		if IsTrue(GetValue(this.Has, "fetchTradesWs")) {
			message = ". If you want to build OHLCV candles from trade executions data, visit https://github.com/ccxt/ccxt/tree/master/examples/ and see \"build-ohlcv-bars\" file"
		}
		panic(NotSupported(Add(Add(this.Id, " fetchOHLCVWs() is not supported yet. Try using fetchOHLCV instead."), message)))

	}()
	return ch
}
func (this *Exchange) WatchOHLCV(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		timeframe := GetArg(optionalArgs, 0, "1m")
		_ = timeframe
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchOHLCV() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) ConvertTradingViewToOHLCV(ohlcvs interface{}, optionalArgs ...interface{}) interface{} {
	timestamp := GetArg(optionalArgs, 0, "t")
	_ = timestamp
	open := GetArg(optionalArgs, 1, "o")
	_ = open
	high := GetArg(optionalArgs, 2, "h")
	_ = high
	low := GetArg(optionalArgs, 3, "l")
	_ = low
	close := GetArg(optionalArgs, 4, "c")
	_ = close
	volume := GetArg(optionalArgs, 5, "v")
	_ = volume
	ms := GetArg(optionalArgs, 6, false)
	_ = ms
	var result interface{} = []interface{}{}
	var timestamps interface{} = this.SafeList(ohlcvs, timestamp, []interface{}{})
	var opens interface{} = this.SafeList(ohlcvs, open, []interface{}{})
	var highs interface{} = this.SafeList(ohlcvs, high, []interface{}{})
	var lows interface{} = this.SafeList(ohlcvs, low, []interface{}{})
	var closes interface{} = this.SafeList(ohlcvs, close, []interface{}{})
	var volumes interface{} = this.SafeList(ohlcvs, volume, []interface{}{})
	for i := 0; IsLessThan(i, GetArrayLength(timestamps)); i++ {
		AppendToArray(&result, []interface{}{Ternary(IsTrue(ms), this.SafeInteger(timestamps, i), this.SafeTimestamp(timestamps, i)), this.SafeValue(opens, i), this.SafeValue(highs, i), this.SafeValue(lows, i), this.SafeValue(closes, i), this.SafeValue(volumes, i)})
	}
	return result
}
func (this *Exchange) ConvertOHLCVToTradingView(ohlcvs interface{}, optionalArgs ...interface{}) interface{} {
	timestamp := GetArg(optionalArgs, 0, "t")
	_ = timestamp
	open := GetArg(optionalArgs, 1, "o")
	_ = open
	high := GetArg(optionalArgs, 2, "h")
	_ = high
	low := GetArg(optionalArgs, 3, "l")
	_ = low
	close := GetArg(optionalArgs, 4, "c")
	_ = close
	volume := GetArg(optionalArgs, 5, "v")
	_ = volume
	ms := GetArg(optionalArgs, 6, false)
	_ = ms
	var result interface{} = map[string]interface{}{}
	AddElementToObject(result, timestamp, []interface{}{})
	AddElementToObject(result, open, []interface{}{})
	AddElementToObject(result, high, []interface{}{})
	AddElementToObject(result, low, []interface{}{})
	AddElementToObject(result, close, []interface{}{})
	AddElementToObject(result, volume, []interface{}{})
	for i := 0; IsLessThan(i, GetArrayLength(ohlcvs)); i++ {
		var ts interface{} = Ternary(IsTrue(ms), GetValue(GetValue(ohlcvs, i), 0), this.ParseToInt(Divide(GetValue(GetValue(ohlcvs, i), 0), 1000)))
		var resultTimestamp interface{} = GetValue(result, timestamp)
		AppendToArray(&resultTimestamp, ts)
		var resultOpen interface{} = GetValue(result, open)
		AppendToArray(&resultOpen, GetValue(GetValue(ohlcvs, i), 1))
		var resultHigh interface{} = GetValue(result, high)
		AppendToArray(&resultHigh, GetValue(GetValue(ohlcvs, i), 2))
		var resultLow interface{} = GetValue(result, low)
		AppendToArray(&resultLow, GetValue(GetValue(ohlcvs, i), 3))
		var resultClose interface{} = GetValue(result, close)
		AppendToArray(&resultClose, GetValue(GetValue(ohlcvs, i), 4))
		var resultVolume interface{} = GetValue(result, volume)
		AppendToArray(&resultVolume, GetValue(GetValue(ohlcvs, i), 5))
	}
	return result
}
func (this *Exchange) FetchWebEndpoint(method interface{}, endpointMethod interface{}, returnAsJson interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		startRegex := GetArg(optionalArgs, 0, nil)
		_ = startRegex
		endRegex := GetArg(optionalArgs, 1, nil)
		_ = endRegex
		var errorMessage interface{} = ""
		var options interface{} = this.SafeValue(this.Options, method, map[string]interface{}{})
		var muteOnFailure interface{} = this.SafeBool(options, "webApiMuteFailure", true)

		{
			func(this *Exchange) (ret_ interface{}) {
				defer func() {
					if e := recover(); e != nil {
						if e == "break" {
							return
						}
						ret_ = func(this *Exchange) interface{} {
							// catch block:
							errorMessage = Add(Add(Add(this.Id, " "), method), "() failed to fetch correct data from website. Probably webpage markup has been changed, breaking the page custom parser.")
							return nil
						}(this)
					}
				}()
				// try block:
				// if it was not explicitly disabled, then don't fetch
				if IsTrue(!IsEqual(this.SafeBool(options, "webApiEnable", true), true)) {

					return nil
				}
				var maxRetries interface{} = this.SafeValue(options, "webApiRetries", 10)
				var response interface{} = nil
				var retry interface{} = 0
				var shouldBreak interface{} = false
				for IsLessThan(retry, maxRetries) {

					{
						func(this *Exchange) (ret_ interface{}) {
							defer func() {
								if e := recover(); e != nil {
									if e == "break" {
										return
									}
									ret_ = func(this *Exchange) interface{} {
										// catch block:
										retry = Add(retry, 1)
										if IsTrue(IsEqual(retry, maxRetries)) {
											panic(e)
										}
										return nil
									}(this)
								}
							}()
							// try block:

							response = (<-this.callDynamically(endpointMethod, map[string]interface{}{}))
							PanicOnError(response)
							shouldBreak = true
							panic("break")

						}(this)

					}
					if IsTrue(shouldBreak) {
						break // this is needed because of GO
					}
				}
				var content interface{} = response
				if IsTrue(!IsEqual(startRegex, nil)) {
					var splitted_by_start interface{} = Split(content, startRegex)
					content = GetValue(splitted_by_start, 1) // we need second part after start
				}
				if IsTrue(!IsEqual(endRegex, nil)) {
					var splitted_by_end interface{} = Split(content, endRegex)
					content = GetValue(splitted_by_end, 0) // we need first part after start
				}
				if IsTrue(IsTrue(returnAsJson) && IsTrue((IsString(content)))) {
					var jsoned interface{} = this.ParseJson(Trim(content)) // content should be trimmed before json parsing
					if IsTrue(jsoned) {

						ch <- jsoned // if parsing was not successfull, exception should be thrown
						return nil
					} else {
						panic(BadResponse("could not parse the response into json"))
					}
				} else {

					ch <- content
					return nil
				}

			}(this)

		}
		if IsTrue(muteOnFailure) {

			return nil
		} else {
			panic(BadResponse(errorMessage))
		}

	}()
	return ch
}
func (this *Exchange) MarketIds(optionalArgs ...interface{}) interface{} {
	symbols := GetArg(optionalArgs, 0, nil)
	_ = symbols
	if IsTrue(IsEqual(symbols, nil)) {
		return symbols
	}
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(symbols)); i++ {
		AppendToArray(&result, this.MarketId(GetValue(symbols, i)))
	}
	return result
}
func (this *Exchange) CurrencyIds(optionalArgs ...interface{}) interface{} {
	codes := GetArg(optionalArgs, 0, nil)
	_ = codes
	if IsTrue(IsEqual(codes, nil)) {
		return codes
	}
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(codes)); i++ {
		AppendToArray(&result, this.CurrencyId(GetValue(codes, i)))
	}
	return result
}
func (this *Exchange) MarketsForSymbols(optionalArgs ...interface{}) interface{} {
	symbols := GetArg(optionalArgs, 0, nil)
	_ = symbols
	if IsTrue(IsEqual(symbols, nil)) {
		return symbols
	}
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(symbols)); i++ {
		AppendToArray(&result, this.DerivedExchange.Market(GetValue(symbols, i)))
	}
	return result
}
func (this *Exchange) MarketSymbols(optionalArgs ...interface{}) interface{} {
	symbols := GetArg(optionalArgs, 0, nil)
	_ = symbols
	typeVar := GetArg(optionalArgs, 1, nil)
	_ = typeVar
	allowEmpty := GetArg(optionalArgs, 2, true)
	_ = allowEmpty
	sameTypeOnly := GetArg(optionalArgs, 3, false)
	_ = sameTypeOnly
	sameSubTypeOnly := GetArg(optionalArgs, 4, false)
	_ = sameSubTypeOnly
	if IsTrue(IsEqual(symbols, nil)) {
		if !IsTrue(allowEmpty) {
			panic(ArgumentsRequired(Add(this.Id, " empty list of symbols is not supported")))
		}
		return symbols
	}
	var symbolsLength interface{} = GetArrayLength(symbols)
	if IsTrue(IsEqual(symbolsLength, 0)) {
		if !IsTrue(allowEmpty) {
			panic(ArgumentsRequired(Add(this.Id, " empty list of symbols is not supported")))
		}
		return symbols
	}
	var result interface{} = []interface{}{}
	var marketType interface{} = nil
	var isLinearSubType interface{} = nil
	for i := 0; IsLessThan(i, GetArrayLength(symbols)); i++ {

		var market interface{} = this.DerivedExchange.Market(GetValue(symbols, i))
		PanicOnError(market)
		if IsTrue(IsTrue(sameTypeOnly) && IsTrue((!IsEqual(marketType, nil)))) {
			if IsTrue(!IsEqual(GetValue(market, "type"), marketType)) {
				panic(BadRequest(Add(Add(Add(Add(Add(this.Id, " symbols must be of the same type, either "), marketType), " or "), GetValue(market, "type")), ".")))
			}
		}
		if IsTrue(IsTrue(sameSubTypeOnly) && IsTrue((!IsEqual(isLinearSubType, nil)))) {
			if IsTrue(!IsEqual(GetValue(market, "linear"), isLinearSubType)) {
				panic(BadRequest(Add(this.Id, " symbols must be of the same subType, either linear or inverse.")))
			}
		}
		if IsTrue(IsTrue(!IsEqual(typeVar, nil)) && IsTrue(!IsEqual(GetValue(market, "type"), typeVar))) {
			panic(BadRequest(Add(Add(Add(this.Id, " symbols must be of the same type "), typeVar), ". If the type is incorrect you can change it in options or the params of the request")))
		}
		marketType = GetValue(market, "type")
		if !IsTrue(GetValue(market, "spot")) {
			isLinearSubType = GetValue(market, "linear")
		}
		var symbol interface{} = this.SafeString(market, "symbol", GetValue(symbols, i))
		AppendToArray(&result, symbol)
	}
	return result
}
func (this *Exchange) MarketCodes(optionalArgs ...interface{}) interface{} {
	codes := GetArg(optionalArgs, 0, nil)
	_ = codes
	if IsTrue(IsEqual(codes, nil)) {
		return codes
	}
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(codes)); i++ {
		AppendToArray(&result, this.CommonCurrencyCode(GetValue(codes, i)))
	}
	return result
}
func (this *Exchange) ParseBidsAsks(bidasks interface{}, optionalArgs ...interface{}) interface{} {
	priceKey := GetArg(optionalArgs, 0, 0)
	_ = priceKey
	amountKey := GetArg(optionalArgs, 1, 1)
	_ = amountKey
	countOrIdKey := GetArg(optionalArgs, 2, 2)
	_ = countOrIdKey
	bidasks = this.ToArray(bidasks)
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(bidasks)); i++ {
		AppendToArray(&result, this.ParseBidAsk(GetValue(bidasks, i), priceKey, amountKey, countOrIdKey))
	}
	return result
}
func (this *Exchange) FetchL2OrderBook(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		limit := GetArg(optionalArgs, 0, nil)
		_ = limit
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		orderbook := <-this.DerivedExchange.FetchOrderBook(symbol, limit, params)
		PanicOnError(orderbook)

		ch <- this.Extend(orderbook, map[string]interface{}{
			"asks": this.SortBy(this.Aggregate(GetValue(orderbook, "asks")), 0),
			"bids": this.SortBy(this.Aggregate(GetValue(orderbook, "bids")), 0, true),
		})
		return nil

	}()
	return ch
}
func (this *Exchange) FilterBySymbol(objects interface{}, optionalArgs ...interface{}) interface{} {
	symbol := GetArg(optionalArgs, 0, nil)
	_ = symbol
	if IsTrue(IsEqual(symbol, nil)) {
		return objects
	}
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(objects)); i++ {
		var objectSymbol interface{} = this.SafeString(GetValue(objects, i), "symbol")
		if IsTrue(IsEqual(objectSymbol, symbol)) {
			AppendToArray(&result, GetValue(objects, i))
		}
	}
	return result
}
func (this *Exchange) ParseOHLCV(ohlcv interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	if IsTrue(IsArray(ohlcv)) {
		return []interface{}{this.SafeInteger(ohlcv, 0), this.SafeNumber(ohlcv, 1), this.SafeNumber(ohlcv, 2), this.SafeNumber(ohlcv, 3), this.SafeNumber(ohlcv, 4), this.SafeNumber(ohlcv, 5)}
	}
	return ohlcv
}
func (this *Exchange) NetworkCodeToId(networkCode interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @name exchange#networkCodeToId
	 * @description tries to convert the provided networkCode (which is expected to be an unified network code) to a network id. In order to achieve this, derived class needs to have 'options->networks' defined.
	 * @param {string} networkCode unified network code
	 * @param {string} currencyCode unified currency code, but this argument is not required by default, unless there is an exchange (like huobi) that needs an override of the method to be able to pass currencyCode argument additionally
	 * @returns {string|undefined} exchange-specific network id
	 */
	currencyCode := GetArg(optionalArgs, 0, nil)
	_ = currencyCode
	if IsTrue(IsEqual(networkCode, nil)) {
		return nil
	}
	var networkIdsByCodes interface{} = this.SafeValue(this.Options, "networks", map[string]interface{}{})
	var networkId interface{} = this.SafeString(networkIdsByCodes, networkCode)
	// for example, if 'ETH' is passed for networkCode, but 'ETH' key not defined in `options->networks` object
	if IsTrue(IsEqual(networkId, nil)) {
		if IsTrue(IsEqual(currencyCode, nil)) {
			var currencies interface{} = ObjectValues(this.Currencies)
			for i := 0; IsLessThan(i, GetArrayLength(currencies)); i++ {
				var currency interface{} = GetValue(currencies, i)
				var networks interface{} = this.SafeDict(currency, "networks")
				var network interface{} = this.SafeDict(networks, networkCode)
				networkId = this.SafeString(network, "id")
				if IsTrue(!IsEqual(networkId, nil)) {
					break
				}
			}
		} else {
			// if currencyCode was provided, then we try to find if that currencyCode has a replacement (i.e. ERC20 for ETH) or is in the currency
			var defaultNetworkCodeReplacements interface{} = this.SafeValue(this.Options, "defaultNetworkCodeReplacements", map[string]interface{}{})
			if IsTrue(InOp(defaultNetworkCodeReplacements, currencyCode)) {
				// if there is a replacement for the passed networkCode, then we use it to find network-id in `options->networks` object
				var replacementObject interface{} = GetValue(defaultNetworkCodeReplacements, currencyCode) // i.e. { 'ERC20': 'ETH' }
				var keys interface{} = ObjectKeys(replacementObject)
				for i := 0; IsLessThan(i, GetArrayLength(keys)); i++ {
					var key interface{} = GetValue(keys, i)
					var value interface{} = GetValue(replacementObject, key)
					// if value matches to provided unified networkCode, then we use it's key to find network-id in `options->networks` object
					if IsTrue(IsEqual(value, networkCode)) {
						networkId = this.SafeString(networkIdsByCodes, key)
						break
					}
				}
			} else {
				// serach for network inside currency
				var currency interface{} = this.SafeDict(this.Currencies, currencyCode)
				var networks interface{} = this.SafeDict(currency, "networks")
				var network interface{} = this.SafeDict(networks, networkCode)
				networkId = this.SafeString(network, "id")
			}
		}
		// if it wasn't found, we just set the provided value to network-id
		if IsTrue(IsEqual(networkId, nil)) {
			networkId = networkCode
		}
	}
	return networkId
}
func (this *Exchange) NetworkIdToCode(optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @name exchange#networkIdToCode
	 * @description tries to convert the provided exchange-specific networkId to an unified network Code. In order to achieve this, derived class needs to have "options['networksById']" defined.
	 * @param {string} networkId exchange specific network id/title, like: TRON, Trc-20, usdt-erc20, etc
	 * @param {string|undefined} currencyCode unified currency code, but this argument is not required by default, unless there is an exchange (like huobi) that needs an override of the method to be able to pass currencyCode argument additionally
	 * @returns {string|undefined} unified network code
	 */
	networkId := GetArg(optionalArgs, 0, nil)
	_ = networkId
	currencyCode := GetArg(optionalArgs, 1, nil)
	_ = currencyCode
	if IsTrue(IsEqual(networkId, nil)) {
		return nil
	}
	var networkCodesByIds interface{} = this.SafeDict(this.Options, "networksById", map[string]interface{}{})
	var networkCode interface{} = this.SafeString(networkCodesByIds, networkId, networkId)
	// replace mainnet network-codes (i.e. ERC20->ETH)
	if IsTrue(!IsEqual(currencyCode, nil)) {
		var defaultNetworkCodeReplacements interface{} = this.SafeDict(this.Options, "defaultNetworkCodeReplacements", map[string]interface{}{})
		if IsTrue(InOp(defaultNetworkCodeReplacements, currencyCode)) {
			var replacementObject interface{} = this.SafeDict(defaultNetworkCodeReplacements, currencyCode, map[string]interface{}{})
			networkCode = this.SafeString(replacementObject, networkCode, networkCode)
		}
	}
	return networkCode
}
func (this *Exchange) HandleNetworkCodeAndParams(params interface{}) interface{} {
	var networkCodeInParams interface{} = this.SafeString2(params, "networkCode", "network")
	if IsTrue(!IsEqual(networkCodeInParams, nil)) {
		params = this.Omit(params, []interface{}{"networkCode", "network"})
	}
	// if it was not defined by user, we should not set it from 'defaultNetworks', because handleNetworkCodeAndParams is for only request-side and thus we do not fill it with anything. We can only use 'defaultNetworks' after parsing response-side
	return []interface{}{networkCodeInParams, params}
}
func (this *Exchange) DefaultNetworkCode(currencyCode interface{}) interface{} {
	var defaultNetworkCode interface{} = nil
	var defaultNetworks interface{} = this.SafeDict(this.Options, "defaultNetworks", map[string]interface{}{})
	if IsTrue(InOp(defaultNetworks, currencyCode)) {
		// if currency had set its network in "defaultNetworks", use it
		defaultNetworkCode = GetValue(defaultNetworks, currencyCode)
	} else {
		// otherwise, try to use the global-scope 'defaultNetwork' value (even if that network is not supported by currency, it doesn't make any problem, this will be just used "at first" if currency supports this network at all)
		var defaultNetwork interface{} = this.SafeString(this.Options, "defaultNetwork")
		if IsTrue(!IsEqual(defaultNetwork, nil)) {
			defaultNetworkCode = defaultNetwork
		}
	}
	return defaultNetworkCode
}
func (this *Exchange) SelectNetworkCodeFromUnifiedNetworks(currencyCode interface{}, networkCode interface{}, indexedNetworkEntries interface{}) interface{} {
	return this.SelectNetworkKeyFromNetworks(currencyCode, networkCode, indexedNetworkEntries, true)
}
func (this *Exchange) SelectNetworkIdFromRawNetworks(currencyCode interface{}, networkCode interface{}, indexedNetworkEntries interface{}) interface{} {
	return this.SelectNetworkKeyFromNetworks(currencyCode, networkCode, indexedNetworkEntries, false)
}
func (this *Exchange) SelectNetworkKeyFromNetworks(currencyCode interface{}, networkCode interface{}, indexedNetworkEntries interface{}, optionalArgs ...interface{}) interface{} {
	// this method is used against raw & unparse network entries, which are just indexed by network id
	isIndexedByUnifiedNetworkCode := GetArg(optionalArgs, 0, false)
	_ = isIndexedByUnifiedNetworkCode
	var chosenNetworkId interface{} = nil
	var availableNetworkIds interface{} = ObjectKeys(indexedNetworkEntries)
	var responseNetworksLength interface{} = GetArrayLength(availableNetworkIds)
	if IsTrue(!IsEqual(networkCode, nil)) {
		if IsTrue(IsEqual(responseNetworksLength, 0)) {
			panic(NotSupported(Add(Add(Add(Add(this.Id, " - "), networkCode), " network did not return any result for "), currencyCode)))
		} else {
			// if networkCode was provided by user, we should check it after response, as the referenced exchange doesn't support network-code during request
			var networkIdOrCode interface{} = Ternary(IsTrue(isIndexedByUnifiedNetworkCode), networkCode, this.NetworkCodeToId(networkCode, currencyCode))
			if IsTrue(InOp(indexedNetworkEntries, networkIdOrCode)) {
				chosenNetworkId = networkIdOrCode
			} else {
				panic(NotSupported(Add(Add(Add(Add(Add(Add(this.Id, " - "), networkIdOrCode), " network was not found for "), currencyCode), ", use one of "), Join(availableNetworkIds, ", "))))
			}
		}
	} else {
		if IsTrue(IsEqual(responseNetworksLength, 0)) {
			panic(NotSupported(Add(Add(this.Id, " - no networks were returned for "), currencyCode)))
		} else {
			// if networkCode was not provided by user, then we try to use the default network (if it was defined in "defaultNetworks"), otherwise, we just return the first network entry
			var defaultNetworkCode interface{} = this.DefaultNetworkCode(currencyCode)
			var defaultNetworkId interface{} = Ternary(IsTrue(isIndexedByUnifiedNetworkCode), defaultNetworkCode, this.NetworkCodeToId(defaultNetworkCode, currencyCode))
			if IsTrue(InOp(indexedNetworkEntries, defaultNetworkId)) {
				return defaultNetworkId
			}
			panic(NotSupported(Add(Add(this.Id, " - can not determine the default network, please pass param[\"network\"] one from : "), Join(availableNetworkIds, ", "))))
		}
	}
	return chosenNetworkId
}
func (this *Exchange) SafeNumber2(dictionary interface{}, key1 interface{}, key2 interface{}, optionalArgs ...interface{}) interface{} {
	d := GetArg(optionalArgs, 0, nil)
	_ = d
	var value interface{} = this.SafeString2(dictionary, key1, key2)
	return this.ParseNumber(value, d)
}
func (this *Exchange) ParseOrderBook(orderbook interface{}, symbol interface{}, optionalArgs ...interface{}) interface{} {
	timestamp := GetArg(optionalArgs, 0, nil)
	_ = timestamp
	bidsKey := GetArg(optionalArgs, 1, "bids")
	_ = bidsKey
	asksKey := GetArg(optionalArgs, 2, "asks")
	_ = asksKey
	priceKey := GetArg(optionalArgs, 3, 0)
	_ = priceKey
	amountKey := GetArg(optionalArgs, 4, 1)
	_ = amountKey
	countOrIdKey := GetArg(optionalArgs, 5, 2)
	_ = countOrIdKey

	var bids interface{} = this.DerivedExchange.ParseBidsAsks(this.SafeValue(orderbook, bidsKey, []interface{}{}), priceKey, amountKey, countOrIdKey)
	PanicOnError(bids)

	var asks interface{} = this.DerivedExchange.ParseBidsAsks(this.SafeValue(orderbook, asksKey, []interface{}{}), priceKey, amountKey, countOrIdKey)
	PanicOnError(asks)
	return map[string]interface{}{
		"symbol":    symbol,
		"bids":      this.SortBy(bids, 0, true),
		"asks":      this.SortBy(asks, 0),
		"timestamp": timestamp,
		"datetime":  this.Iso8601(timestamp),
		"nonce":     nil,
	}
}
func (this *Exchange) ParseOHLCVs(ohlcvs interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	timeframe := GetArg(optionalArgs, 1, "1m")
	_ = timeframe
	since := GetArg(optionalArgs, 2, nil)
	_ = since
	limit := GetArg(optionalArgs, 3, nil)
	_ = limit
	tail := GetArg(optionalArgs, 4, false)
	_ = tail
	var results interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(ohlcvs)); i++ {
		AppendToArray(&results, this.DerivedExchange.ParseOHLCV(GetValue(ohlcvs, i), market))
	}
	var sorted interface{} = this.SortBy(results, 0)
	return this.FilterBySinceLimit(sorted, since, limit, 0, tail)
}
func (this *Exchange) ParseLeverageTiers(response interface{}, optionalArgs ...interface{}) interface{} {
	// marketIdKey should only be undefined when response is a dictionary.
	symbols := GetArg(optionalArgs, 0, nil)
	_ = symbols
	marketIdKey := GetArg(optionalArgs, 1, nil)
	_ = marketIdKey
	symbols = this.MarketSymbols(symbols)
	var tiers interface{} = map[string]interface{}{}
	var symbolsLength interface{} = 0
	if IsTrue(!IsEqual(symbols, nil)) {
		symbolsLength = GetArrayLength(symbols)
	}
	var noSymbols interface{} = IsTrue((IsEqual(symbols, nil))) || IsTrue((IsEqual(symbolsLength, 0)))
	if IsTrue(IsArray(response)) {
		for i := 0; IsLessThan(i, GetArrayLength(response)); i++ {
			var item interface{} = GetValue(response, i)
			var id interface{} = this.SafeString(item, marketIdKey)

			var market interface{} = this.DerivedExchange.SafeMarket(id, nil, nil, "swap")
			PanicOnError(market)
			var symbol interface{} = GetValue(market, "symbol")
			var contract interface{} = this.SafeBool(market, "contract", false)
			if IsTrue(IsTrue(contract) && IsTrue((IsTrue(noSymbols) || IsTrue(this.InArray(symbol, symbols))))) {
				AddElementToObject(tiers, symbol, this.DerivedExchange.ParseMarketLeverageTiers(item, market))
			}
		}
	} else {
		var keys interface{} = ObjectKeys(response)
		for i := 0; IsLessThan(i, GetArrayLength(keys)); i++ {
			var marketId interface{} = GetValue(keys, i)
			var item interface{} = GetValue(response, marketId)

			var market interface{} = this.DerivedExchange.SafeMarket(marketId, nil, nil, "swap")
			PanicOnError(market)
			var symbol interface{} = GetValue(market, "symbol")
			var contract interface{} = this.SafeBool(market, "contract", false)
			if IsTrue(IsTrue(contract) && IsTrue((IsTrue(noSymbols) || IsTrue(this.InArray(symbol, symbols))))) {
				AddElementToObject(tiers, symbol, this.DerivedExchange.ParseMarketLeverageTiers(item, market))
			}
		}
	}
	return tiers
}
func (this *Exchange) LoadTradingLimits(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		reload := GetArg(optionalArgs, 1, false)
		_ = reload
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchTradingLimits")) {
			if IsTrue(IsTrue(reload) || !IsTrue((InOp(this.Options, "limitsLoaded")))) {

				response := (<-this.FetchTradingLimits(symbols))
				PanicOnError(response)
				for i := 0; IsLessThan(i, GetArrayLength(symbols)); i++ {
					var symbol interface{} = GetValue(symbols, i)
					AddElementToObject(this.Markets, symbol, this.DeepExtend(GetValue(this.Markets, symbol), GetValue(response, symbol)))
				}
				AddElementToObject(this.Options, "limitsLoaded", this.Milliseconds())
			}
		}

		ch <- this.Markets
		return nil

	}()
	return ch
}
func (this *Exchange) SafePosition(position interface{}) interface{} {
	// simplified version of: /pull/12765/
	var unrealizedPnlString interface{} = this.SafeString(position, "unrealisedPnl")
	var initialMarginString interface{} = this.SafeString(position, "initialMargin")
	//
	// PERCENTAGE
	//
	var percentage interface{} = this.SafeValue(position, "percentage")
	if IsTrue(IsTrue(IsTrue((IsEqual(percentage, nil))) && IsTrue((!IsEqual(unrealizedPnlString, nil)))) && IsTrue((!IsEqual(initialMarginString, nil)))) {
		// as it was done in all implementations ( aax, btcex, bybit, deribit, ftx, gate, kucoinfutures, phemex )
		var percentageString interface{} = Precise.StringMul(Precise.StringDiv(unrealizedPnlString, initialMarginString, 4), "100")
		AddElementToObject(position, "percentage", this.ParseNumber(percentageString))
	}
	// if contractSize is undefined get from market
	var contractSize interface{} = this.SafeNumber(position, "contractSize")
	var symbol interface{} = this.SafeString(position, "symbol")
	var market interface{} = nil
	if IsTrue(!IsEqual(symbol, nil)) {
		market = this.SafeValue(this.Markets, symbol)
	}
	if IsTrue(IsTrue(IsEqual(contractSize, nil)) && IsTrue(!IsEqual(market, nil))) {
		contractSize = this.SafeNumber(market, "contractSize")
		AddElementToObject(position, "contractSize", contractSize)
	}
	return position
}
func (this *Exchange) ParsePositions(positions interface{}, optionalArgs ...interface{}) interface{} {
	symbols := GetArg(optionalArgs, 0, nil)
	_ = symbols
	params := GetArg(optionalArgs, 1, map[string]interface{}{})
	_ = params
	symbols = this.MarketSymbols(symbols)
	positions = this.ToArray(positions)
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(positions)); i++ {
		var position interface{} = this.Extend(this.DerivedExchange.ParsePosition(GetValue(positions, i), nil), params)
		AppendToArray(&result, position)
	}
	return this.FilterByArrayPositions(result, "symbol", symbols, false)
}
func (this *Exchange) ParseAccounts(accounts interface{}, optionalArgs ...interface{}) interface{} {
	params := GetArg(optionalArgs, 0, map[string]interface{}{})
	_ = params
	accounts = this.ToArray(accounts)
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(accounts)); i++ {
		var account interface{} = this.Extend(this.DerivedExchange.ParseAccount(GetValue(accounts, i)), params)
		AppendToArray(&result, account)
	}
	return result
}
func (this *Exchange) ParseTradesHelper(isWs interface{}, trades interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	params := GetArg(optionalArgs, 3, map[string]interface{}{})
	_ = params
	trades = this.ToArray(trades)
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(trades)); i++ {
		var parsed interface{} = nil
		if IsTrue(isWs) {
			parsed = this.ParseWsTrade(GetValue(trades, i), market)
		} else {

			parsed = this.DerivedExchange.ParseTrade(GetValue(trades, i), market)
			PanicOnError(parsed)
		}
		var trade interface{} = this.Extend(parsed, params)
		AppendToArray(&result, trade)
	}
	result = this.SortBy2(result, "timestamp", "id")
	var symbol interface{} = Ternary(IsTrue((!IsEqual(market, nil))), GetValue(market, "symbol"), nil)
	return this.FilterBySymbolSinceLimit(result, symbol, since, limit)
}
func (this *Exchange) ParseTrades(trades interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	params := GetArg(optionalArgs, 3, map[string]interface{}{})
	_ = params
	return this.ParseTradesHelper(false, trades, market, since, limit, params)
}
func (this *Exchange) ParseWsTrades(trades interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	params := GetArg(optionalArgs, 3, map[string]interface{}{})
	_ = params
	return this.ParseTradesHelper(true, trades, market, since, limit, params)
}
func (this *Exchange) ParseTransactions(transactions interface{}, optionalArgs ...interface{}) interface{} {
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	params := GetArg(optionalArgs, 3, map[string]interface{}{})
	_ = params
	transactions = this.ToArray(transactions)
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(transactions)); i++ {
		var transaction interface{} = this.Extend(this.DerivedExchange.ParseTransaction(GetValue(transactions, i), currency), params)
		AppendToArray(&result, transaction)
	}
	result = this.SortBy(result, "timestamp")
	var code interface{} = Ternary(IsTrue((!IsEqual(currency, nil))), GetValue(currency, "code"), nil)
	return this.FilterByCurrencySinceLimit(result, code, since, limit)
}
func (this *Exchange) ParseTransfers(transfers interface{}, optionalArgs ...interface{}) interface{} {
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	params := GetArg(optionalArgs, 3, map[string]interface{}{})
	_ = params
	transfers = this.ToArray(transfers)
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(transfers)); i++ {
		var transfer interface{} = this.Extend(this.DerivedExchange.ParseTransfer(GetValue(transfers, i), currency), params)
		AppendToArray(&result, transfer)
	}
	result = this.SortBy(result, "timestamp")
	var code interface{} = Ternary(IsTrue((!IsEqual(currency, nil))), GetValue(currency, "code"), nil)
	return this.FilterByCurrencySinceLimit(result, code, since, limit)
}
func (this *Exchange) ParseLedger(data interface{}, optionalArgs ...interface{}) interface{} {
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	params := GetArg(optionalArgs, 3, map[string]interface{}{})
	_ = params
	var result interface{} = []interface{}{}
	var arrayData interface{} = this.ToArray(data)
	for i := 0; IsLessThan(i, GetArrayLength(arrayData)); i++ {

		var itemOrItems interface{} = this.DerivedExchange.ParseLedgerEntry(GetValue(arrayData, i), currency)
		PanicOnError(itemOrItems)
		if IsTrue(IsArray(itemOrItems)) {
			for j := 0; IsLessThan(j, GetArrayLength(itemOrItems)); j++ {
				AppendToArray(&result, this.Extend(GetValue(itemOrItems, j), params))
			}
		} else {
			AppendToArray(&result, this.Extend(itemOrItems, params))
		}
	}
	result = this.SortBy(result, "timestamp")
	var code interface{} = Ternary(IsTrue((!IsEqual(currency, nil))), GetValue(currency, "code"), nil)
	return this.FilterByCurrencySinceLimit(result, code, since, limit)
}
func (this *Exchange) Nonce() interface{} {
	return this.Seconds()
}
func (this *Exchange) SetHeaders(headers interface{}) interface{} {
	return headers
}
func (this *Exchange) CurrencyId(code interface{}) interface{} {
	var currency interface{} = this.SafeDict(this.Currencies, code)
	if IsTrue(IsEqual(currency, nil)) {
		currency = this.SafeCurrency(code)
	}
	if IsTrue(!IsEqual(currency, nil)) {
		return GetValue(currency, "id")
	}
	return code
}
func (this *Exchange) MarketId(symbol interface{}) interface{} {

	var market interface{} = this.DerivedExchange.Market(symbol)
	PanicOnError(market)
	if IsTrue(!IsEqual(market, nil)) {
		return GetValue(market, "id")
	}
	return symbol
}
func (this *Exchange) Symbol(symbol interface{}) interface{} {

	var market interface{} = this.DerivedExchange.Market(symbol)
	PanicOnError(market)
	return this.SafeString(market, "symbol", symbol)
}
func (this *Exchange) HandleParamString(params interface{}, paramName interface{}, optionalArgs ...interface{}) interface{} {
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	var value interface{} = this.SafeString(params, paramName, defaultValue)
	if IsTrue(!IsEqual(value, nil)) {
		params = this.Omit(params, paramName)
	}
	return []interface{}{value, params}
}
func (this *Exchange) HandleParamString2(params interface{}, paramName1 interface{}, paramName2 interface{}, optionalArgs ...interface{}) interface{} {
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	var value interface{} = this.SafeString2(params, paramName1, paramName2, defaultValue)
	if IsTrue(!IsEqual(value, nil)) {
		params = this.Omit(params, []interface{}{paramName1, paramName2})
	}
	return []interface{}{value, params}
}
func (this *Exchange) HandleParamInteger(params interface{}, paramName interface{}, optionalArgs ...interface{}) interface{} {
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	var value interface{} = this.SafeInteger(params, paramName, defaultValue)
	if IsTrue(!IsEqual(value, nil)) {
		params = this.Omit(params, paramName)
	}
	return []interface{}{value, params}
}
func (this *Exchange) HandleParamInteger2(params interface{}, paramName1 interface{}, paramName2 interface{}, optionalArgs ...interface{}) interface{} {
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	var value interface{} = this.SafeInteger2(params, paramName1, paramName2, defaultValue)
	if IsTrue(!IsEqual(value, nil)) {
		params = this.Omit(params, []interface{}{paramName1, paramName2})
	}
	return []interface{}{value, params}
}
func (this *Exchange) HandleParamBool(params interface{}, paramName interface{}, optionalArgs ...interface{}) interface{} {
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	var value interface{} = this.SafeBool(params, paramName, defaultValue)
	if IsTrue(!IsEqual(value, nil)) {
		params = this.Omit(params, paramName)
	}
	return []interface{}{value, params}
}
func (this *Exchange) HandleParamBool2(params interface{}, paramName1 interface{}, paramName2 interface{}, optionalArgs ...interface{}) interface{} {
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	var value interface{} = this.SafeBool2(params, paramName1, paramName2, defaultValue)
	if IsTrue(!IsEqual(value, nil)) {
		params = this.Omit(params, []interface{}{paramName1, paramName2})
	}
	return []interface{}{value, params}
}

/**
 * @param {object} params - extra parameters
 * @param {object} request - existing dictionary of request
 * @param {string} exchangeSpecificKey - the key for chain id to be set in request
 * @param {object} currencyCode - (optional) existing dictionary of request
 * @param {boolean} isRequired - (optional) whether that param is required to be present
 * @returns {object[]} - returns [request, params] where request is the modified request object and params is the modified params object
 */
func (this *Exchange) HandleRequestNetwork(params interface{}, request interface{}, exchangeSpecificKey interface{}, optionalArgs ...interface{}) interface{} {
	currencyCode := GetArg(optionalArgs, 0, nil)
	_ = currencyCode
	isRequired := GetArg(optionalArgs, 1, false)
	_ = isRequired
	var networkCode interface{} = nil
	networkCodeparamsVariable := this.HandleNetworkCodeAndParams(params)
	networkCode = GetValue(networkCodeparamsVariable, 0)
	params = GetValue(networkCodeparamsVariable, 1)
	if IsTrue(!IsEqual(networkCode, nil)) {
		AddElementToObject(request, exchangeSpecificKey, this.NetworkCodeToId(networkCode, currencyCode))
	} else if IsTrue(isRequired) {
		panic(ArgumentsRequired(Add(this.Id, " - \"network\" param is required for this request")))
	}
	return []interface{}{request, params}
}
func (this *Exchange) ResolvePath(path interface{}, params interface{}) interface{} {
	return []interface{}{this.ImplodeParams(path, params), this.Omit(params, this.ExtractParams(path))}
}
func (this *Exchange) GetListFromObjectValues(objects interface{}, key interface{}) interface{} {
	var newArray interface{} = objects
	if !IsTrue(IsArray(objects)) {
		newArray = this.ToArray(objects)
	}
	var results interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(newArray)); i++ {
		AppendToArray(&results, GetValue(GetValue(newArray, i), key))
	}
	return results
}
func (this *Exchange) GetSymbolsForMarketType(optionalArgs ...interface{}) interface{} {
	marketType := GetArg(optionalArgs, 0, nil)
	_ = marketType
	subType := GetArg(optionalArgs, 1, nil)
	_ = subType
	symbolWithActiveStatus := GetArg(optionalArgs, 2, true)
	_ = symbolWithActiveStatus
	symbolWithUnknownStatus := GetArg(optionalArgs, 3, true)
	_ = symbolWithUnknownStatus
	var filteredMarkets interface{} = this.Markets
	if IsTrue(!IsEqual(marketType, nil)) {
		filteredMarkets = this.FilterBy(filteredMarkets, "type", marketType)
	}
	if IsTrue(!IsEqual(subType, nil)) {
		this.CheckRequiredArgument("getSymbolsForMarketType", subType, "subType", []interface{}{"linear", "inverse", "quanto"})
		filteredMarkets = this.FilterBy(filteredMarkets, "subType", subType)
	}
	var activeStatuses interface{} = []interface{}{}
	if IsTrue(symbolWithActiveStatus) {
		AppendToArray(&activeStatuses, true)
	}
	if IsTrue(symbolWithUnknownStatus) {
		AppendToArray(&activeStatuses, nil)
	}
	filteredMarkets = this.FilterByArray(filteredMarkets, "active", activeStatuses, false)
	return this.GetListFromObjectValues(filteredMarkets, "symbol")
}
func (this *Exchange) FilterByArray(objects interface{}, key interface{}, optionalArgs ...interface{}) interface{} {
	values := GetArg(optionalArgs, 0, nil)
	_ = values
	indexed := GetArg(optionalArgs, 1, true)
	_ = indexed
	objects = this.ToArray(objects)
	// return all of them if no values were passed
	if IsTrue(IsTrue(IsEqual(values, nil)) || !IsTrue(values)) {
		// return indexed ? this.indexBy (objects, key) : objects;
		if IsTrue(indexed) {
			return this.IndexBy(objects, key)
		} else {
			return objects
		}
	}
	var results interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(objects)); i++ {
		if IsTrue(this.InArray(GetValue(GetValue(objects, i), key), values)) {
			AppendToArray(&results, GetValue(objects, i))
		}
	}
	// return indexed ? this.indexBy (results, key) : results;
	if IsTrue(indexed) {
		return this.IndexBy(results, key)
	}
	return results
}
func (this *Exchange) Fetch2(path interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		api := GetArg(optionalArgs, 0, "public")
		_ = api
		method := GetArg(optionalArgs, 1, "GET")
		_ = method
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		headers := GetArg(optionalArgs, 3, nil)
		_ = headers
		body := GetArg(optionalArgs, 4, nil)
		_ = body
		config := GetArg(optionalArgs, 5, map[string]interface{}{})
		_ = config
		if IsTrue(this.EnableRateLimit) {
			var cost interface{} = this.CalculateRateLimiterCost(api, method, path, params, config)

			retRes494912 := (<-this.Throttle(cost))
			PanicOnError(retRes494912)
		}
		var retries interface{} = nil
		retriesparamsVariable := this.HandleOptionAndParams(params, path, "maxRetriesOnFailure", 0)
		retries = GetValue(retriesparamsVariable, 0)
		params = GetValue(retriesparamsVariable, 1)
		var retryDelay interface{} = nil
		retryDelayparamsVariable := this.HandleOptionAndParams(params, path, "maxRetriesOnFailureDelay", 0)
		retryDelay = GetValue(retryDelayparamsVariable, 0)
		params = GetValue(retryDelayparamsVariable, 1)
		this.LastRestRequestTimestamp = this.Milliseconds()

		var request interface{} = this.DerivedExchange.Sign(path, api, method, params, headers, body)
		PanicOnError(request)
		this.Last_request_headers = GetValue(request, "headers")
		this.Last_request_body = GetValue(request, "body")
		this.Last_request_url = GetValue(request, "url")
		for i := 0; IsLessThan(i, Add(retries, 1)); i++ {

			{
				func(this *Exchange) (ret_ interface{}) {
					defer func() {
						if e := recover(); e != nil {
							if e == "break" {
								return
							}
							ret_ = func(this *Exchange) interface{} {
								// catch block:
								if IsTrue(IsInstance(e, OperationFailed)) {
									if IsTrue(IsLessThan(i, retries)) {
										if IsTrue(this.Verbose) {
											this.Log(Add(Add(Add(Add(Add(Add("Request failed with the error: ", ToString(e)), ", retrying "), ToString((Add(i, 1)))), " of "), ToString(retries)), "..."))
										}
										if IsTrue(IsTrue((!IsEqual(retryDelay, nil))) && IsTrue((!IsEqual(retryDelay, 0)))) {

											retRes497028 := (<-this.Sleep(retryDelay))
											PanicOnError(retRes497028)
										}
									} else {
										panic(e)
									}
								} else {
									panic(e)
								}
								return nil
							}(this)
						}
					}()
					// try block:

					retRes496223 := (<-this.Fetch(GetValue(request, "url"), GetValue(request, "method"), GetValue(request, "headers"), GetValue(request, "body")))
					PanicOnError(retRes496223)
					ch <- retRes496223
					return nil

				}(this)

			}
		}

		return nil

	}()
	return ch
}
func (this *Exchange) Request(path interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		api := GetArg(optionalArgs, 0, "public")
		_ = api
		method := GetArg(optionalArgs, 1, "GET")
		_ = method
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		headers := GetArg(optionalArgs, 3, nil)
		_ = headers
		body := GetArg(optionalArgs, 4, nil)
		_ = body
		config := GetArg(optionalArgs, 5, map[string]interface{}{})
		_ = config

		retRes498415 := (<-this.Fetch2(path, api, method, params, headers, body, config))
		PanicOnError(retRes498415)
		ch <- retRes498415
		return nil

	}()
	return ch
}
func (this *Exchange) LoadAccounts(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		reload := GetArg(optionalArgs, 0, false)
		_ = reload
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		if IsTrue(reload) {

			this.Accounts = <-this.DerivedExchange.FetchAccounts(params)
			PanicOnError(this.Accounts)
		} else {
			if IsTrue(this.Accounts) {

				ch <- this.Accounts
				return nil
			} else {

				this.Accounts = <-this.DerivedExchange.FetchAccounts(params)
				PanicOnError(this.Accounts)
			}
		}
		this.AccountsById = this.IndexBy(this.Accounts, "id")

		ch <- this.Accounts
		return nil

	}()
	return ch
}
func (this *Exchange) BuildOHLCVC(trades interface{}, optionalArgs ...interface{}) interface{} {
	// given a sorted arrays of trades (recent last) and a timeframe builds an array of OHLCV candles
	// note, default limit value (**********) is max int32 value
	timeframe := GetArg(optionalArgs, 0, "1m")
	_ = timeframe
	since := GetArg(optionalArgs, 1, 0)
	_ = since
	limit := GetArg(optionalArgs, 2, **********)
	_ = limit
	var ms interface{} = Multiply(this.ParseTimeframe(timeframe), 1000)
	var ohlcvs interface{} = []interface{}{}
	var i_timestamp interface{} = 0
	// const open = 1;
	var i_high interface{} = 2
	var i_low interface{} = 3
	var i_close interface{} = 4
	var i_volume interface{} = 5
	var i_count interface{} = 6
	var tradesLength interface{} = GetArrayLength(trades)
	var oldest interface{} = mathMin(tradesLength, limit)
	for i := 0; IsLessThan(i, oldest); i++ {
		var trade interface{} = GetValue(trades, i)
		var ts interface{} = GetValue(trade, "timestamp")
		if IsTrue(IsLessThan(ts, since)) {
			continue
		}
		var openingTime interface{} = Multiply(MathFloor(Divide(ts, ms)), ms) // shift to the edge of m/h/d (but not M)
		if IsTrue(IsLessThan(openingTime, since)) {
			continue
		}
		var ohlcv_length interface{} = GetArrayLength(ohlcvs)
		var candle interface{} = Subtract(ohlcv_length, 1)
		if IsTrue(IsTrue((IsEqual(candle, OpNeg(1)))) || IsTrue((IsGreaterThanOrEqual(openingTime, this.Sum(GetValue(GetValue(ohlcvs, candle), i_timestamp), ms))))) {
			// moved to a new timeframe -> create a new candle from opening trade
			AppendToArray(&ohlcvs, []interface{}{openingTime, GetValue(trade, "price"), GetValue(trade, "price"), GetValue(trade, "price"), GetValue(trade, "price"), GetValue(trade, "amount"), 1})
		} else {
			// still processing the same timeframe -> update opening trade
			AddElementToObject(GetValue(ohlcvs, candle), i_high, mathMax(GetValue(GetValue(ohlcvs, candle), i_high), GetValue(trade, "price")))
			AddElementToObject(GetValue(ohlcvs, candle), i_low, mathMin(GetValue(GetValue(ohlcvs, candle), i_low), GetValue(trade, "price")))
			AddElementToObject(GetValue(ohlcvs, candle), i_close, GetValue(trade, "price"))
			AddElementToObject(GetValue(ohlcvs, candle), i_volume, this.Sum(GetValue(GetValue(ohlcvs, candle), i_volume), GetValue(trade, "amount")))
			AddElementToObject(GetValue(ohlcvs, candle), i_count, this.Sum(GetValue(GetValue(ohlcvs, candle), i_count), 1))
		}
	}
	return ohlcvs
}
func (this *Exchange) ParseTradingViewOHLCV(ohlcvs interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	timeframe := GetArg(optionalArgs, 1, "1m")
	_ = timeframe
	since := GetArg(optionalArgs, 2, nil)
	_ = since
	limit := GetArg(optionalArgs, 3, nil)
	_ = limit
	var result interface{} = this.ConvertTradingViewToOHLCV(ohlcvs)
	return this.ParseOHLCVs(result, market, timeframe, since, limit)
}
func (this *Exchange) EditLimitBuyOrder(id interface{}, symbol interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes505615 := (<-this.EditLimitOrder(id, symbol, "buy", amount, price, params))
		PanicOnError(retRes505615)
		ch <- retRes505615
		return nil

	}()
	return ch
}
func (this *Exchange) EditLimitSellOrder(id interface{}, symbol interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes506015 := (<-this.EditLimitOrder(id, symbol, "sell", amount, price, params))
		PanicOnError(retRes506015)
		ch <- retRes506015
		return nil

	}()
	return ch
}
func (this *Exchange) EditLimitOrder(id interface{}, symbol interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes506415 := <-this.DerivedExchange.EditOrder(id, symbol, "limit", side, amount, price, params)
		PanicOnError(retRes506415)
		ch <- retRes506415
		return nil

	}()
	return ch
}
func (this *Exchange) EditOrder(id interface{}, symbol interface{}, typeVar interface{}, side interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		amount := GetArg(optionalArgs, 0, nil)
		_ = amount
		price := GetArg(optionalArgs, 1, nil)
		_ = price
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params

		retRes50688 := <-this.DerivedExchange.CancelOrder(id, symbol)
		PanicOnError(retRes50688)

		retRes506915 := <-this.DerivedExchange.CreateOrder(symbol, typeVar, side, amount, price, params)
		PanicOnError(retRes506915)
		ch <- retRes506915
		return nil

	}()
	return ch
}
func (this *Exchange) EditOrderWs(id interface{}, symbol interface{}, typeVar interface{}, side interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		amount := GetArg(optionalArgs, 0, nil)
		_ = amount
		price := GetArg(optionalArgs, 1, nil)
		_ = price
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params

		retRes50738 := (<-this.CancelOrderWs(id, symbol))
		PanicOnError(retRes50738)

		retRes507415 := (<-this.CreateOrderWs(symbol, typeVar, side, amount, price, params))
		PanicOnError(retRes507415)
		ch <- retRes507415
		return nil

	}()
	return ch
}
func (this *Exchange) FetchPosition(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchPosition() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchPositionWs(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchPositionWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchPosition(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchPosition() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchPositions(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchPositions() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchPositionForSymbols(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes509415 := (<-this.WatchPositions(symbols, since, limit, params))
		PanicOnError(retRes509415)
		ch <- retRes509415
		return nil

	}()
	return ch
}
func (this *Exchange) FetchPositionsForSymbol(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchPositionsForSymbol() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchPositionsForSymbolWs(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchPositionsForSymbol() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchPositions(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchPositions() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchPositionsWs(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchPositions() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchPositionsRisk(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchPositionsRisk() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchBidsAsks(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchBidsAsks() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchBorrowInterest(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		symbol := GetArg(optionalArgs, 1, nil)
		_ = symbol
		since := GetArg(optionalArgs, 2, nil)
		_ = since
		limit := GetArg(optionalArgs, 3, nil)
		_ = limit
		params := GetArg(optionalArgs, 4, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchBorrowInterest() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchLedger(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchLedger() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchLedgerEntry(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchLedgerEntry() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) ParseBidAsk(bidask interface{}, optionalArgs ...interface{}) interface{} {
	priceKey := GetArg(optionalArgs, 0, 0)
	_ = priceKey
	amountKey := GetArg(optionalArgs, 1, 1)
	_ = amountKey
	countOrIdKey := GetArg(optionalArgs, 2, 2)
	_ = countOrIdKey
	var price interface{} = this.SafeNumber(bidask, priceKey)
	var amount interface{} = this.SafeNumber(bidask, amountKey)
	var countOrId interface{} = this.SafeInteger(bidask, countOrIdKey)
	var bidAsk interface{} = []interface{}{price, amount}
	if IsTrue(!IsEqual(countOrId, nil)) {
		AppendToArray(&bidAsk, countOrId)
	}
	return bidAsk
}
func (this *Exchange) SafeCurrency(currencyId interface{}, optionalArgs ...interface{}) interface{} {
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	if IsTrue(IsTrue((IsEqual(currencyId, nil))) && IsTrue((!IsEqual(currency, nil)))) {
		return currency
	}
	if IsTrue(IsTrue(IsTrue((!IsEqual(this.Currencies_by_id, nil))) && IsTrue((InOp(this.Currencies_by_id, currencyId)))) && IsTrue((!IsEqual(GetValue(this.Currencies_by_id, currencyId), nil)))) {
		return GetValue(this.Currencies_by_id, currencyId)
	}
	var code interface{} = currencyId
	if IsTrue(!IsEqual(currencyId, nil)) {
		code = this.CommonCurrencyCode(ToUpper(currencyId))
	}
	return this.SafeCurrencyStructure(map[string]interface{}{
		"id":        currencyId,
		"code":      code,
		"precision": nil,
	})
}
func (this *Exchange) SafeMarket(optionalArgs ...interface{}) interface{} {
	marketId := GetArg(optionalArgs, 0, nil)
	_ = marketId
	market := GetArg(optionalArgs, 1, nil)
	_ = market
	delimiter := GetArg(optionalArgs, 2, nil)
	_ = delimiter
	marketType := GetArg(optionalArgs, 3, nil)
	_ = marketType
	var result interface{} = this.SafeMarketStructure(map[string]interface{}{
		"symbol":   marketId,
		"marketId": marketId,
	})
	if IsTrue(!IsEqual(marketId, nil)) {
		if IsTrue(IsTrue((!IsEqual(this.Markets_by_id, nil))) && IsTrue((InOp(this.Markets_by_id, marketId)))) {
			var markets interface{} = GetValue(this.Markets_by_id, marketId)
			var numMarkets interface{} = GetArrayLength(markets)
			if IsTrue(IsEqual(numMarkets, 1)) {
				return GetValue(markets, 0)
			} else {
				if IsTrue(IsEqual(marketType, nil)) {
					if IsTrue(IsEqual(market, nil)) {
						panic(ArgumentsRequired(Add(Add(Add(this.Id, " safeMarket() requires a fourth argument for "), marketId), " to disambiguate between different markets with the same market id")))
					} else {
						marketType = GetValue(market, "type")
					}
				}
				for i := 0; IsLessThan(i, GetArrayLength(markets)); i++ {
					var currentMarket interface{} = GetValue(markets, i)
					if IsTrue(GetValue(currentMarket, marketType)) {
						return currentMarket
					}
				}
			}
		} else if IsTrue(IsTrue(!IsEqual(delimiter, nil)) && IsTrue(!IsEqual(delimiter, ""))) {
			var parts interface{} = Split(marketId, delimiter)
			var partsLength interface{} = GetArrayLength(parts)
			if IsTrue(IsEqual(partsLength, 2)) {
				AddElementToObject(result, "baseId", this.SafeString(parts, 0))
				AddElementToObject(result, "quoteId", this.SafeString(parts, 1))
				AddElementToObject(result, "base", this.DerivedExchange.SafeCurrencyCode(GetValue(result, "baseId")))
				AddElementToObject(result, "quote", this.DerivedExchange.SafeCurrencyCode(GetValue(result, "quoteId")))
				AddElementToObject(result, "symbol", Add(Add(GetValue(result, "base"), "/"), GetValue(result, "quote")))
				return result
			} else {
				return result
			}
		}
	}
	if IsTrue(!IsEqual(market, nil)) {
		return market
	}
	return result
}
func (this *Exchange) MarketOrNull(symbol interface{}) interface{} {
	if IsTrue(IsEqual(symbol, nil)) {
		return nil
	}
	return this.DerivedExchange.Market(symbol)
}
func (this *Exchange) CheckRequiredCredentials(optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @param {boolean} error throw an error that a credential is required if true
	 * @returns {boolean} true if all required credentials have been set, otherwise false or an error is thrown is param error=true
	 */
	error := GetArg(optionalArgs, 0, true)
	_ = error
	var keys interface{} = ObjectKeys(this.RequiredCredentials)
	for i := 0; IsLessThan(i, GetArrayLength(keys)); i++ {
		var key interface{} = GetValue(keys, i)
		if IsTrue(IsTrue(GetValue(this.RequiredCredentials, key)) && !IsTrue(GetValue(this, key))) {
			if IsTrue(error) {
				panic(AuthenticationError(Add(Add(Add(this.Id, " requires \""), key), "\" credential")))
			} else {
				return false
			}
		}
	}
	return true
}
func (this *Exchange) Oath() interface{} {
	if IsTrue(!IsEqual(this.Twofa, nil)) {
		return Totp(this.Twofa)
	} else {
		panic(ExchangeError(Add(this.Id, " exchange.twofa has not been set for 2FA Two-Factor Authentication")))
	}
}
func (this *Exchange) FetchBalance(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchBalance() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchBalanceWs(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchBalanceWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) ParseBalance(response interface{}) interface{} {
	panic(NotSupported(Add(this.Id, " parseBalance() is not supported yet")))
}
func (this *Exchange) WatchBalance(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchBalance() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchPartialBalance(part interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		balance := <-this.DerivedExchange.FetchBalance(params)
		PanicOnError(balance)

		ch <- GetValue(balance, part)
		return nil

	}()
	return ch
}
func (this *Exchange) FetchFreeBalance(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes528315 := (<-this.FetchPartialBalance("free", params))
		PanicOnError(retRes528315)
		ch <- retRes528315
		return nil

	}()
	return ch
}
func (this *Exchange) FetchUsedBalance(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes528715 := (<-this.FetchPartialBalance("used", params))
		PanicOnError(retRes528715)
		ch <- retRes528715
		return nil

	}()
	return ch
}
func (this *Exchange) FetchTotalBalance(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes529115 := (<-this.FetchPartialBalance("total", params))
		PanicOnError(retRes529115)
		ch <- retRes529115
		return nil

	}()
	return ch
}
func (this *Exchange) FetchStatus(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchStatus() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchTransactionFee(code interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if !IsTrue(GetValue(this.Has, "fetchTransactionFees")) {
			panic(NotSupported(Add(this.Id, " fetchTransactionFee() is not supported yet")))
		}

		retRes530215 := (<-this.FetchTransactionFees([]interface{}{code}, params))
		PanicOnError(retRes530215)
		ch <- retRes530215
		return nil

	}()
	return ch
}
func (this *Exchange) FetchTransactionFees(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		codes := GetArg(optionalArgs, 0, nil)
		_ = codes
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchTransactionFees() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchDepositWithdrawFees(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		codes := GetArg(optionalArgs, 0, nil)
		_ = codes
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchDepositWithdrawFees() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchDepositWithdrawFee(code interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if !IsTrue(GetValue(this.Has, "fetchDepositWithdrawFees")) {
			panic(NotSupported(Add(this.Id, " fetchDepositWithdrawFee() is not supported yet")))
		}

		fees := <-this.DerivedExchange.FetchDepositWithdrawFees([]interface{}{code}, params)
		PanicOnError(fees)

		ch <- this.SafeValue(fees, code)
		return nil

	}()
	return ch
}
func (this *Exchange) GetSupportedMapping(key interface{}, optionalArgs ...interface{}) interface{} {
	mapping := GetArg(optionalArgs, 0, map[string]interface{}{})
	_ = mapping
	if IsTrue(InOp(mapping, key)) {
		return GetValue(mapping, key)
	} else {
		panic(NotSupported(Add(Add(Add(this.Id, " "), key), " does not have a value in mapping")))
	}
}
func (this *Exchange) FetchCrossBorrowRate(code interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes53308 := (<-this.LoadMarkets())
		PanicOnError(retRes53308)
		if !IsTrue(GetValue(this.Has, "fetchBorrowRates")) {
			panic(NotSupported(Add(this.Id, " fetchCrossBorrowRate() is not supported yet")))
		}

		borrowRates := (<-this.FetchCrossBorrowRates(params))
		PanicOnError(borrowRates)
		var rate interface{} = this.SafeValue(borrowRates, code)
		if IsTrue(IsEqual(rate, nil)) {
			panic(ExchangeError(Add(Add(this.Id, " fetchCrossBorrowRate() could not find the borrow rate for currency code "), code)))
		}

		ch <- rate
		return nil

	}()
	return ch
}
func (this *Exchange) FetchIsolatedBorrowRate(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes53438 := (<-this.LoadMarkets())
		PanicOnError(retRes53438)
		if !IsTrue(GetValue(this.Has, "fetchBorrowRates")) {
			panic(NotSupported(Add(this.Id, " fetchIsolatedBorrowRate() is not supported yet")))
		}

		borrowRates := (<-this.FetchIsolatedBorrowRates(params))
		PanicOnError(borrowRates)
		var rate interface{} = this.SafeDict(borrowRates, symbol)
		if IsTrue(IsEqual(rate, nil)) {
			panic(ExchangeError(Add(Add(this.Id, " fetchIsolatedBorrowRate() could not find the borrow rate for market symbol "), symbol)))
		}

		ch <- rate
		return nil

	}()
	return ch
}
func (this *Exchange) HandleOptionAndParams(params interface{}, methodName interface{}, optionName interface{}, optionalArgs ...interface{}) interface{} {
	// This method can be used to obtain method specific properties, i.e: this.handleOptionAndParams (params, 'fetchPosition', 'marginMode', 'isolated')
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	var defaultOptionName interface{} = Add("default", this.Capitalize(optionName)) // we also need to check the 'defaultXyzWhatever'
	// check if params contain the key
	var value interface{} = this.SafeValue2(params, optionName, defaultOptionName)
	if IsTrue(!IsEqual(value, nil)) {
		params = this.Omit(params, []interface{}{optionName, defaultOptionName})
	} else {
		// handle routed methods like "watchTrades > watchTradesForSymbols" (or "watchTicker > watchTickers")
		methodNameparamsVariable := this.HandleParamString(params, "callerMethodName", methodName)
		methodName = GetValue(methodNameparamsVariable, 0)
		params = GetValue(methodNameparamsVariable, 1)
		// check if exchange has properties for this method
		var exchangeWideMethodOptions interface{} = this.SafeValue(this.Options, methodName)
		if IsTrue(!IsEqual(exchangeWideMethodOptions, nil)) {
			// check if the option is defined inside this method's props
			value = this.SafeValue2(exchangeWideMethodOptions, optionName, defaultOptionName)
		}
		if IsTrue(IsEqual(value, nil)) {
			// if it's still undefined, check if global exchange-wide option exists
			value = this.SafeValue2(this.Options, optionName, defaultOptionName)
		}
		// if it's still undefined, use the default value
		value = Ternary(IsTrue((!IsEqual(value, nil))), value, defaultValue)
	}
	return []interface{}{value, params}
}
func (this *Exchange) HandleOptionAndParams2(params interface{}, methodName1 interface{}, optionName1 interface{}, optionName2 interface{}, optionalArgs ...interface{}) interface{} {
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	var value interface{} = nil
	valueparamsVariable := this.HandleOptionAndParams(params, methodName1, optionName1)
	value = GetValue(valueparamsVariable, 0)
	params = GetValue(valueparamsVariable, 1)
	if IsTrue(!IsEqual(value, nil)) {
		// omit optionName2 too from params
		params = this.Omit(params, optionName2)
		return []interface{}{value, params}
	}
	// if still undefined, try optionName2
	var value2 interface{} = nil
	value2paramsVariable := this.HandleOptionAndParams(params, methodName1, optionName2, defaultValue)
	value2 = GetValue(value2paramsVariable, 0)
	params = GetValue(value2paramsVariable, 1)
	return []interface{}{value2, params}
}
func (this *Exchange) HandleOption(methodName interface{}, optionName interface{}, optionalArgs ...interface{}) interface{} {
	defaultValue := GetArg(optionalArgs, 0, nil)
	_ = defaultValue
	var res interface{} = this.HandleOptionAndParams(map[string]interface{}{}, methodName, optionName, defaultValue)
	return this.SafeValue(res, 0)
}
func (this *Exchange) HandleMarketTypeAndParams(methodName interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @name exchange#handleMarketTypeAndParams
	 * @param methodName the method calling handleMarketTypeAndParams
	 * @param {Market} market
	 * @param {object} params
	 * @param {string} [params.type] type assigned by user
	 * @param {string} [params.defaultType] same as params.type
	 * @param {string} [defaultValue] assigned programatically in the method calling handleMarketTypeAndParams
	 * @returns {[string, object]} the market type and params with type and defaultType omitted
	 */
	// type from param
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	params := GetArg(optionalArgs, 1, map[string]interface{}{})
	_ = params
	defaultValue := GetArg(optionalArgs, 2, nil)
	_ = defaultValue
	var typeVar interface{} = this.SafeString2(params, "defaultType", "type")
	if IsTrue(!IsEqual(typeVar, nil)) {
		params = this.Omit(params, []interface{}{"defaultType", "type"})
		return []interface{}{typeVar, params}
	}
	// type from market
	if IsTrue(!IsEqual(market, nil)) {
		return []interface{}{GetValue(market, "type"), params}
	}
	// type from default-argument
	if IsTrue(!IsEqual(defaultValue, nil)) {
		return []interface{}{defaultValue, params}
	}
	var methodOptions interface{} = this.SafeDict(this.Options, methodName)
	if IsTrue(!IsEqual(methodOptions, nil)) {
		if IsTrue(IsString(methodOptions)) {
			return []interface{}{methodOptions, params}
		} else {
			var typeFromMethod interface{} = this.SafeString2(methodOptions, "defaultType", "type")
			if IsTrue(!IsEqual(typeFromMethod, nil)) {
				return []interface{}{typeFromMethod, params}
			}
		}
	}
	var defaultType interface{} = this.SafeString2(this.Options, "defaultType", "type", "spot")
	return []interface{}{defaultType, params}
}
func (this *Exchange) HandleSubTypeAndParams(methodName interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	params := GetArg(optionalArgs, 1, map[string]interface{}{})
	_ = params
	defaultValue := GetArg(optionalArgs, 2, nil)
	_ = defaultValue
	var subType interface{} = nil
	// if set in params, it takes precedence
	var subTypeInParams interface{} = this.SafeString2(params, "subType", "defaultSubType")
	// avoid omitting if it's not present
	if IsTrue(!IsEqual(subTypeInParams, nil)) {
		subType = subTypeInParams
		params = this.Omit(params, []interface{}{"subType", "defaultSubType"})
	} else {
		// at first, check from market object
		if IsTrue(!IsEqual(market, nil)) {
			if IsTrue(GetValue(market, "linear")) {
				subType = "linear"
			} else if IsTrue(GetValue(market, "inverse")) {
				subType = "inverse"
			}
		}
		// if it was not defined in market object
		if IsTrue(IsEqual(subType, nil)) {
			var values interface{} = this.HandleOptionAndParams(map[string]interface{}{}, methodName, "subType", defaultValue) // no need to re-test params here
			subType = GetValue(values, 0)
		}
	}
	return []interface{}{subType, params}
}
func (this *Exchange) HandleMarginModeAndParams(methodName interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @param {object} [params] extra parameters specific to the exchange API endpoint
	 * @returns {Array} the marginMode in lowercase as specified by params["marginMode"], params["defaultMarginMode"] this.options["marginMode"] or this.options["defaultMarginMode"]
	 */
	params := GetArg(optionalArgs, 0, map[string]interface{}{})
	_ = params
	defaultValue := GetArg(optionalArgs, 1, nil)
	_ = defaultValue
	return this.HandleOptionAndParams(params, methodName, "marginMode", defaultValue)
}
func (this *Exchange) ThrowExactlyMatchedException(exact interface{}, string interface{}, message interface{}) {
	if IsTrue(IsEqual(string, nil)) {
		return
	}
	if IsTrue(InOp(exact, string)) {
		throwDynamicException(GetValue(exact, string), message)
	}
}
func (this *Exchange) ThrowBroadlyMatchedException(broad interface{}, string interface{}, message interface{}) {
	var broadKey interface{} = this.FindBroadlyMatchedKey(broad, string)
	if IsTrue(!IsEqual(broadKey, nil)) {
		throwDynamicException(GetValue(broad, broadKey), message)
	}
}
func (this *Exchange) FindBroadlyMatchedKey(broad interface{}, string interface{}) interface{} {
	// a helper for matching error strings exactly vs broadly
	var keys interface{} = ObjectKeys(broad)
	for i := 0; IsLessThan(i, GetArrayLength(keys)); i++ {
		var key interface{} = GetValue(keys, i)
		if IsTrue(!IsEqual(string, nil)) {
			if IsTrue(IsGreaterThanOrEqual(GetIndexOf(string, key), 0)) {
				return key
			}
		}
	}
	return nil
}
func (this *Exchange) HandleErrors(statusCode interface{}, statusText interface{}, url interface{}, method interface{}, responseHeaders interface{}, responseBody interface{}, response interface{}, requestHeaders interface{}, requestBody interface{}) interface{} {
	// it is a stub method that must be overrided in the derived exchange classes
	// throw new NotSupported (this.id + ' handleErrors() not implemented yet');
	return nil
}
func (this *Exchange) CalculateRateLimiterCost(api interface{}, method interface{}, path interface{}, params interface{}, optionalArgs ...interface{}) interface{} {
	config := GetArg(optionalArgs, 0, map[string]interface{}{})
	_ = config
	return this.SafeValue(config, "cost", 1)
}
func (this *Exchange) FetchTicker(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchTickers")) {

			retRes552012 := (<-this.LoadMarkets())
			PanicOnError(retRes552012)

			var market interface{} = this.DerivedExchange.Market(symbol)
			PanicOnError(market)
			symbol = GetValue(market, "symbol")

			tickers := <-this.DerivedExchange.FetchTickers([]interface{}{symbol}, params)
			PanicOnError(tickers)
			var ticker interface{} = this.SafeDict(tickers, symbol)
			if IsTrue(IsEqual(ticker, nil)) {
				panic(NullResponse(Add(Add(this.Id, " fetchTickers() could not find a ticker for "), symbol)))
			} else {

				ch <- ticker
				return nil
			}
		} else {
			panic(NotSupported(Add(this.Id, " fetchTicker() is not supported yet")))
		}

	}()
	return ch
}
func (this *Exchange) FetchMarkPrice(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchMarkPrices")) {

			retRes553712 := (<-this.LoadMarkets())
			PanicOnError(retRes553712)

			var market interface{} = this.DerivedExchange.Market(symbol)
			PanicOnError(market)
			symbol = GetValue(market, "symbol")

			tickers := (<-this.FetchMarkPrices([]interface{}{symbol}, params))
			PanicOnError(tickers)
			var ticker interface{} = this.SafeDict(tickers, symbol)
			if IsTrue(IsEqual(ticker, nil)) {
				panic(NullResponse(Add(Add(this.Id, " fetchMarkPrices() could not find a ticker for "), symbol)))
			} else {

				ch <- ticker
				return nil
			}
		} else {
			panic(NotSupported(Add(this.Id, " fetchMarkPrices() is not supported yet")))
		}

	}()
	return ch
}
func (this *Exchange) FetchTickerWs(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchTickersWs")) {

			retRes555412 := (<-this.LoadMarkets())
			PanicOnError(retRes555412)

			var market interface{} = this.DerivedExchange.Market(symbol)
			PanicOnError(market)
			symbol = GetValue(market, "symbol")

			tickers := (<-this.FetchTickersWs([]interface{}{symbol}, params))
			PanicOnError(tickers)
			var ticker interface{} = this.SafeDict(tickers, symbol)
			if IsTrue(IsEqual(ticker, nil)) {
				panic(NullResponse(Add(Add(this.Id, " fetchTickerWs() could not find a ticker for "), symbol)))
			} else {

				ch <- ticker
				return nil
			}
		} else {
			panic(NotSupported(Add(this.Id, " fetchTickerWs() is not supported yet")))
		}

	}()
	return ch
}
func (this *Exchange) WatchTicker(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchTicker() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchTickers(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchTickers() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchMarkPrices(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchMarkPrices() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchTickersWs(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchTickers() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchOrderBooks(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchOrderBooks() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchBidsAsks(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchBidsAsks() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchTickers(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchTickers() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) UnWatchTickers(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " unWatchTickers() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchOrder(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchOrder() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchOrderWs(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchOrderWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchOrderStatus(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		// TODO: TypeScript: change method signature by replacing
		// Promise<string> with Promise<Order['status']>.
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		order := <-this.DerivedExchange.FetchOrder(id, symbol, params)
		PanicOnError(order)

		ch <- GetValue(order, "status")
		return nil

	}()
	return ch
}
func (this *Exchange) FetchUnifiedOrder(order interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes561715 := <-this.DerivedExchange.FetchOrder(this.SafeString(order, "id"), this.SafeString(order, "symbol"), params)
		PanicOnError(retRes561715)
		ch <- retRes561715
		return nil

	}()
	return ch
}
func (this *Exchange) CreateOrder(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " createOrder() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateConvertTrade(id interface{}, fromCode interface{}, toCode interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		amount := GetArg(optionalArgs, 0, nil)
		_ = amount
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " createConvertTrade() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchConvertTrade(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchConvertTrade() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchConvertTradeHistory(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchConvertTradeHistory() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchPositionMode(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchPositionMode() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateTrailingAmountOrder(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createTrailingAmountOrder
		 * @description create a trailing order by providing the symbol, type, side, amount, price and trailingAmount
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {string} type 'market' or 'limit'
		 * @param {string} side 'buy' or 'sell'
		 * @param {float} amount how much you want to trade in units of the base currency, or number of contracts
		 * @param {float} [price] the price for the order to be filled at, in units of the quote currency, ignored in market orders
		 * @param {float} trailingAmount the quote amount to trail away from the current market price
		 * @param {float} [trailingTriggerPrice] the price to activate a trailing order, default uses the price argument
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		trailingAmount := GetArg(optionalArgs, 1, nil)
		_ = trailingAmount
		trailingTriggerPrice := GetArg(optionalArgs, 2, nil)
		_ = trailingTriggerPrice
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(trailingAmount, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " createTrailingAmountOrder() requires a trailingAmount argument")))
		}
		AddElementToObject(params, "trailingAmount", trailingAmount)
		if IsTrue(!IsEqual(trailingTriggerPrice, nil)) {
			AddElementToObject(params, "trailingTriggerPrice", trailingTriggerPrice)
		}
		if IsTrue(GetValue(this.Has, "createTrailingAmountOrder")) {

			retRes566319 := <-this.DerivedExchange.CreateOrder(symbol, typeVar, side, amount, price, params)
			PanicOnError(retRes566319)
			ch <- retRes566319
			return nil
		}
		panic(NotSupported(Add(this.Id, " createTrailingAmountOrder() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateTrailingAmountOrderWs(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createTrailingAmountOrderWs
		 * @description create a trailing order by providing the symbol, type, side, amount, price and trailingAmount
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {string} type 'market' or 'limit'
		 * @param {string} side 'buy' or 'sell'
		 * @param {float} amount how much you want to trade in units of the base currency, or number of contracts
		 * @param {float} [price] the price for the order to be filled at, in units of the quote currency, ignored in market orders
		 * @param {float} trailingAmount the quote amount to trail away from the current market price
		 * @param {float} [trailingTriggerPrice] the price to activate a trailing order, default uses the price argument
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		trailingAmount := GetArg(optionalArgs, 1, nil)
		_ = trailingAmount
		trailingTriggerPrice := GetArg(optionalArgs, 2, nil)
		_ = trailingTriggerPrice
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(trailingAmount, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " createTrailingAmountOrderWs() requires a trailingAmount argument")))
		}
		AddElementToObject(params, "trailingAmount", trailingAmount)
		if IsTrue(!IsEqual(trailingTriggerPrice, nil)) {
			AddElementToObject(params, "trailingTriggerPrice", trailingTriggerPrice)
		}
		if IsTrue(GetValue(this.Has, "createTrailingAmountOrderWs")) {

			retRes569119 := (<-this.CreateOrderWs(symbol, typeVar, side, amount, price, params))
			PanicOnError(retRes569119)
			ch <- retRes569119
			return nil
		}
		panic(NotSupported(Add(this.Id, " createTrailingAmountOrderWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateTrailingPercentOrder(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createTrailingPercentOrder
		 * @description create a trailing order by providing the symbol, type, side, amount, price and trailingPercent
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {string} type 'market' or 'limit'
		 * @param {string} side 'buy' or 'sell'
		 * @param {float} amount how much you want to trade in units of the base currency, or number of contracts
		 * @param {float} [price] the price for the order to be filled at, in units of the quote currency, ignored in market orders
		 * @param {float} trailingPercent the percent to trail away from the current market price
		 * @param {float} [trailingTriggerPrice] the price to activate a trailing order, default uses the price argument
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		trailingPercent := GetArg(optionalArgs, 1, nil)
		_ = trailingPercent
		trailingTriggerPrice := GetArg(optionalArgs, 2, nil)
		_ = trailingTriggerPrice
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(trailingPercent, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " createTrailingPercentOrder() requires a trailingPercent argument")))
		}
		AddElementToObject(params, "trailingPercent", trailingPercent)
		if IsTrue(!IsEqual(trailingTriggerPrice, nil)) {
			AddElementToObject(params, "trailingTriggerPrice", trailingTriggerPrice)
		}
		if IsTrue(GetValue(this.Has, "createTrailingPercentOrder")) {

			retRes571919 := <-this.DerivedExchange.CreateOrder(symbol, typeVar, side, amount, price, params)
			PanicOnError(retRes571919)
			ch <- retRes571919
			return nil
		}
		panic(NotSupported(Add(this.Id, " createTrailingPercentOrder() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateTrailingPercentOrderWs(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createTrailingPercentOrderWs
		 * @description create a trailing order by providing the symbol, type, side, amount, price and trailingPercent
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {string} type 'market' or 'limit'
		 * @param {string} side 'buy' or 'sell'
		 * @param {float} amount how much you want to trade in units of the base currency, or number of contracts
		 * @param {float} [price] the price for the order to be filled at, in units of the quote currency, ignored in market orders
		 * @param {float} trailingPercent the percent to trail away from the current market price
		 * @param {float} [trailingTriggerPrice] the price to activate a trailing order, default uses the price argument
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		trailingPercent := GetArg(optionalArgs, 1, nil)
		_ = trailingPercent
		trailingTriggerPrice := GetArg(optionalArgs, 2, nil)
		_ = trailingTriggerPrice
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(trailingPercent, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " createTrailingPercentOrderWs() requires a trailingPercent argument")))
		}
		AddElementToObject(params, "trailingPercent", trailingPercent)
		if IsTrue(!IsEqual(trailingTriggerPrice, nil)) {
			AddElementToObject(params, "trailingTriggerPrice", trailingTriggerPrice)
		}
		if IsTrue(GetValue(this.Has, "createTrailingPercentOrderWs")) {

			retRes574719 := (<-this.CreateOrderWs(symbol, typeVar, side, amount, price, params))
			PanicOnError(retRes574719)
			ch <- retRes574719
			return nil
		}
		panic(NotSupported(Add(this.Id, " createTrailingPercentOrderWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateMarketOrderWithCost(symbol interface{}, side interface{}, cost interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createMarketOrderWithCost
		 * @description create a market order by providing the symbol, side and cost
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {string} side 'buy' or 'sell'
		 * @param {float} cost how much you want to trade in units of the quote currency
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if IsTrue(IsTrue(GetValue(this.Has, "createMarketOrderWithCost")) || IsTrue((IsTrue(GetValue(this.Has, "createMarketBuyOrderWithCost")) && IsTrue(GetValue(this.Has, "createMarketSellOrderWithCost"))))) {

			retRes576419 := <-this.DerivedExchange.CreateOrder(symbol, "market", side, cost, 1, params)
			PanicOnError(retRes576419)
			ch <- retRes576419
			return nil
		}
		panic(NotSupported(Add(this.Id, " createMarketOrderWithCost() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateMarketBuyOrderWithCost(symbol interface{}, cost interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createMarketBuyOrderWithCost
		 * @description create a market buy order by providing the symbol and cost
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {float} cost how much you want to trade in units of the quote currency
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if IsTrue(IsTrue(GetValue(this.Options, "createMarketBuyOrderRequiresPrice")) || IsTrue(GetValue(this.Has, "createMarketBuyOrderWithCost"))) {

			retRes578019 := <-this.DerivedExchange.CreateOrder(symbol, "market", "buy", cost, 1, params)
			PanicOnError(retRes578019)
			ch <- retRes578019
			return nil
		}
		panic(NotSupported(Add(this.Id, " createMarketBuyOrderWithCost() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateMarketSellOrderWithCost(symbol interface{}, cost interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createMarketSellOrderWithCost
		 * @description create a market sell order by providing the symbol and cost
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {float} cost how much you want to trade in units of the quote currency
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if IsTrue(IsTrue(GetValue(this.Options, "createMarketSellOrderRequiresPrice")) || IsTrue(GetValue(this.Has, "createMarketSellOrderWithCost"))) {

			retRes579619 := <-this.DerivedExchange.CreateOrder(symbol, "market", "sell", cost, 1, params)
			PanicOnError(retRes579619)
			ch <- retRes579619
			return nil
		}
		panic(NotSupported(Add(this.Id, " createMarketSellOrderWithCost() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateMarketOrderWithCostWs(symbol interface{}, side interface{}, cost interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createMarketOrderWithCostWs
		 * @description create a market order by providing the symbol, side and cost
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {string} side 'buy' or 'sell'
		 * @param {float} cost how much you want to trade in units of the quote currency
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if IsTrue(IsTrue(GetValue(this.Has, "createMarketOrderWithCostWs")) || IsTrue((IsTrue(GetValue(this.Has, "createMarketBuyOrderWithCostWs")) && IsTrue(GetValue(this.Has, "createMarketSellOrderWithCostWs"))))) {

			retRes581319 := (<-this.CreateOrderWs(symbol, "market", side, cost, 1, params))
			PanicOnError(retRes581319)
			ch <- retRes581319
			return nil
		}
		panic(NotSupported(Add(this.Id, " createMarketOrderWithCostWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateTriggerOrder(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createTriggerOrder
		 * @description create a trigger stop order (type 1)
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {string} type 'market' or 'limit'
		 * @param {string} side 'buy' or 'sell'
		 * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
		 * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
		 * @param {float} triggerPrice the price to trigger the stop order, in units of the quote currency
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		triggerPrice := GetArg(optionalArgs, 1, nil)
		_ = triggerPrice
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(triggerPrice, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " createTriggerOrder() requires a triggerPrice argument")))
		}
		AddElementToObject(params, "triggerPrice", triggerPrice)
		if IsTrue(GetValue(this.Has, "createTriggerOrder")) {

			retRes583719 := <-this.DerivedExchange.CreateOrder(symbol, typeVar, side, amount, price, params)
			PanicOnError(retRes583719)
			ch <- retRes583719
			return nil
		}
		panic(NotSupported(Add(this.Id, " createTriggerOrder() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateTriggerOrderWs(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createTriggerOrderWs
		 * @description create a trigger stop order (type 1)
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {string} type 'market' or 'limit'
		 * @param {string} side 'buy' or 'sell'
		 * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
		 * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
		 * @param {float} triggerPrice the price to trigger the stop order, in units of the quote currency
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		triggerPrice := GetArg(optionalArgs, 1, nil)
		_ = triggerPrice
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(triggerPrice, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " createTriggerOrderWs() requires a triggerPrice argument")))
		}
		AddElementToObject(params, "triggerPrice", triggerPrice)
		if IsTrue(GetValue(this.Has, "createTriggerOrderWs")) {

			retRes586119 := (<-this.CreateOrderWs(symbol, typeVar, side, amount, price, params))
			PanicOnError(retRes586119)
			ch <- retRes586119
			return nil
		}
		panic(NotSupported(Add(this.Id, " createTriggerOrderWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateStopLossOrder(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createStopLossOrder
		 * @description create a trigger stop loss order (type 2)
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {string} type 'market' or 'limit'
		 * @param {string} side 'buy' or 'sell'
		 * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
		 * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
		 * @param {float} stopLossPrice the price to trigger the stop loss order, in units of the quote currency
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		stopLossPrice := GetArg(optionalArgs, 1, nil)
		_ = stopLossPrice
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(stopLossPrice, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " createStopLossOrder() requires a stopLossPrice argument")))
		}
		AddElementToObject(params, "stopLossPrice", stopLossPrice)
		if IsTrue(GetValue(this.Has, "createStopLossOrder")) {

			retRes588519 := <-this.DerivedExchange.CreateOrder(symbol, typeVar, side, amount, price, params)
			PanicOnError(retRes588519)
			ch <- retRes588519
			return nil
		}
		panic(NotSupported(Add(this.Id, " createStopLossOrder() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateStopLossOrderWs(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createStopLossOrderWs
		 * @description create a trigger stop loss order (type 2)
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {string} type 'market' or 'limit'
		 * @param {string} side 'buy' or 'sell'
		 * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
		 * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
		 * @param {float} stopLossPrice the price to trigger the stop loss order, in units of the quote currency
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		stopLossPrice := GetArg(optionalArgs, 1, nil)
		_ = stopLossPrice
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(stopLossPrice, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " createStopLossOrderWs() requires a stopLossPrice argument")))
		}
		AddElementToObject(params, "stopLossPrice", stopLossPrice)
		if IsTrue(GetValue(this.Has, "createStopLossOrderWs")) {

			retRes590919 := (<-this.CreateOrderWs(symbol, typeVar, side, amount, price, params))
			PanicOnError(retRes590919)
			ch <- retRes590919
			return nil
		}
		panic(NotSupported(Add(this.Id, " createStopLossOrderWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateTakeProfitOrder(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createTakeProfitOrder
		 * @description create a trigger take profit order (type 2)
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {string} type 'market' or 'limit'
		 * @param {string} side 'buy' or 'sell'
		 * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
		 * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
		 * @param {float} takeProfitPrice the price to trigger the take profit order, in units of the quote currency
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		takeProfitPrice := GetArg(optionalArgs, 1, nil)
		_ = takeProfitPrice
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(takeProfitPrice, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " createTakeProfitOrder() requires a takeProfitPrice argument")))
		}
		AddElementToObject(params, "takeProfitPrice", takeProfitPrice)
		if IsTrue(GetValue(this.Has, "createTakeProfitOrder")) {

			retRes593319 := <-this.DerivedExchange.CreateOrder(symbol, typeVar, side, amount, price, params)
			PanicOnError(retRes593319)
			ch <- retRes593319
			return nil
		}
		panic(NotSupported(Add(this.Id, " createTakeProfitOrder() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateTakeProfitOrderWs(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createTakeProfitOrderWs
		 * @description create a trigger take profit order (type 2)
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {string} type 'market' or 'limit'
		 * @param {string} side 'buy' or 'sell'
		 * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
		 * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
		 * @param {float} takeProfitPrice the price to trigger the take profit order, in units of the quote currency
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		takeProfitPrice := GetArg(optionalArgs, 1, nil)
		_ = takeProfitPrice
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(takeProfitPrice, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " createTakeProfitOrderWs() requires a takeProfitPrice argument")))
		}
		AddElementToObject(params, "takeProfitPrice", takeProfitPrice)
		if IsTrue(GetValue(this.Has, "createTakeProfitOrderWs")) {

			retRes595719 := (<-this.CreateOrderWs(symbol, typeVar, side, amount, price, params))
			PanicOnError(retRes595719)
			ch <- retRes595719
			return nil
		}
		panic(NotSupported(Add(this.Id, " createTakeProfitOrderWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateOrderWithTakeProfitAndStopLoss(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createOrderWithTakeProfitAndStopLoss
		 * @description create an order with a stop loss or take profit attached (type 3)
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {string} type 'market' or 'limit'
		 * @param {string} side 'buy' or 'sell'
		 * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
		 * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
		 * @param {float} [takeProfit] the take profit price, in units of the quote currency
		 * @param {float} [stopLoss] the stop loss price, in units of the quote currency
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @param {string} [params.takeProfitType] *not available on all exchanges* 'limit' or 'market'
		 * @param {string} [params.stopLossType] *not available on all exchanges* 'limit' or 'market'
		 * @param {string} [params.takeProfitPriceType] *not available on all exchanges* 'last', 'mark' or 'index'
		 * @param {string} [params.stopLossPriceType] *not available on all exchanges* 'last', 'mark' or 'index'
		 * @param {float} [params.takeProfitLimitPrice] *not available on all exchanges* limit price for a limit take profit order
		 * @param {float} [params.stopLossLimitPrice] *not available on all exchanges* stop loss for a limit stop loss order
		 * @param {float} [params.takeProfitAmount] *not available on all exchanges* the amount for a take profit
		 * @param {float} [params.stopLossAmount] *not available on all exchanges* the amount for a stop loss
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		takeProfit := GetArg(optionalArgs, 1, nil)
		_ = takeProfit
		stopLoss := GetArg(optionalArgs, 2, nil)
		_ = stopLoss
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		params = this.SetTakeProfitAndStopLossParams(symbol, typeVar, side, amount, price, takeProfit, stopLoss, params)
		if IsTrue(GetValue(this.Has, "createOrderWithTakeProfitAndStopLoss")) {

			retRes598719 := <-this.DerivedExchange.CreateOrder(symbol, typeVar, side, amount, price, params)
			PanicOnError(retRes598719)
			ch <- retRes598719
			return nil
		}
		panic(NotSupported(Add(this.Id, " createOrderWithTakeProfitAndStopLoss() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) SetTakeProfitAndStopLossParams(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) interface{} {
	price := GetArg(optionalArgs, 0, nil)
	_ = price
	takeProfit := GetArg(optionalArgs, 1, nil)
	_ = takeProfit
	stopLoss := GetArg(optionalArgs, 2, nil)
	_ = stopLoss
	params := GetArg(optionalArgs, 3, map[string]interface{}{})
	_ = params
	if IsTrue(IsTrue((IsEqual(takeProfit, nil))) && IsTrue((IsEqual(stopLoss, nil)))) {
		panic(ArgumentsRequired(Add(this.Id, " createOrderWithTakeProfitAndStopLoss() requires either a takeProfit or stopLoss argument")))
	}
	if IsTrue(!IsEqual(takeProfit, nil)) {
		AddElementToObject(params, "takeProfit", map[string]interface{}{
			"triggerPrice": takeProfit,
		})
	}
	if IsTrue(!IsEqual(stopLoss, nil)) {
		AddElementToObject(params, "stopLoss", map[string]interface{}{
			"triggerPrice": stopLoss,
		})
	}
	var takeProfitType interface{} = this.SafeString(params, "takeProfitType")
	var takeProfitPriceType interface{} = this.SafeString(params, "takeProfitPriceType")
	var takeProfitLimitPrice interface{} = this.SafeString(params, "takeProfitLimitPrice")
	var takeProfitAmount interface{} = this.SafeString(params, "takeProfitAmount")
	var stopLossType interface{} = this.SafeString(params, "stopLossType")
	var stopLossPriceType interface{} = this.SafeString(params, "stopLossPriceType")
	var stopLossLimitPrice interface{} = this.SafeString(params, "stopLossLimitPrice")
	var stopLossAmount interface{} = this.SafeString(params, "stopLossAmount")
	if IsTrue(!IsEqual(takeProfitType, nil)) {
		AddElementToObject(GetValue(params, "takeProfit"), "type", takeProfitType)
	}
	if IsTrue(!IsEqual(takeProfitPriceType, nil)) {
		AddElementToObject(GetValue(params, "takeProfit"), "priceType", takeProfitPriceType)
	}
	if IsTrue(!IsEqual(takeProfitLimitPrice, nil)) {
		AddElementToObject(GetValue(params, "takeProfit"), "price", this.ParseToNumeric(takeProfitLimitPrice))
	}
	if IsTrue(!IsEqual(takeProfitAmount, nil)) {
		AddElementToObject(GetValue(params, "takeProfit"), "amount", this.ParseToNumeric(takeProfitAmount))
	}
	if IsTrue(!IsEqual(stopLossType, nil)) {
		AddElementToObject(GetValue(params, "stopLoss"), "type", stopLossType)
	}
	if IsTrue(!IsEqual(stopLossPriceType, nil)) {
		AddElementToObject(GetValue(params, "stopLoss"), "priceType", stopLossPriceType)
	}
	if IsTrue(!IsEqual(stopLossLimitPrice, nil)) {
		AddElementToObject(GetValue(params, "stopLoss"), "price", this.ParseToNumeric(stopLossLimitPrice))
	}
	if IsTrue(!IsEqual(stopLossAmount, nil)) {
		AddElementToObject(GetValue(params, "stopLoss"), "amount", this.ParseToNumeric(stopLossAmount))
	}
	params = this.Omit(params, []interface{}{"takeProfitType", "takeProfitPriceType", "takeProfitLimitPrice", "takeProfitAmount", "stopLossType", "stopLossPriceType", "stopLossLimitPrice", "stopLossAmount"})
	return params
}
func (this *Exchange) CreateOrderWithTakeProfitAndStopLossWs(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name createOrderWithTakeProfitAndStopLossWs
		 * @description create an order with a stop loss or take profit attached (type 3)
		 * @param {string} symbol unified symbol of the market to create an order in
		 * @param {string} type 'market' or 'limit'
		 * @param {string} side 'buy' or 'sell'
		 * @param {float} amount how much you want to trade in units of the base currency or the number of contracts
		 * @param {float} [price] the price to fulfill the order, in units of the quote currency, ignored in market orders
		 * @param {float} [takeProfit] the take profit price, in units of the quote currency
		 * @param {float} [stopLoss] the stop loss price, in units of the quote currency
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @param {string} [params.takeProfitType] *not available on all exchanges* 'limit' or 'market'
		 * @param {string} [params.stopLossType] *not available on all exchanges* 'limit' or 'market'
		 * @param {string} [params.takeProfitPriceType] *not available on all exchanges* 'last', 'mark' or 'index'
		 * @param {string} [params.stopLossPriceType] *not available on all exchanges* 'last', 'mark' or 'index'
		 * @param {float} [params.takeProfitLimitPrice] *not available on all exchanges* limit price for a limit take profit order
		 * @param {float} [params.stopLossLimitPrice] *not available on all exchanges* stop loss for a limit stop loss order
		 * @param {float} [params.takeProfitAmount] *not available on all exchanges* the amount for a take profit
		 * @param {float} [params.stopLossAmount] *not available on all exchanges* the amount for a stop loss
		 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
		 */
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		takeProfit := GetArg(optionalArgs, 1, nil)
		_ = takeProfit
		stopLoss := GetArg(optionalArgs, 2, nil)
		_ = stopLoss
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		params = this.SetTakeProfitAndStopLossParams(symbol, typeVar, side, amount, price, takeProfit, stopLoss, params)
		if IsTrue(GetValue(this.Has, "createOrderWithTakeProfitAndStopLossWs")) {

			retRes606719 := (<-this.CreateOrderWs(symbol, typeVar, side, amount, price, params))
			PanicOnError(retRes606719)
			ch <- retRes606719
			return nil
		}
		panic(NotSupported(Add(this.Id, " createOrderWithTakeProfitAndStopLossWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateOrders(orders interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " createOrders() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) EditOrders(orders interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " editOrders() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CreateOrderWs(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " createOrderWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CancelOrder(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " cancelOrder() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CancelOrderWs(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " cancelOrderWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CancelOrdersWs(ids interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " cancelOrdersWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CancelAllOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " cancelAllOrders() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CancelAllOrdersAfter(timeout interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " cancelAllOrdersAfter() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CancelOrdersForSymbols(orders interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " cancelOrdersForSymbols() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CancelAllOrdersWs(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " cancelAllOrdersWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CancelUnifiedOrder(order interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		ch <- <-this.DerivedExchange.CancelOrder(this.SafeString(order, "id"), this.SafeString(order, "symbol"), params)
		return nil

	}()
	return ch
}
func (this *Exchange) FetchOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		if IsTrue(IsTrue(GetValue(this.Has, "fetchOpenOrders")) && IsTrue(GetValue(this.Has, "fetchClosedOrders"))) {
			panic(NotSupported(Add(this.Id, " fetchOrders() is not supported yet, consider using fetchOpenOrders() and fetchClosedOrders() instead")))
		}
		panic(NotSupported(Add(this.Id, " fetchOrders() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchOrdersWs(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchOrdersWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchOrderTrades(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchOrderTrades() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchOrders() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchOpenOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchOrders")) {

			orders := <-this.DerivedExchange.FetchOrders(symbol, since, limit, params)
			PanicOnError(orders)

			ch <- this.FilterBy(orders, "status", "open")
			return nil
		}
		panic(NotSupported(Add(this.Id, " fetchOpenOrders() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchOpenOrdersWs(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchOrdersWs")) {

			orders := (<-this.FetchOrdersWs(symbol, since, limit, params))
			PanicOnError(orders)

			ch <- this.FilterBy(orders, "status", "open")
			return nil
		}
		panic(NotSupported(Add(this.Id, " fetchOpenOrdersWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchClosedOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchOrders")) {

			orders := <-this.DerivedExchange.FetchOrders(symbol, since, limit, params)
			PanicOnError(orders)

			ch <- this.FilterBy(orders, "status", "closed")
			return nil
		}
		panic(NotSupported(Add(this.Id, " fetchClosedOrders() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchCanceledAndClosedOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchCanceledAndClosedOrders() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchClosedOrdersWs(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchOrdersWs")) {

			orders := (<-this.FetchOrdersWs(symbol, since, limit, params))
			PanicOnError(orders)

			ch <- this.FilterBy(orders, "status", "closed")
			return nil
		}
		panic(NotSupported(Add(this.Id, " fetchClosedOrdersWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchMyTrades(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchMyTrades() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchMyLiquidations(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchMyLiquidations() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchLiquidations(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchLiquidations() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchMyTradesWs(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchMyTradesWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) WatchMyTrades(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " watchMyTrades() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchGreeks(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchGreeks() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchAllGreeks(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchAllGreeks() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchOptionChain(code interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchOptionChain() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchOption(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchOption() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchConvertQuote(fromCode interface{}, toCode interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		amount := GetArg(optionalArgs, 0, nil)
		_ = amount
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchConvertQuote() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchDepositsWithdrawals(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchDepositsWithdrawals() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchDeposits(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchDeposits() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchWithdrawals(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchWithdrawals() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchDepositsWs(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchDepositsWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchWithdrawalsWs(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchWithdrawalsWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchFundingRateHistory(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchFundingRateHistory() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchFundingHistory(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchFundingHistory() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) ClosePosition(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		side := GetArg(optionalArgs, 0, nil)
		_ = side
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " closePosition() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CloseAllPositions(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " closeAllPositions() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchL3OrderBook(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		limit := GetArg(optionalArgs, 0, nil)
		_ = limit
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(BadRequest(Add(this.Id, " fetchL3OrderBook() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) ParseLastPrice(price interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseLastPrice() is not supported yet")))
}
func (this *Exchange) FetchDepositAddress(code interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchDepositAddresses")) {

			depositAddresses := (<-this.FetchDepositAddresses([]interface{}{code}, params))
			PanicOnError(depositAddresses)
			var depositAddress interface{} = this.SafeValue(depositAddresses, code)
			if IsTrue(IsEqual(depositAddress, nil)) {
				panic(InvalidAddress(Add(Add(Add(this.Id, " fetchDepositAddress() could not find a deposit address for "), code), ", make sure you have created a corresponding deposit address in your wallet on the exchange website")))
			} else {

				ch <- depositAddress
				return nil
			}
		} else if IsTrue(GetValue(this.Has, "fetchDepositAddressesByNetwork")) {
			var network interface{} = this.SafeString(params, "network")
			params = this.Omit(params, "network")

			addressStructures := (<-this.FetchDepositAddressesByNetwork(code, params))
			PanicOnError(addressStructures)
			if IsTrue(!IsEqual(network, nil)) {

				ch <- this.SafeDict(addressStructures, network)
				return nil
			} else {
				var keys interface{} = ObjectKeys(addressStructures)
				var key interface{} = this.SafeString(keys, 0)

				ch <- this.SafeDict(addressStructures, key)
				return nil
			}
		} else {
			panic(NotSupported(Add(this.Id, " fetchDepositAddress() is not supported yet")))
		}

	}()
	return ch
}
func (this *Exchange) Account() interface{} {
	return map[string]interface{}{
		"free":  nil,
		"used":  nil,
		"total": nil,
	}
}
func (this *Exchange) CommonCurrencyCode(code interface{}) interface{} {
	if !IsTrue(this.SubstituteCommonCurrencyCodes) {
		return code
	}
	return this.SafeString(this.CommonCurrencies, code, code)
}
func (this *Exchange) Currency(code interface{}) interface{} {
	if IsTrue(IsEqual(this.Currencies, nil)) {
		panic(ExchangeError(Add(this.Id, " currencies not loaded")))
	}
	if IsTrue(IsString(code)) {
		if IsTrue(InOp(this.Currencies, code)) {
			return GetValue(this.Currencies, code)
		} else if IsTrue(InOp(this.Currencies_by_id, code)) {
			return GetValue(this.Currencies_by_id, code)
		}
	}
	panic(ExchangeError(Add(Add(this.Id, " does not have currency code "), code)))
}
func (this *Exchange) Market(symbol interface{}) interface{} {
	if IsTrue(IsEqual(this.Markets, nil)) {
		panic(ExchangeError(Add(this.Id, " markets not loaded")))
	}
	if IsTrue(InOp(this.Markets, symbol)) {
		return GetValue(this.Markets, symbol)
	} else if IsTrue(InOp(this.Markets_by_id, symbol)) {
		var markets interface{} = GetValue(this.Markets_by_id, symbol)
		var defaultType interface{} = this.SafeString2(this.Options, "defaultType", "defaultSubType", "spot")
		for i := 0; IsLessThan(i, GetArrayLength(markets)); i++ {
			var market interface{} = GetValue(markets, i)
			if IsTrue(GetValue(market, defaultType)) {
				return market
			}
		}
		return GetValue(markets, 0)
	} else if IsTrue(IsTrue(IsTrue(IsTrue((EndsWith(symbol, "-C"))) || IsTrue((EndsWith(symbol, "-P")))) || IsTrue((StartsWith(symbol, "C-")))) || IsTrue((StartsWith(symbol, "P-")))) {
		return this.DerivedExchange.CreateExpiredOptionMarket(symbol)
	}
	panic(BadSymbol(Add(Add(this.Id, " does not have market symbol "), symbol)))
}
func (this *Exchange) CreateExpiredOptionMarket(symbol interface{}) interface{} {
	panic(NotSupported(Add(this.Id, " createExpiredOptionMarket () is not supported yet")))
}
func (this *Exchange) IsLeveragedCurrency(currencyCode interface{}, optionalArgs ...interface{}) interface{} {
	checkBaseCoin := GetArg(optionalArgs, 0, false)
	_ = checkBaseCoin
	existingCurrencies := GetArg(optionalArgs, 1, nil)
	_ = existingCurrencies
	var leverageSuffixes interface{} = []interface{}{"2L", "2S", "3L", "3S", "4L", "4S", "5L", "5S", "UP", "DOWN", "BULL", "BEAR"}
	for i := 0; IsLessThan(i, GetArrayLength(leverageSuffixes)); i++ {
		var leverageSuffix interface{} = GetValue(leverageSuffixes, i)
		if IsTrue(EndsWith(currencyCode, leverageSuffix)) {
			if !IsTrue(checkBaseCoin) {
				return true
			} else {
				// check if base currency is inside dict
				var baseCurrencyCode interface{} = Replace(currencyCode, leverageSuffix, "")
				if IsTrue(InOp(existingCurrencies, baseCurrencyCode)) {
					return true
				}
			}
		}
	}
	return false
}
func (this *Exchange) HandleWithdrawTagAndParams(tag interface{}, params interface{}) interface{} {
	if IsTrue(IsTrue((!IsEqual(tag, nil))) && IsTrue((IsObject(tag)))) {
		params = this.Extend(tag, params)
		tag = nil
	}
	if IsTrue(IsEqual(tag, nil)) {
		tag = this.SafeString(params, "tag")
		if IsTrue(!IsEqual(tag, nil)) {
			params = this.Omit(params, "tag")
		}
	}
	return []interface{}{tag, params}
}
func (this *Exchange) CreateLimitOrder(symbol interface{}, side interface{}, amount interface{}, price interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes638315 := <-this.DerivedExchange.CreateOrder(symbol, "limit", side, amount, price, params)
		PanicOnError(retRes638315)
		ch <- retRes638315
		return nil

	}()
	return ch
}
func (this *Exchange) CreateLimitOrderWs(symbol interface{}, side interface{}, amount interface{}, price interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes638715 := (<-this.CreateOrderWs(symbol, "limit", side, amount, price, params))
		PanicOnError(retRes638715)
		ch <- retRes638715
		return nil

	}()
	return ch
}
func (this *Exchange) CreateMarketOrder(symbol interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes639115 := <-this.DerivedExchange.CreateOrder(symbol, "market", side, amount, price, params)
		PanicOnError(retRes639115)
		ch <- retRes639115
		return nil

	}()
	return ch
}
func (this *Exchange) CreateMarketOrderWs(symbol interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes639515 := (<-this.CreateOrderWs(symbol, "market", side, amount, price, params))
		PanicOnError(retRes639515)
		ch <- retRes639515
		return nil

	}()
	return ch
}
func (this *Exchange) CreateLimitBuyOrder(symbol interface{}, amount interface{}, price interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes639915 := <-this.DerivedExchange.CreateOrder(symbol, "limit", "buy", amount, price, params)
		PanicOnError(retRes639915)
		ch <- retRes639915
		return nil

	}()
	return ch
}
func (this *Exchange) CreateLimitBuyOrderWs(symbol interface{}, amount interface{}, price interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes640315 := (<-this.CreateOrderWs(symbol, "limit", "buy", amount, price, params))
		PanicOnError(retRes640315)
		ch <- retRes640315
		return nil

	}()
	return ch
}
func (this *Exchange) CreateLimitSellOrder(symbol interface{}, amount interface{}, price interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes640715 := <-this.DerivedExchange.CreateOrder(symbol, "limit", "sell", amount, price, params)
		PanicOnError(retRes640715)
		ch <- retRes640715
		return nil

	}()
	return ch
}
func (this *Exchange) CreateLimitSellOrderWs(symbol interface{}, amount interface{}, price interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes641115 := (<-this.CreateOrderWs(symbol, "limit", "sell", amount, price, params))
		PanicOnError(retRes641115)
		ch <- retRes641115
		return nil

	}()
	return ch
}
func (this *Exchange) CreateMarketBuyOrder(symbol interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes641515 := <-this.DerivedExchange.CreateOrder(symbol, "market", "buy", amount, nil, params)
		PanicOnError(retRes641515)
		ch <- retRes641515
		return nil

	}()
	return ch
}
func (this *Exchange) CreateMarketBuyOrderWs(symbol interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes641915 := (<-this.CreateOrderWs(symbol, "market", "buy", amount, nil, params))
		PanicOnError(retRes641915)
		ch <- retRes641915
		return nil

	}()
	return ch
}
func (this *Exchange) CreateMarketSellOrder(symbol interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes642315 := <-this.DerivedExchange.CreateOrder(symbol, "market", "sell", amount, nil, params)
		PanicOnError(retRes642315)
		ch <- retRes642315
		return nil

	}()
	return ch
}
func (this *Exchange) CreateMarketSellOrderWs(symbol interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes642715 := (<-this.CreateOrderWs(symbol, "market", "sell", amount, nil, params))
		PanicOnError(retRes642715)
		ch <- retRes642715
		return nil

	}()
	return ch
}
func (this *Exchange) CostToPrecision(symbol interface{}, cost interface{}) interface{} {
	if IsTrue(IsEqual(cost, nil)) {
		return nil
	}

	var market interface{} = this.DerivedExchange.Market(symbol)
	PanicOnError(market)
	return this.DecimalToPrecision(cost, TRUNCATE, GetValue(GetValue(market, "precision"), "price"), this.PrecisionMode, this.PaddingMode)
}
func (this *Exchange) PriceToPrecision(symbol interface{}, price interface{}) interface{} {
	if IsTrue(IsEqual(price, nil)) {
		return nil
	}

	var market interface{} = this.DerivedExchange.Market(symbol)
	PanicOnError(market)
	var result interface{} = this.DecimalToPrecision(price, ROUND, GetValue(GetValue(market, "precision"), "price"), this.PrecisionMode, this.PaddingMode)
	if IsTrue(IsEqual(result, "0")) {
		panic(InvalidOrder(Add(Add(Add(Add(this.Id, " price of "), GetValue(market, "symbol")), " must be greater than minimum price precision of "), this.NumberToString(GetValue(GetValue(market, "precision"), "price")))))
	}
	return result
}
func (this *Exchange) AmountToPrecision(symbol interface{}, amount interface{}) interface{} {
	if IsTrue(IsEqual(amount, nil)) {
		return nil
	}

	var market interface{} = this.DerivedExchange.Market(symbol)
	PanicOnError(market)
	var result interface{} = this.DecimalToPrecision(amount, TRUNCATE, GetValue(GetValue(market, "precision"), "amount"), this.PrecisionMode, this.PaddingMode)
	if IsTrue(IsEqual(result, "0")) {
		panic(InvalidOrder(Add(Add(Add(Add(this.Id, " amount of "), GetValue(market, "symbol")), " must be greater than minimum amount precision of "), this.NumberToString(GetValue(GetValue(market, "precision"), "amount")))))
	}
	return result
}
func (this *Exchange) FeeToPrecision(symbol interface{}, fee interface{}) interface{} {
	if IsTrue(IsEqual(fee, nil)) {
		return nil
	}

	var market interface{} = this.DerivedExchange.Market(symbol)
	PanicOnError(market)
	return this.DecimalToPrecision(fee, ROUND, GetValue(GetValue(market, "precision"), "price"), this.PrecisionMode, this.PaddingMode)
}
func (this *Exchange) CurrencyToPrecision(code interface{}, fee interface{}, optionalArgs ...interface{}) interface{} {
	networkCode := GetArg(optionalArgs, 0, nil)
	_ = networkCode
	var currency interface{} = GetValue(this.Currencies, code)
	var precision interface{} = this.SafeValue(currency, "precision")
	if IsTrue(!IsEqual(networkCode, nil)) {
		var networks interface{} = this.SafeDict(currency, "networks", map[string]interface{}{})
		var networkItem interface{} = this.SafeDict(networks, networkCode, map[string]interface{}{})
		precision = this.SafeValue(networkItem, "precision", precision)
	}
	if IsTrue(IsEqual(precision, nil)) {
		return this.ForceString(fee)
	} else {
		var roundingMode interface{} = this.SafeInteger(this.Options, "currencyToPrecisionRoundingMode", ROUND)
		return this.DecimalToPrecision(fee, roundingMode, precision, this.PrecisionMode, this.PaddingMode)
	}
}
func (this *Exchange) ForceString(value interface{}) interface{} {
	if IsTrue(!IsString(value)) {
		return this.NumberToString(value)
	}
	return value
}
func (this *Exchange) IsTickPrecision() interface{} {
	return IsEqual(this.PrecisionMode, TICK_SIZE)
}
func (this *Exchange) IsDecimalPrecision() interface{} {
	return IsEqual(this.PrecisionMode, DECIMAL_PLACES)
}
func (this *Exchange) IsSignificantPrecision() interface{} {
	return IsEqual(this.PrecisionMode, SIGNIFICANT_DIGITS)
}
func (this *Exchange) SafeNumber(obj interface{}, key interface{}, optionalArgs ...interface{}) interface{} {
	defaultNumber := GetArg(optionalArgs, 0, nil)
	_ = defaultNumber
	var value interface{} = this.SafeString(obj, key)
	return this.ParseNumber(value, defaultNumber)
}
func (this *Exchange) SafeNumberN(obj interface{}, arr interface{}, optionalArgs ...interface{}) interface{} {
	defaultNumber := GetArg(optionalArgs, 0, nil)
	_ = defaultNumber
	var value interface{} = this.SafeStringN(obj, arr)
	return this.ParseNumber(value, defaultNumber)
}
func (this *Exchange) ParsePrecision(precision interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @param {string} precision The number of digits to the right of the decimal
	 * @returns {string} a string number equal to 1e-precision
	 */
	if IsTrue(IsEqual(precision, nil)) {
		return nil
	}
	var precisionNumber interface{} = ParseInt(precision)
	if IsTrue(IsEqual(precisionNumber, 0)) {
		return "1"
	}
	if IsTrue(IsGreaterThan(precisionNumber, 0)) {
		var parsedPrecision interface{} = "0."
		for i := 0; IsLessThan(i, Subtract(precisionNumber, 1)); i++ {
			parsedPrecision = Add(parsedPrecision, "0")
		}
		return Add(parsedPrecision, "1")
	} else {
		var parsedPrecision interface{} = "1"
		for i := 0; IsLessThan(i, Subtract(Multiply(precisionNumber, OpNeg(1)), 1)); i++ {
			parsedPrecision = Add(parsedPrecision, "0")
		}
		return Add(parsedPrecision, "0")
	}
}
func (this *Exchange) IntegerPrecisionToAmount(precision interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @description handles positive & negative numbers too. parsePrecision() does not handle negative numbers, but this method handles
	 * @param {string} precision The number of digits to the right of the decimal
	 * @returns {string} a string number equal to 1e-precision
	 */
	if IsTrue(IsEqual(precision, nil)) {
		return nil
	}
	if IsTrue(Precise.StringGe(precision, "0")) {
		return this.ParsePrecision(precision)
	} else {
		var positivePrecisionString interface{} = Precise.StringAbs(precision)
		var positivePrecision interface{} = ParseInt(positivePrecisionString)
		var parsedPrecision interface{} = "1"
		for i := 0; IsLessThan(i, Subtract(positivePrecision, 1)); i++ {
			parsedPrecision = Add(parsedPrecision, "0")
		}
		return Add(parsedPrecision, "0")
	}
}
func (this *Exchange) LoadTimeDifference(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		serverTime := <-this.DerivedExchange.FetchTime(params)
		PanicOnError(serverTime)
		var after interface{} = this.Milliseconds()
		AddElementToObject(this.Options, "timeDifference", Subtract(after, serverTime))

		ch <- GetValue(this.Options, "timeDifference")
		return nil

	}()
	return ch
}
func (this *Exchange) ImplodeHostname(url interface{}) interface{} {
	return this.ImplodeParams(url, map[string]interface{}{
		"hostname": this.Hostname,
	})
}
func (this *Exchange) FetchMarketLeverageTiers(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchLeverageTiers")) {

			var market interface{} = this.DerivedExchange.Market(symbol)
			PanicOnError(market)
			if !IsTrue(GetValue(market, "contract")) {
				panic(BadSymbol(Add(this.Id, " fetchMarketLeverageTiers() supports contract markets only")))
			}

			tiers := <-this.DerivedExchange.FetchLeverageTiers([]interface{}{symbol})
			PanicOnError(tiers)

			ch <- this.SafeValue(tiers, symbol)
			return nil
		} else {
			panic(NotSupported(Add(this.Id, " fetchMarketLeverageTiers() is not supported yet")))
		}

	}()
	return ch
}
func (this *Exchange) CreatePostOnlyOrder(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		if !IsTrue(GetValue(this.Has, "createPostOnlyOrder")) {
			panic(NotSupported(Add(this.Id, " createPostOnlyOrder() is not supported yet")))
		}
		var query interface{} = this.Extend(params, map[string]interface{}{
			"postOnly": true,
		})

		retRes659715 := <-this.DerivedExchange.CreateOrder(symbol, typeVar, side, amount, price, query)
		PanicOnError(retRes659715)
		ch <- retRes659715
		return nil

	}()
	return ch
}
func (this *Exchange) CreatePostOnlyOrderWs(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		if !IsTrue(GetValue(this.Has, "createPostOnlyOrderWs")) {
			panic(NotSupported(Add(this.Id, " createPostOnlyOrderWs() is not supported yet")))
		}
		var query interface{} = this.Extend(params, map[string]interface{}{
			"postOnly": true,
		})

		retRes660515 := (<-this.CreateOrderWs(symbol, typeVar, side, amount, price, query))
		PanicOnError(retRes660515)
		ch <- retRes660515
		return nil

	}()
	return ch
}
func (this *Exchange) CreateReduceOnlyOrder(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		if !IsTrue(GetValue(this.Has, "createReduceOnlyOrder")) {
			panic(NotSupported(Add(this.Id, " createReduceOnlyOrder() is not supported yet")))
		}
		var query interface{} = this.Extend(params, map[string]interface{}{
			"reduceOnly": true,
		})

		retRes661315 := <-this.DerivedExchange.CreateOrder(symbol, typeVar, side, amount, price, query)
		PanicOnError(retRes661315)
		ch <- retRes661315
		return nil

	}()
	return ch
}
func (this *Exchange) CreateReduceOnlyOrderWs(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		if !IsTrue(GetValue(this.Has, "createReduceOnlyOrderWs")) {
			panic(NotSupported(Add(this.Id, " createReduceOnlyOrderWs() is not supported yet")))
		}
		var query interface{} = this.Extend(params, map[string]interface{}{
			"reduceOnly": true,
		})

		retRes662115 := (<-this.CreateOrderWs(symbol, typeVar, side, amount, price, query))
		PanicOnError(retRes662115)
		ch <- retRes662115
		return nil

	}()
	return ch
}
func (this *Exchange) CreateStopOrder(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		triggerPrice := GetArg(optionalArgs, 1, nil)
		_ = triggerPrice
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		if !IsTrue(GetValue(this.Has, "createStopOrder")) {
			panic(NotSupported(Add(this.Id, " createStopOrder() is not supported yet")))
		}
		if IsTrue(IsEqual(triggerPrice, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " create_stop_order() requires a stopPrice argument")))
		}
		var query interface{} = this.Extend(params, map[string]interface{}{
			"stopPrice": triggerPrice,
		})

		retRes663215 := <-this.DerivedExchange.CreateOrder(symbol, typeVar, side, amount, price, query)
		PanicOnError(retRes663215)
		ch <- retRes663215
		return nil

	}()
	return ch
}
func (this *Exchange) CreateStopOrderWs(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		triggerPrice := GetArg(optionalArgs, 1, nil)
		_ = triggerPrice
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		if !IsTrue(GetValue(this.Has, "createStopOrderWs")) {
			panic(NotSupported(Add(this.Id, " createStopOrderWs() is not supported yet")))
		}
		if IsTrue(IsEqual(triggerPrice, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " createStopOrderWs() requires a stopPrice argument")))
		}
		var query interface{} = this.Extend(params, map[string]interface{}{
			"stopPrice": triggerPrice,
		})

		retRes664315 := (<-this.CreateOrderWs(symbol, typeVar, side, amount, price, query))
		PanicOnError(retRes664315)
		ch <- retRes664315
		return nil

	}()
	return ch
}
func (this *Exchange) CreateStopLimitOrder(symbol interface{}, side interface{}, amount interface{}, price interface{}, triggerPrice interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if !IsTrue(GetValue(this.Has, "createStopLimitOrder")) {
			panic(NotSupported(Add(this.Id, " createStopLimitOrder() is not supported yet")))
		}
		var query interface{} = this.Extend(params, map[string]interface{}{
			"stopPrice": triggerPrice,
		})

		retRes665115 := <-this.DerivedExchange.CreateOrder(symbol, "limit", side, amount, price, query)
		PanicOnError(retRes665115)
		ch <- retRes665115
		return nil

	}()
	return ch
}
func (this *Exchange) CreateStopLimitOrderWs(symbol interface{}, side interface{}, amount interface{}, price interface{}, triggerPrice interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if !IsTrue(GetValue(this.Has, "createStopLimitOrderWs")) {
			panic(NotSupported(Add(this.Id, " createStopLimitOrderWs() is not supported yet")))
		}
		var query interface{} = this.Extend(params, map[string]interface{}{
			"stopPrice": triggerPrice,
		})

		retRes665915 := (<-this.CreateOrderWs(symbol, "limit", side, amount, price, query))
		PanicOnError(retRes665915)
		ch <- retRes665915
		return nil

	}()
	return ch
}
func (this *Exchange) CreateStopMarketOrder(symbol interface{}, side interface{}, amount interface{}, triggerPrice interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if !IsTrue(GetValue(this.Has, "createStopMarketOrder")) {
			panic(NotSupported(Add(this.Id, " createStopMarketOrder() is not supported yet")))
		}
		var query interface{} = this.Extend(params, map[string]interface{}{
			"stopPrice": triggerPrice,
		})

		retRes666715 := <-this.DerivedExchange.CreateOrder(symbol, "market", side, amount, nil, query)
		PanicOnError(retRes666715)
		ch <- retRes666715
		return nil

	}()
	return ch
}
func (this *Exchange) CreateStopMarketOrderWs(symbol interface{}, side interface{}, amount interface{}, triggerPrice interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if !IsTrue(GetValue(this.Has, "createStopMarketOrderWs")) {
			panic(NotSupported(Add(this.Id, " createStopMarketOrderWs() is not supported yet")))
		}
		var query interface{} = this.Extend(params, map[string]interface{}{
			"stopPrice": triggerPrice,
		})

		retRes667515 := (<-this.CreateOrderWs(symbol, "market", side, amount, nil, query))
		PanicOnError(retRes667515)
		ch <- retRes667515
		return nil

	}()
	return ch
}
func (this *Exchange) SafeCurrencyCode(currencyId interface{}, optionalArgs ...interface{}) interface{} {
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	currency = this.SafeCurrency(currencyId, currency)
	return GetValue(currency, "code")
}
func (this *Exchange) FilterBySymbolSinceLimit(array interface{}, optionalArgs ...interface{}) interface{} {
	symbol := GetArg(optionalArgs, 0, nil)
	_ = symbol
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	tail := GetArg(optionalArgs, 3, false)
	_ = tail
	return this.FilterByValueSinceLimit(array, "symbol", symbol, since, limit, "timestamp", tail)
}
func (this *Exchange) FilterByCurrencySinceLimit(array interface{}, optionalArgs ...interface{}) interface{} {
	code := GetArg(optionalArgs, 0, nil)
	_ = code
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	tail := GetArg(optionalArgs, 3, false)
	_ = tail
	return this.FilterByValueSinceLimit(array, "currency", code, since, limit, "timestamp", tail)
}
func (this *Exchange) FilterBySymbolsSinceLimit(array interface{}, optionalArgs ...interface{}) interface{} {
	symbols := GetArg(optionalArgs, 0, nil)
	_ = symbols
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	tail := GetArg(optionalArgs, 3, false)
	_ = tail
	var result interface{} = this.FilterByArray(array, "symbol", symbols, false)
	return this.FilterBySinceLimit(result, since, limit, "timestamp", tail)
}
func (this *Exchange) ParseLastPrices(pricesData interface{}, optionalArgs ...interface{}) interface{} {
	//
	// the value of tickers is either a dict or a list
	//
	// dict
	//
	//     {
	//         'marketId1': { ... },
	//         'marketId2': { ... },
	//         ...
	//     }
	//
	// list
	//
	//     [
	//         { 'market': 'marketId1', ... },
	//         { 'market': 'marketId2', ... },
	//         ...
	//     ]
	//
	symbols := GetArg(optionalArgs, 0, nil)
	_ = symbols
	params := GetArg(optionalArgs, 1, map[string]interface{}{})
	_ = params
	var results interface{} = []interface{}{}
	if IsTrue(IsArray(pricesData)) {
		for i := 0; IsLessThan(i, GetArrayLength(pricesData)); i++ {
			var priceData interface{} = this.Extend(this.ParseLastPrice(GetValue(pricesData, i)), params)
			AppendToArray(&results, priceData)
		}
	} else {
		var marketIds interface{} = ObjectKeys(pricesData)
		for i := 0; IsLessThan(i, GetArrayLength(marketIds)); i++ {
			var marketId interface{} = GetValue(marketIds, i)

			var market interface{} = this.DerivedExchange.SafeMarket(marketId)
			PanicOnError(market)
			var priceData interface{} = this.Extend(this.ParseLastPrice(GetValue(pricesData, marketId), market), params)
			AppendToArray(&results, priceData)
		}
	}
	symbols = this.MarketSymbols(symbols)
	return this.FilterByArray(results, "symbol", symbols)
}
func (this *Exchange) ParseTickers(tickers interface{}, optionalArgs ...interface{}) interface{} {
	//
	// the value of tickers is either a dict or a list
	//
	//
	// dict
	//
	//     {
	//         'marketId1': { ... },
	//         'marketId2': { ... },
	//         'marketId3': { ... },
	//         ...
	//     }
	//
	// list
	//
	//     [
	//         { 'market': 'marketId1', ... },
	//         { 'market': 'marketId2', ... },
	//         { 'market': 'marketId3', ... },
	//         ...
	//     ]
	//
	symbols := GetArg(optionalArgs, 0, nil)
	_ = symbols
	params := GetArg(optionalArgs, 1, map[string]interface{}{})
	_ = params
	var results interface{} = []interface{}{}
	if IsTrue(IsArray(tickers)) {
		for i := 0; IsLessThan(i, GetArrayLength(tickers)); i++ {

			var parsedTicker interface{} = this.DerivedExchange.ParseTicker(GetValue(tickers, i))
			PanicOnError(parsedTicker)
			var ticker interface{} = this.Extend(parsedTicker, params)
			AppendToArray(&results, ticker)
		}
	} else {
		var marketIds interface{} = ObjectKeys(tickers)
		for i := 0; IsLessThan(i, GetArrayLength(marketIds)); i++ {
			var marketId interface{} = GetValue(marketIds, i)

			var market interface{} = this.DerivedExchange.SafeMarket(marketId)
			PanicOnError(market)

			var parsed interface{} = this.DerivedExchange.ParseTicker(GetValue(tickers, marketId), market)
			PanicOnError(parsed)
			var ticker interface{} = this.Extend(parsed, params)
			AppendToArray(&results, ticker)
		}
	}
	symbols = this.MarketSymbols(symbols)
	return this.FilterByArray(results, "symbol", symbols)
}
func (this *Exchange) ParseDepositAddresses(addresses interface{}, optionalArgs ...interface{}) interface{} {
	codes := GetArg(optionalArgs, 0, nil)
	_ = codes
	indexed := GetArg(optionalArgs, 1, true)
	_ = indexed
	params := GetArg(optionalArgs, 2, map[string]interface{}{})
	_ = params
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(addresses)); i++ {
		var address interface{} = this.Extend(this.DerivedExchange.ParseDepositAddress(GetValue(addresses, i)), params)
		AppendToArray(&result, address)
	}
	if IsTrue(!IsEqual(codes, nil)) {
		result = this.FilterByArray(result, "currency", codes, false)
	}
	if IsTrue(indexed) {
		result = this.FilterByArray(result, "currency", nil, indexed)
	}
	return result
}
func (this *Exchange) ParseBorrowInterests(response interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var interests interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(response)); i++ {
		var row interface{} = GetValue(response, i)
		AppendToArray(&interests, this.DerivedExchange.ParseBorrowInterest(row, market))
	}
	return interests
}
func (this *Exchange) ParseBorrowRate(info interface{}, optionalArgs ...interface{}) interface{} {
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	panic(NotSupported(Add(this.Id, " parseBorrowRate() is not supported yet")))
}
func (this *Exchange) ParseBorrowRateHistory(response interface{}, code interface{}, since interface{}, limit interface{}) interface{} {
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(response)); i++ {
		var item interface{} = GetValue(response, i)

		var borrowRate interface{} = this.DerivedExchange.ParseBorrowRate(item)
		PanicOnError(borrowRate)
		AppendToArray(&result, borrowRate)
	}
	var sorted interface{} = this.SortBy(result, "timestamp")
	return this.FilterByCurrencySinceLimit(sorted, code, since, limit)
}
func (this *Exchange) ParseIsolatedBorrowRates(info interface{}) interface{} {
	var result interface{} = map[string]interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(info)); i++ {
		var item interface{} = GetValue(info, i)
		var borrowRate interface{} = this.ParseIsolatedBorrowRate(item)
		var symbol interface{} = this.SafeString(borrowRate, "symbol")
		AddElementToObject(result, symbol, borrowRate)
	}
	return result
}
func (this *Exchange) ParseFundingRateHistories(response interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	var rates interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(response)); i++ {
		var entry interface{} = GetValue(response, i)
		AppendToArray(&rates, this.DerivedExchange.ParseFundingRateHistory(entry, market))
	}
	var sorted interface{} = this.SortBy(rates, "timestamp")
	var symbol interface{} = Ternary(IsTrue((IsEqual(market, nil))), nil, GetValue(market, "symbol"))
	return this.FilterBySymbolSinceLimit(sorted, symbol, since, limit)
}
func (this *Exchange) SafeSymbol(marketId interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	delimiter := GetArg(optionalArgs, 1, nil)
	_ = delimiter
	marketType := GetArg(optionalArgs, 2, nil)
	_ = marketType

	market = this.DerivedExchange.SafeMarket(marketId, market, delimiter, marketType)
	PanicOnError(market)
	return GetValue(market, "symbol")
}
func (this *Exchange) ParseFundingRate(contract interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseFundingRate() is not supported yet")))
}
func (this *Exchange) ParseFundingRates(response interface{}, optionalArgs ...interface{}) interface{} {
	symbols := GetArg(optionalArgs, 0, nil)
	_ = symbols
	var fundingRates interface{} = map[string]interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(response)); i++ {
		var entry interface{} = GetValue(response, i)

		var parsed interface{} = this.DerivedExchange.ParseFundingRate(entry)
		PanicOnError(parsed)
		AddElementToObject(fundingRates, GetValue(parsed, "symbol"), parsed)
	}
	return this.FilterByArray(fundingRates, "symbol", symbols)
}
func (this *Exchange) ParseLongShortRatio(info interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseLongShortRatio() is not supported yet")))
}
func (this *Exchange) ParseLongShortRatioHistory(response interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	var rates interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(response)); i++ {
		var entry interface{} = GetValue(response, i)
		AppendToArray(&rates, this.ParseLongShortRatio(entry, market))
	}
	var sorted interface{} = this.SortBy(rates, "timestamp")
	var symbol interface{} = Ternary(IsTrue((IsEqual(market, nil))), nil, GetValue(market, "symbol"))
	return this.FilterBySymbolSinceLimit(sorted, symbol, since, limit)
}
func (this *Exchange) HandleTriggerPricesAndParams(symbol interface{}, params interface{}, optionalArgs ...interface{}) interface{} {
	//
	omitParams := GetArg(optionalArgs, 0, true)
	_ = omitParams
	var triggerPrice interface{} = this.SafeString(params, "triggerPrice", "stopPrice")
	var triggerPriceStr interface{} = nil
	var stopLossPrice interface{} = this.SafeString(params, "stopLossPrice")
	var stopLossPriceStr interface{} = nil
	var takeProfitPrice interface{} = this.SafeString(params, "takeProfitPrice")
	var takeProfitPriceStr interface{} = nil
	//
	if IsTrue(!IsEqual(triggerPrice, nil)) {
		if IsTrue(omitParams) {
			params = this.Omit(params, []interface{}{"triggerPrice", "stopPrice"})
		}
		triggerPriceStr = this.PriceToPrecision(symbol, ParseFloat(triggerPrice))
	}
	if IsTrue(!IsEqual(stopLossPrice, nil)) {
		if IsTrue(omitParams) {
			params = this.Omit(params, "stopLossPrice")
		}
		stopLossPriceStr = this.PriceToPrecision(symbol, ParseFloat(stopLossPrice))
	}
	if IsTrue(!IsEqual(takeProfitPrice, nil)) {
		if IsTrue(omitParams) {
			params = this.Omit(params, "takeProfitPrice")
		}
		takeProfitPriceStr = this.PriceToPrecision(symbol, ParseFloat(takeProfitPrice))
	}
	return []interface{}{triggerPriceStr, stopLossPriceStr, takeProfitPriceStr, params}
}
func (this *Exchange) HandleTriggerDirectionAndParams(params interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @returns {[string, object]} the trigger-direction value and omited params
	 */
	exchangeSpecificKey := GetArg(optionalArgs, 0, nil)
	_ = exchangeSpecificKey
	allowEmpty := GetArg(optionalArgs, 1, false)
	_ = allowEmpty
	var triggerDirection interface{} = this.SafeString(params, "triggerDirection")
	var exchangeSpecificDefined interface{} = IsTrue((!IsEqual(exchangeSpecificKey, nil))) && IsTrue((InOp(params, exchangeSpecificKey)))
	if IsTrue(!IsEqual(triggerDirection, nil)) {
		params = this.Omit(params, "triggerDirection")
	}
	// throw exception if:
	// A) if provided value is not unified (support old "up/down" strings too)
	// B) if exchange specific "trigger direction key" (eg. "stopPriceSide") was not provided
	if IsTrue(IsTrue(!IsTrue(this.InArray(triggerDirection, []interface{}{"ascending", "descending", "up", "down", "above", "below"})) && !IsTrue(exchangeSpecificDefined)) && !IsTrue(allowEmpty)) {
		panic(ArgumentsRequired(Add(this.Id, " createOrder() : trigger orders require params[\"triggerDirection\"] to be either \"ascending\" or \"descending\"")))
	}
	// if old format was provided, overwrite to new
	if IsTrue(IsTrue(IsEqual(triggerDirection, "up")) || IsTrue(IsEqual(triggerDirection, "above"))) {
		triggerDirection = "ascending"
	} else if IsTrue(IsTrue(IsEqual(triggerDirection, "down")) || IsTrue(IsEqual(triggerDirection, "below"))) {
		triggerDirection = "descending"
	}
	return []interface{}{triggerDirection, params}
}
func (this *Exchange) HandleTriggerAndParams(params interface{}) interface{} {
	var isTrigger interface{} = this.SafeBool2(params, "trigger", "stop")
	if IsTrue(isTrigger) {
		params = this.Omit(params, []interface{}{"trigger", "stop"})
	}
	return []interface{}{isTrigger, params}
}
func (this *Exchange) IsTriggerOrder(params interface{}) interface{} {
	// for backwards compatibility
	return this.HandleTriggerAndParams(params)
}
func (this *Exchange) IsPostOnly(isMarketOrder interface{}, exchangeSpecificParam interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @param {string} type Order type
	 * @param {boolean} exchangeSpecificParam exchange specific postOnly
	 * @param {object} [params] exchange specific params
	 * @returns {boolean} true if a post only order, false otherwise
	 */
	params := GetArg(optionalArgs, 0, map[string]interface{}{})
	_ = params
	var timeInForce interface{} = this.SafeStringUpper(params, "timeInForce")
	var postOnly interface{} = this.SafeBool2(params, "postOnly", "post_only", false)
	// we assume timeInForce is uppercase from safeStringUpper (params, 'timeInForce')
	var ioc interface{} = IsEqual(timeInForce, "IOC")
	var fok interface{} = IsEqual(timeInForce, "FOK")
	var timeInForcePostOnly interface{} = IsEqual(timeInForce, "PO")
	postOnly = IsTrue(IsTrue(postOnly) || IsTrue(timeInForcePostOnly)) || IsTrue(exchangeSpecificParam)
	if IsTrue(postOnly) {
		if IsTrue(IsTrue(ioc) || IsTrue(fok)) {
			panic(InvalidOrder(Add(Add(this.Id, " postOnly orders cannot have timeInForce equal to "), timeInForce)))
		} else if IsTrue(isMarketOrder) {
			panic(InvalidOrder(Add(this.Id, " market orders cannot be postOnly")))
		} else {
			return true
		}
	} else {
		return false
	}
}
func (this *Exchange) HandlePostOnly(isMarketOrder interface{}, exchangeSpecificPostOnlyOption interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @param {string} type Order type
	 * @param {boolean} exchangeSpecificBoolean exchange specific postOnly
	 * @param {object} [params] exchange specific params
	 * @returns {Array}
	 */
	params := GetArg(optionalArgs, 0, map[string]interface{}{})
	_ = params
	var timeInForce interface{} = this.SafeStringUpper(params, "timeInForce")
	var postOnly interface{} = this.SafeBool(params, "postOnly", false)
	var ioc interface{} = IsEqual(timeInForce, "IOC")
	var fok interface{} = IsEqual(timeInForce, "FOK")
	var po interface{} = IsEqual(timeInForce, "PO")
	postOnly = IsTrue(IsTrue(postOnly) || IsTrue(po)) || IsTrue(exchangeSpecificPostOnlyOption)
	if IsTrue(postOnly) {
		if IsTrue(IsTrue(ioc) || IsTrue(fok)) {
			panic(InvalidOrder(Add(Add(this.Id, " postOnly orders cannot have timeInForce equal to "), timeInForce)))
		} else if IsTrue(isMarketOrder) {
			panic(InvalidOrder(Add(this.Id, " market orders cannot be postOnly")))
		} else {
			if IsTrue(po) {
				params = this.Omit(params, "timeInForce")
			}
			params = this.Omit(params, "postOnly")
			return []interface{}{true, params}
		}
	}
	return []interface{}{false, params}
}
func (this *Exchange) FetchLastPrices(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchLastPrices() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchTradingFees(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchTradingFees() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchTradingFeesWs(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchTradingFeesWs() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchTradingFee(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if !IsTrue(GetValue(this.Has, "fetchTradingFees")) {
			panic(NotSupported(Add(this.Id, " fetchTradingFee() is not supported yet")))
		}

		fees := <-this.DerivedExchange.FetchTradingFees(params)
		PanicOnError(fees)

		ch <- this.SafeDict(fees, symbol)
		return nil

	}()
	return ch
}
func (this *Exchange) FetchConvertCurrencies(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchConvertCurrencies() is not supported yet")))

	}()
	return ch
}
func (this *Exchange) ParseOpenInterest(interest interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseOpenInterest () is not supported yet")))
}
func (this *Exchange) ParseOpenInterests(response interface{}, optionalArgs ...interface{}) interface{} {
	symbols := GetArg(optionalArgs, 0, nil)
	_ = symbols
	var result interface{} = map[string]interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(response)); i++ {
		var entry interface{} = GetValue(response, i)

		var parsed interface{} = this.DerivedExchange.ParseOpenInterest(entry)
		PanicOnError(parsed)
		AddElementToObject(result, GetValue(parsed, "symbol"), parsed)
	}
	return this.FilterByArray(result, "symbol", symbols)
}
func (this *Exchange) ParseOpenInterestsHistory(response interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	var interests interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(response)); i++ {
		var entry interface{} = GetValue(response, i)

		var interest interface{} = this.DerivedExchange.ParseOpenInterest(entry, market)
		PanicOnError(interest)
		AppendToArray(&interests, interest)
	}
	var sorted interface{} = this.SortBy(interests, "timestamp")
	var symbol interface{} = this.SafeString(market, "symbol")
	return this.FilterBySymbolSinceLimit(sorted, symbol, since, limit)
}
func (this *Exchange) FetchFundingRate(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchFundingRates")) {

			retRes705512 := (<-this.LoadMarkets())
			PanicOnError(retRes705512)

			var market interface{} = this.DerivedExchange.Market(symbol)
			PanicOnError(market)
			symbol = GetValue(market, "symbol")
			if !IsTrue(GetValue(market, "contract")) {
				panic(BadSymbol(Add(this.Id, " fetchFundingRate() supports contract markets only")))
			}

			rates := <-this.DerivedExchange.FetchFundingRates([]interface{}{symbol}, params)
			PanicOnError(rates)
			var rate interface{} = this.SafeValue(rates, symbol)
			if IsTrue(IsEqual(rate, nil)) {
				panic(NullResponse(Add(Add(this.Id, " fetchFundingRate () returned no data for "), symbol)))
			} else {

				ch <- rate
				return nil
			}
		} else {
			panic(NotSupported(Add(this.Id, " fetchFundingRate () is not supported yet")))
		}

	}()
	return ch
}
func (this *Exchange) FetchFundingInterval(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchFundingIntervals")) {

			retRes707512 := (<-this.LoadMarkets())
			PanicOnError(retRes707512)

			var market interface{} = this.DerivedExchange.Market(symbol)
			PanicOnError(market)
			symbol = GetValue(market, "symbol")
			if !IsTrue(GetValue(market, "contract")) {
				panic(BadSymbol(Add(this.Id, " fetchFundingInterval() supports contract markets only")))
			}

			rates := <-this.DerivedExchange.FetchFundingIntervals([]interface{}{symbol}, params)
			PanicOnError(rates)
			var rate interface{} = this.SafeValue(rates, symbol)
			if IsTrue(IsEqual(rate, nil)) {
				panic(NullResponse(Add(Add(this.Id, " fetchFundingInterval() returned no data for "), symbol)))
			} else {

				ch <- rate
				return nil
			}
		} else {
			panic(NotSupported(Add(this.Id, " fetchFundingInterval() is not supported yet")))
		}

	}()
	return ch
}
func (this *Exchange) FetchMarkOHLCV(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name exchange#fetchMarkOHLCV
		 * @description fetches historical mark price candlestick data containing the open, high, low, and close price of a market
		 * @param {string} symbol unified symbol of the market to fetch OHLCV data for
		 * @param {string} timeframe the length of time each candle represents
		 * @param {int} [since] timestamp in ms of the earliest candle to fetch
		 * @param {int} [limit] the maximum amount of candles to fetch
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {float[][]} A list of candles ordered as timestamp, open, high, low, close, undefined
		 */
		timeframe := GetArg(optionalArgs, 0, "1m")
		_ = timeframe
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchMarkOHLCV")) {
			var request interface{} = map[string]interface{}{
				"price": "mark",
			}

			retRes710919 := <-this.DerivedExchange.FetchOHLCV(symbol, timeframe, since, limit, this.Extend(request, params))
			PanicOnError(retRes710919)
			ch <- retRes710919
			return nil
		} else {
			panic(NotSupported(Add(this.Id, " fetchMarkOHLCV () is not supported yet")))
		}

	}()
	return ch
}
func (this *Exchange) FetchIndexOHLCV(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name exchange#fetchIndexOHLCV
		 * @description fetches historical index price candlestick data containing the open, high, low, and close price of a market
		 * @param {string} symbol unified symbol of the market to fetch OHLCV data for
		 * @param {string} timeframe the length of time each candle represents
		 * @param {int} [since] timestamp in ms of the earliest candle to fetch
		 * @param {int} [limit] the maximum amount of candles to fetch
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {} A list of candles ordered as timestamp, open, high, low, close, undefined
		 */
		timeframe := GetArg(optionalArgs, 0, "1m")
		_ = timeframe
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchIndexOHLCV")) {
			var request interface{} = map[string]interface{}{
				"price": "index",
			}

			retRes713119 := <-this.DerivedExchange.FetchOHLCV(symbol, timeframe, since, limit, this.Extend(request, params))
			PanicOnError(retRes713119)
			ch <- retRes713119
			return nil
		} else {
			panic(NotSupported(Add(this.Id, " fetchIndexOHLCV () is not supported yet")))
		}

	}()
	return ch
}
func (this *Exchange) FetchPremiumIndexOHLCV(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name exchange#fetchPremiumIndexOHLCV
		 * @description fetches historical premium index price candlestick data containing the open, high, low, and close price of a market
		 * @param {string} symbol unified symbol of the market to fetch OHLCV data for
		 * @param {string} timeframe the length of time each candle represents
		 * @param {int} [since] timestamp in ms of the earliest candle to fetch
		 * @param {int} [limit] the maximum amount of candles to fetch
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {float[][]} A list of candles ordered as timestamp, open, high, low, close, undefined
		 */
		timeframe := GetArg(optionalArgs, 0, "1m")
		_ = timeframe
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchPremiumIndexOHLCV")) {
			var request interface{} = map[string]interface{}{
				"price": "premiumIndex",
			}

			retRes715319 := <-this.DerivedExchange.FetchOHLCV(symbol, timeframe, since, limit, this.Extend(request, params))
			PanicOnError(retRes715319)
			ch <- retRes715319
			return nil
		} else {
			panic(NotSupported(Add(this.Id, " fetchPremiumIndexOHLCV () is not supported yet")))
		}

	}()
	return ch
}
func (this *Exchange) HandleTimeInForce(optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * Must add timeInForce to this.options to use this method
	 * @returns {string} returns the exchange specific value for timeInForce
	 */
	params := GetArg(optionalArgs, 0, map[string]interface{}{})
	_ = params
	var timeInForce interface{} = this.SafeStringUpper(params, "timeInForce") // supported values GTC, IOC, PO
	if IsTrue(!IsEqual(timeInForce, nil)) {
		var exchangeValue interface{} = this.SafeString(GetValue(this.Options, "timeInForce"), timeInForce)
		if IsTrue(IsEqual(exchangeValue, nil)) {
			panic(ExchangeError(Add(Add(Add(this.Id, " does not support timeInForce \""), timeInForce), "\"")))
		}
		return exchangeValue
	}
	return nil
}
func (this *Exchange) ConvertTypeToAccount(account interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * Must add accountsByType to this.options to use this method
	 * @param {string} account key for account name in this.options['accountsByType']
	 * @returns the exchange specific account name or the isolated margin id for transfers
	 */
	var accountsByType interface{} = this.SafeDict(this.Options, "accountsByType", map[string]interface{}{})
	var lowercaseAccount interface{} = ToLower(account)
	if IsTrue(InOp(accountsByType, lowercaseAccount)) {
		return GetValue(accountsByType, lowercaseAccount)
	} else if IsTrue(IsTrue((InOp(this.Markets, account))) || IsTrue((InOp(this.Markets_by_id, account)))) {

		var market interface{} = this.DerivedExchange.Market(account)
		PanicOnError(market)
		return GetValue(market, "id")
	} else {
		return account
	}
}
func (this *Exchange) CheckRequiredArgument(methodName interface{}, argument interface{}, argumentName interface{}, optionalArgs ...interface{}) {
	/**
	 * @ignore
	 * @method
	 * @param {string} methodName the name of the method that the argument is being checked for
	 * @param {string} argument the argument's actual value provided
	 * @param {string} argumentName the name of the argument being checked (for logging purposes)
	 * @param {string[]} options a list of options that the argument can be
	 * @returns {undefined}
	 */
	options := GetArg(optionalArgs, 0, []interface{}{})
	_ = options
	var optionsLength interface{} = GetArrayLength(options)
	if IsTrue(IsTrue((IsEqual(argument, nil))) || IsTrue((IsTrue((IsGreaterThan(optionsLength, 0))) && IsTrue((!IsTrue((this.InArray(argument, options)))))))) {
		var messageOptions interface{} = Join(options, ", ")
		var message interface{} = Add(Add(Add(Add(Add(this.Id, " "), methodName), "() requires a "), argumentName), " argument")
		if IsTrue(!IsEqual(messageOptions, "")) {
			message = Add(message, Add(Add(Add(", one of ", "("), messageOptions), ")"))
		}
		panic(ArgumentsRequired(message))
	}
}
func (this *Exchange) CheckRequiredMarginArgument(methodName interface{}, symbol interface{}, marginMode interface{}) {
	/**
	 * @ignore
	 * @method
	 * @param {string} symbol unified symbol of the market
	 * @param {string} methodName name of the method that requires a symbol
	 * @param {string} marginMode is either 'isolated' or 'cross'
	 */
	if IsTrue(IsTrue((IsEqual(marginMode, "isolated"))) && IsTrue((IsEqual(symbol, nil)))) {
		panic(ArgumentsRequired(Add(Add(Add(this.Id, " "), methodName), "() requires a symbol argument for isolated margin")))
	} else if IsTrue(IsTrue((IsEqual(marginMode, "cross"))) && IsTrue((!IsEqual(symbol, nil)))) {
		panic(ArgumentsRequired(Add(Add(Add(this.Id, " "), methodName), "() cannot have a symbol argument for cross margin")))
	}
}
func (this *Exchange) ParseDepositWithdrawFees(response interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @param {object[]|object} response unparsed response from the exchange
	 * @param {string[]|undefined} codes the unified currency codes to fetch transactions fees for, returns all currencies when undefined
	 * @param {str} currencyIdKey *should only be undefined when response is a dictionary* the object key that corresponds to the currency id
	 * @returns {object} objects with withdraw and deposit fees, indexed by currency codes
	 */
	codes := GetArg(optionalArgs, 0, nil)
	_ = codes
	currencyIdKey := GetArg(optionalArgs, 1, nil)
	_ = currencyIdKey
	var depositWithdrawFees interface{} = map[string]interface{}{}
	var isArray interface{} = IsArray(response)
	var responseKeys interface{} = response
	if !IsTrue(isArray) {
		responseKeys = ObjectKeys(response)
	}
	for i := 0; IsLessThan(i, GetArrayLength(responseKeys)); i++ {
		var entry interface{} = GetValue(responseKeys, i)
		var dictionary interface{} = Ternary(IsTrue(isArray), entry, GetValue(response, entry))
		var currencyId interface{} = Ternary(IsTrue(isArray), this.SafeString(dictionary, currencyIdKey), entry)
		var currency interface{} = this.SafeCurrency(currencyId)
		var code interface{} = this.SafeString(currency, "code")
		if IsTrue(IsTrue((IsEqual(codes, nil))) || IsTrue((this.InArray(code, codes)))) {
			AddElementToObject(depositWithdrawFees, code, this.DerivedExchange.ParseDepositWithdrawFee(dictionary, currency))
		}
	}
	return depositWithdrawFees
}
func (this *Exchange) ParseDepositWithdrawFee(fee interface{}, optionalArgs ...interface{}) interface{} {
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	panic(NotSupported(Add(this.Id, " parseDepositWithdrawFee() is not supported yet")))
}
func (this *Exchange) DepositWithdrawFee(info interface{}) interface{} {
	return map[string]interface{}{
		"info": info,
		"withdraw": map[string]interface{}{
			"fee":        nil,
			"percentage": nil,
		},
		"deposit": map[string]interface{}{
			"fee":        nil,
			"percentage": nil,
		},
		"networks": map[string]interface{}{},
	}
}
func (this *Exchange) AssignDefaultDepositWithdrawFees(fee interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @description Takes a depositWithdrawFee structure and assigns the default values for withdraw and deposit
	 * @param {object} fee A deposit withdraw fee structure
	 * @param {object} currency A currency structure, the response from this.currency ()
	 * @returns {object} A deposit withdraw fee structure
	 */
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	var networkKeys interface{} = ObjectKeys(GetValue(fee, "networks"))
	var numNetworks interface{} = GetArrayLength(networkKeys)
	if IsTrue(IsEqual(numNetworks, 1)) {
		AddElementToObject(fee, "withdraw", GetValue(GetValue(GetValue(fee, "networks"), GetValue(networkKeys, 0)), "withdraw"))
		AddElementToObject(fee, "deposit", GetValue(GetValue(GetValue(fee, "networks"), GetValue(networkKeys, 0)), "deposit"))
		return fee
	}
	var currencyCode interface{} = this.SafeString(currency, "code")
	for i := 0; IsLessThan(i, numNetworks); i++ {
		var network interface{} = GetValue(networkKeys, i)
		if IsTrue(IsEqual(network, currencyCode)) {
			AddElementToObject(fee, "withdraw", GetValue(GetValue(GetValue(fee, "networks"), GetValue(networkKeys, i)), "withdraw"))
			AddElementToObject(fee, "deposit", GetValue(GetValue(GetValue(fee, "networks"), GetValue(networkKeys, i)), "deposit"))
		}
	}
	return fee
}
func (this *Exchange) ParseIncome(info interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseIncome () is not supported yet")))
}
func (this *Exchange) ParseIncomes(incomes interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @description parses funding fee info from exchange response
	 * @param {object[]} incomes each item describes once instance of currency being received or paid
	 * @param {object} market ccxt market
	 * @param {int} [since] when defined, the response items are filtered to only include items after this timestamp
	 * @param {int} [limit] limits the number of items in the response
	 * @returns {object[]} an array of [funding history structures]{@link https://docs.ccxt.com/#/?id=funding-history-structure}
	 */
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(incomes)); i++ {
		var entry interface{} = GetValue(incomes, i)

		var parsed interface{} = this.DerivedExchange.ParseIncome(entry, market)
		PanicOnError(parsed)
		AppendToArray(&result, parsed)
	}
	var sorted interface{} = this.SortBy(result, "timestamp")
	var symbol interface{} = this.SafeString(market, "symbol")
	return this.FilterBySymbolSinceLimit(sorted, symbol, since, limit)
}
func (this *Exchange) GetMarketFromSymbols(optionalArgs ...interface{}) interface{} {
	symbols := GetArg(optionalArgs, 0, nil)
	_ = symbols
	if IsTrue(IsEqual(symbols, nil)) {
		return nil
	}
	var firstMarket interface{} = this.SafeString(symbols, 0)

	var market interface{} = this.DerivedExchange.Market(firstMarket)
	PanicOnError(market)
	return market
}
func (this *Exchange) ParseWsOHLCVs(ohlcvs interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	timeframe := GetArg(optionalArgs, 1, "1m")
	_ = timeframe
	since := GetArg(optionalArgs, 2, nil)
	_ = since
	limit := GetArg(optionalArgs, 3, nil)
	_ = limit
	var results interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(ohlcvs)); i++ {
		AppendToArray(&results, this.ParseWsOHLCV(GetValue(ohlcvs, i), market))
	}
	return results
}
func (this *Exchange) FetchTransactions(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name exchange#fetchTransactions
		 * @deprecated
		 * @description *DEPRECATED* use fetchDepositsWithdrawals instead
		 * @param {string} code unified currency code for the currency of the deposit/withdrawals, default is undefined
		 * @param {int} [since] timestamp in ms of the earliest deposit/withdrawal, default is undefined
		 * @param {int} [limit] max number of deposit/withdrawals to return, default is undefined
		 * @param {object} [params] extra parameters specific to the exchange API endpoint
		 * @returns {object} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
		 */
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchDepositsWithdrawals")) {

			retRes736319 := <-this.DerivedExchange.FetchDepositsWithdrawals(code, since, limit, params)
			PanicOnError(retRes736319)
			ch <- retRes736319
			return nil
		} else {
			panic(NotSupported(Add(this.Id, " fetchTransactions () is not supported yet")))
		}

	}()
	return ch
}
func (this *Exchange) FilterByArrayPositions(objects interface{}, key interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @description Typed wrapper for filterByArray that returns a list of positions
	 */
	values := GetArg(optionalArgs, 0, nil)
	_ = values
	indexed := GetArg(optionalArgs, 1, true)
	_ = indexed
	return this.FilterByArray(objects, key, values, indexed)
}
func (this *Exchange) FilterByArrayTickers(objects interface{}, key interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @description Typed wrapper for filterByArray that returns a dictionary of tickers
	 */
	values := GetArg(optionalArgs, 0, nil)
	_ = values
	indexed := GetArg(optionalArgs, 1, true)
	_ = indexed
	return this.FilterByArray(objects, key, values, indexed)
}
func (this *Exchange) CreateOHLCVObject(symbol interface{}, timeframe interface{}, data interface{}) interface{} {
	var res interface{} = map[string]interface{}{}
	AddElementToObject(res, symbol, map[string]interface{}{})
	AddElementToObject(GetValue(res, symbol), timeframe, data)
	return res
}
func (this *Exchange) HandleMaxEntriesPerRequestAndParams(method interface{}, optionalArgs ...interface{}) interface{} {
	maxEntriesPerRequest := GetArg(optionalArgs, 0, nil)
	_ = maxEntriesPerRequest
	params := GetArg(optionalArgs, 1, map[string]interface{}{})
	_ = params
	var newMaxEntriesPerRequest interface{} = nil
	newMaxEntriesPerRequestparamsVariable := this.HandleOptionAndParams(params, method, "maxEntriesPerRequest")
	newMaxEntriesPerRequest = GetValue(newMaxEntriesPerRequestparamsVariable, 0)
	params = GetValue(newMaxEntriesPerRequestparamsVariable, 1)
	if IsTrue(IsTrue((!IsEqual(newMaxEntriesPerRequest, nil))) && IsTrue((!IsEqual(newMaxEntriesPerRequest, maxEntriesPerRequest)))) {
		maxEntriesPerRequest = newMaxEntriesPerRequest
	}
	if IsTrue(IsEqual(maxEntriesPerRequest, nil)) {
		maxEntriesPerRequest = 1000 // default to 1000
	}
	return []interface{}{maxEntriesPerRequest, params}
}
func (this *Exchange) FetchPaginatedCallDynamic(method interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		maxEntriesPerRequest := GetArg(optionalArgs, 4, nil)
		_ = maxEntriesPerRequest
		removeRepeated := GetArg(optionalArgs, 5, true)
		_ = removeRepeated
		var maxCalls interface{} = nil
		maxCallsparamsVariable := this.HandleOptionAndParams(params, method, "paginationCalls", 10)
		maxCalls = GetValue(maxCallsparamsVariable, 0)
		params = GetValue(maxCallsparamsVariable, 1)
		var maxRetries interface{} = nil
		maxRetriesparamsVariable := this.HandleOptionAndParams(params, method, "maxRetries", 3)
		maxRetries = GetValue(maxRetriesparamsVariable, 0)
		params = GetValue(maxRetriesparamsVariable, 1)
		var paginationDirection interface{} = nil
		paginationDirectionparamsVariable := this.HandleOptionAndParams(params, method, "paginationDirection", "backward")
		paginationDirection = GetValue(paginationDirectionparamsVariable, 0)
		params = GetValue(paginationDirectionparamsVariable, 1)
		var paginationTimestamp interface{} = nil
		var removeRepeatedOption interface{} = removeRepeated
		removeRepeatedOptionparamsVariable := this.HandleOptionAndParams(params, method, "removeRepeated", removeRepeated)
		removeRepeatedOption = GetValue(removeRepeatedOptionparamsVariable, 0)
		params = GetValue(removeRepeatedOptionparamsVariable, 1)
		var calls interface{} = 0
		var result interface{} = []interface{}{}
		var errors interface{} = 0
		var until interface{} = this.SafeIntegerN(params, []interface{}{"until", "untill", "till"}) // do not omit it from params here
		maxEntriesPerRequestparamsVariable := this.HandleMaxEntriesPerRequestAndParams(method, maxEntriesPerRequest, params)
		maxEntriesPerRequest = GetValue(maxEntriesPerRequestparamsVariable, 0)
		params = GetValue(maxEntriesPerRequestparamsVariable, 1)
		if IsTrue((IsEqual(paginationDirection, "forward"))) {
			if IsTrue(IsEqual(since, nil)) {
				panic(ArgumentsRequired(Add(this.Id, " pagination requires a since argument when paginationDirection set to forward")))
			}
			paginationTimestamp = since
		}
		for IsLessThan(calls, maxCalls) {
			calls = Add(calls, 1)

			{
				func(this *Exchange) (ret_ interface{}) {
					defer func() {
						if e := recover(); e != nil {
							if e == "break" {
								return
							}
							ret_ = func(this *Exchange) interface{} {
								// catch block:
								errors = Add(errors, 1)
								if IsTrue(IsGreaterThan(errors, maxRetries)) {
									panic(e)
								}
								return nil
							}(this)
						}
					}()
					// try block:
					if IsTrue(IsEqual(paginationDirection, "backward")) {
						// do it backwards, starting from the last
						// UNTIL filtering is required in order to work
						if IsTrue(!IsEqual(paginationTimestamp, nil)) {
							AddElementToObject(params, "until", Subtract(paginationTimestamp, 1))
						}

						response := (<-this.callDynamically(method, symbol, nil, maxEntriesPerRequest, params))
						PanicOnError(response)
						var responseLength interface{} = GetArrayLength(response)
						if IsTrue(this.Verbose) {
							var backwardMessage interface{} = Add(Add(Add(Add(Add("Dynamic pagination call ", this.NumberToString(calls)), " method "), method), " response length "), this.NumberToString(responseLength))
							if IsTrue(!IsEqual(paginationTimestamp, nil)) {
								backwardMessage = Add(backwardMessage, Add(" timestamp ", this.NumberToString(paginationTimestamp)))
							}
							this.Log(backwardMessage)
						}
						if IsTrue(IsEqual(responseLength, 0)) {
							panic("break")
						}
						errors = 0
						result = this.ArrayConcat(result, response)
						var firstElement interface{} = this.SafeValue(response, 0)
						paginationTimestamp = this.SafeInteger2(firstElement, "timestamp", 0)
						if IsTrue(IsTrue((!IsEqual(since, nil))) && IsTrue((IsLessThanOrEqual(paginationTimestamp, since)))) {
							panic("break")
						}
					} else {
						// do it forwards, starting from the since

						response := (<-this.callDynamically(method, symbol, paginationTimestamp, maxEntriesPerRequest, params))
						PanicOnError(response)
						var responseLength interface{} = GetArrayLength(response)
						if IsTrue(this.Verbose) {
							var forwardMessage interface{} = Add(Add(Add(Add(Add("Dynamic pagination call ", this.NumberToString(calls)), " method "), method), " response length "), this.NumberToString(responseLength))
							if IsTrue(!IsEqual(paginationTimestamp, nil)) {
								forwardMessage = Add(forwardMessage, Add(" timestamp ", this.NumberToString(paginationTimestamp)))
							}
							this.Log(forwardMessage)
						}
						if IsTrue(IsEqual(responseLength, 0)) {
							panic("break")
						}
						errors = 0
						result = this.ArrayConcat(result, response)
						var last interface{} = this.SafeValue(response, Subtract(responseLength, 1))
						paginationTimestamp = Add(this.SafeInteger(last, "timestamp"), 1)
						if IsTrue(IsTrue((!IsEqual(until, nil))) && IsTrue((IsGreaterThanOrEqual(paginationTimestamp, until)))) {
							panic("break")
						}
					}
					return nil
				}(this)

			}
		}
		var uniqueResults interface{} = result
		if IsTrue(removeRepeatedOption) {
			uniqueResults = this.RemoveRepeatedElementsFromArray(result)
		}
		var key interface{} = Ternary(IsTrue((IsEqual(method, "fetchOHLCV"))), 0, "timestamp")

		ch <- this.FilterBySinceLimit(uniqueResults, since, limit, key)
		return nil

	}()
	return ch
}
func (this *Exchange) SafeDeterministicCall(method interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		timeframe := GetArg(optionalArgs, 3, nil)
		_ = timeframe
		params := GetArg(optionalArgs, 4, map[string]interface{}{})
		_ = params
		var maxRetries interface{} = nil
		maxRetriesparamsVariable := this.HandleOptionAndParams(params, method, "maxRetries", 3)
		maxRetries = GetValue(maxRetriesparamsVariable, 0)
		params = GetValue(maxRetriesparamsVariable, 1)
		var errors interface{} = 0
		for IsLessThanOrEqual(errors, maxRetries) {

			{
				func(this *Exchange) (ret_ interface{}) {
					defer func() {
						if e := recover(); e != nil {
							if e == "break" {
								return
							}
							ret_ = func(this *Exchange) interface{} {
								// catch block:
								if IsTrue(IsInstance(e, RateLimitExceeded)) {
									panic(e)
								}
								errors = Add(errors, 1)
								if IsTrue(IsGreaterThan(errors, maxRetries)) {
									panic(e)
								}
								return nil
							}(this)
						}
					}()
					// try block:
					if IsTrue(IsTrue(timeframe) && IsTrue(!IsEqual(method, "fetchFundingRateHistory"))) {

						retRes749927 := (<-this.callDynamically(method, symbol, timeframe, since, limit, params))
						PanicOnError(retRes749927)
						ch <- retRes749927
						return nil
					} else {

						retRes750127 := (<-this.callDynamically(method, symbol, since, limit, params))
						PanicOnError(retRes750127)
						ch <- retRes750127
						return nil
					}

				}(this)

			}
		}

		ch <- []interface{}{}
		return nil

	}()
	return ch
}
func (this *Exchange) FetchPaginatedCallDeterministic(method interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		timeframe := GetArg(optionalArgs, 3, nil)
		_ = timeframe
		params := GetArg(optionalArgs, 4, map[string]interface{}{})
		_ = params
		maxEntriesPerRequest := GetArg(optionalArgs, 5, nil)
		_ = maxEntriesPerRequest
		var maxCalls interface{} = nil
		maxCallsparamsVariable := this.HandleOptionAndParams(params, method, "paginationCalls", 10)
		maxCalls = GetValue(maxCallsparamsVariable, 0)
		params = GetValue(maxCallsparamsVariable, 1)
		maxEntriesPerRequestparamsVariable := this.HandleMaxEntriesPerRequestAndParams(method, maxEntriesPerRequest, params)
		maxEntriesPerRequest = GetValue(maxEntriesPerRequestparamsVariable, 0)
		params = GetValue(maxEntriesPerRequestparamsVariable, 1)
		var current interface{} = this.Milliseconds()
		var tasks interface{} = []interface{}{}
		var time interface{} = Multiply(this.ParseTimeframe(timeframe), 1000)
		var step interface{} = Multiply(time, maxEntriesPerRequest)
		var currentSince interface{} = Subtract(Subtract(current, (Multiply(maxCalls, step))), 1)
		if IsTrue(!IsEqual(since, nil)) {
			currentSince = mathMax(currentSince, since)
		} else {
			currentSince = mathMax(currentSince, 1241440531000) // avoid timestamps older than 2009
		}
		var until interface{} = this.SafeInteger2(params, "until", "till") // do not omit it here
		if IsTrue(!IsEqual(until, nil)) {
			var requiredCalls interface{} = MathCeil(Divide((Subtract(until, since)), step))
			if IsTrue(IsGreaterThan(requiredCalls, maxCalls)) {
				panic(BadRequest(Add(Add(Add(Add(this.Id, " the number of required calls is greater than the max number of calls allowed, either increase the paginationCalls or decrease the since-until gap. Current paginationCalls limit is "), ToString(maxCalls)), " required calls is "), ToString(requiredCalls))))
			}
		}
		for i := 0; IsLessThan(i, maxCalls); i++ {
			if IsTrue(IsTrue((!IsEqual(until, nil))) && IsTrue((IsGreaterThanOrEqual(currentSince, until)))) {
				break
			}
			if IsTrue(IsGreaterThanOrEqual(currentSince, current)) {
				break
			}
			AppendToArray(&tasks, this.SafeDeterministicCall(method, symbol, currentSince, maxEntriesPerRequest, timeframe, params))
			currentSince = Subtract(this.Sum(currentSince, step), 1)
		}

		results := (<-promiseAll(tasks))
		PanicOnError(results)
		var result interface{} = []interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(results)); i++ {
			result = this.ArrayConcat(result, GetValue(results, i))
		}
		var uniqueResults interface{} = this.RemoveRepeatedElementsFromArray(result)
		var key interface{} = Ternary(IsTrue((IsEqual(method, "fetchOHLCV"))), 0, "timestamp")

		ch <- this.FilterBySinceLimit(uniqueResults, since, limit, key)
		return nil

	}()
	return ch
}
func (this *Exchange) FetchPaginatedCallCursor(method interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		cursorReceived := GetArg(optionalArgs, 4, nil)
		_ = cursorReceived
		cursorSent := GetArg(optionalArgs, 5, nil)
		_ = cursorSent
		cursorIncrement := GetArg(optionalArgs, 6, nil)
		_ = cursorIncrement
		maxEntriesPerRequest := GetArg(optionalArgs, 7, nil)
		_ = maxEntriesPerRequest
		var maxCalls interface{} = nil
		maxCallsparamsVariable := this.HandleOptionAndParams(params, method, "paginationCalls", 10)
		maxCalls = GetValue(maxCallsparamsVariable, 0)
		params = GetValue(maxCallsparamsVariable, 1)
		var maxRetries interface{} = nil
		maxRetriesparamsVariable := this.HandleOptionAndParams(params, method, "maxRetries", 3)
		maxRetries = GetValue(maxRetriesparamsVariable, 0)
		params = GetValue(maxRetriesparamsVariable, 1)
		maxEntriesPerRequestparamsVariable := this.HandleMaxEntriesPerRequestAndParams(method, maxEntriesPerRequest, params)
		maxEntriesPerRequest = GetValue(maxEntriesPerRequestparamsVariable, 0)
		params = GetValue(maxEntriesPerRequestparamsVariable, 1)
		var cursorValue interface{} = nil
		var i interface{} = 0
		var errors interface{} = 0
		var result interface{} = []interface{}{}
		var timeframe interface{} = this.SafeString(params, "timeframe")
		params = this.Omit(params, "timeframe") // reading the timeframe from the method arguments to avoid changing the signature
		for IsLessThan(i, maxCalls) {

			{
				func(this *Exchange) (ret_ interface{}) {
					defer func() {
						if e := recover(); e != nil {
							if e == "break" {
								return
							}
							ret_ = func(this *Exchange) interface{} {
								// catch block:
								errors = Add(errors, 1)
								if IsTrue(IsGreaterThan(errors, maxRetries)) {
									panic(e)
								}
								return nil
							}(this)
						}
					}()
					// try block:
					if IsTrue(!IsEqual(cursorValue, nil)) {
						if IsTrue(!IsEqual(cursorIncrement, nil)) {
							cursorValue = Add(this.ParseToInt(cursorValue), cursorIncrement)
						}
						AddElementToObject(params, cursorSent, cursorValue)
					}
					var response interface{} = nil
					if IsTrue(IsEqual(method, "fetchAccounts")) {

						response = (<-this.callDynamically(method, params))
						PanicOnError(response)
					} else if IsTrue(IsTrue(IsEqual(method, "getLeverageTiersPaginated")) || IsTrue(IsEqual(method, "fetchPositions"))) {

						response = (<-this.callDynamically(method, symbol, params))
						PanicOnError(response)
					} else if IsTrue(IsEqual(method, "fetchOpenInterestHistory")) {

						response = (<-this.callDynamically(method, symbol, timeframe, since, maxEntriesPerRequest, params))
						PanicOnError(response)
					} else {

						response = (<-this.callDynamically(method, symbol, since, maxEntriesPerRequest, params))
						PanicOnError(response)
					}
					errors = 0
					var responseLength interface{} = GetArrayLength(response)
					if IsTrue(this.Verbose) {
						var cursorString interface{} = Ternary(IsTrue((IsEqual(cursorValue, nil))), "", cursorValue)
						var iteration interface{} = (Add(i, 1))
						var cursorMessage interface{} = Add(Add(Add(Add(Add(Add(Add("Cursor pagination call ", ToString(iteration)), " method "), method), " response length "), ToString(responseLength)), " cursor "), cursorString)
						this.Log(cursorMessage)
					}
					if IsTrue(IsEqual(responseLength, 0)) {
						panic("break")
					}
					result = this.ArrayConcat(result, response)
					var last interface{} = this.SafeDict(response, Subtract(responseLength, 1))
					// cursorValue = this.safeValue (last['info'], cursorReceived);
					cursorValue = nil // search for the cursor
					for j := 0; IsLessThan(j, responseLength); j++ {
						var index interface{} = Subtract(Subtract(responseLength, j), 1)
						var entry interface{} = this.SafeDict(response, index)
						var info interface{} = this.SafeDict(entry, "info")
						var cursor interface{} = this.SafeValue(info, cursorReceived)
						if IsTrue(!IsEqual(cursor, nil)) {
							cursorValue = cursor
							panic("break")
						}
					}
					if IsTrue(IsEqual(cursorValue, nil)) {
						panic("break")
					}
					var lastTimestamp interface{} = this.SafeInteger(last, "timestamp")
					if IsTrue(IsTrue(!IsEqual(lastTimestamp, nil)) && IsTrue(IsLessThan(lastTimestamp, since))) {
						panic("break")
					}
					return nil
				}(this)

			}
			i = Add(i, 1)
		}
		var sorted interface{} = this.SortCursorPaginatedResult(result)
		var key interface{} = Ternary(IsTrue((IsEqual(method, "fetchOHLCV"))), 0, "timestamp")

		ch <- this.FilterBySinceLimit(sorted, since, limit, key)
		return nil

	}()
	return ch
}
func (this *Exchange) FetchPaginatedCallIncremental(method interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		pageKey := GetArg(optionalArgs, 4, nil)
		_ = pageKey
		maxEntriesPerRequest := GetArg(optionalArgs, 5, nil)
		_ = maxEntriesPerRequest
		var maxCalls interface{} = nil
		maxCallsparamsVariable := this.HandleOptionAndParams(params, method, "paginationCalls", 10)
		maxCalls = GetValue(maxCallsparamsVariable, 0)
		params = GetValue(maxCallsparamsVariable, 1)
		var maxRetries interface{} = nil
		maxRetriesparamsVariable := this.HandleOptionAndParams(params, method, "maxRetries", 3)
		maxRetries = GetValue(maxRetriesparamsVariable, 0)
		params = GetValue(maxRetriesparamsVariable, 1)
		maxEntriesPerRequestparamsVariable := this.HandleMaxEntriesPerRequestAndParams(method, maxEntriesPerRequest, params)
		maxEntriesPerRequest = GetValue(maxEntriesPerRequestparamsVariable, 0)
		params = GetValue(maxEntriesPerRequestparamsVariable, 1)
		var i interface{} = 0
		var errors interface{} = 0
		var result interface{} = []interface{}{}
		for IsLessThan(i, maxCalls) {

			{
				func(this *Exchange) (ret_ interface{}) {
					defer func() {
						if e := recover(); e != nil {
							if e == "break" {
								return
							}
							ret_ = func(this *Exchange) interface{} {
								// catch block:
								errors = Add(errors, 1)
								if IsTrue(IsGreaterThan(errors, maxRetries)) {
									panic(e)
								}
								return nil
							}(this)
						}
					}()
					// try block:
					AddElementToObject(params, pageKey, Add(i, 1))

					response := (<-this.callDynamically(method, symbol, since, maxEntriesPerRequest, params))
					PanicOnError(response)
					errors = 0
					var responseLength interface{} = GetArrayLength(response)
					if IsTrue(this.Verbose) {
						var iteration interface{} = ToString((Add(i, 1)))
						var incrementalMessage interface{} = Add(Add(Add(Add(Add("Incremental pagination call ", iteration), " method "), method), " response length "), ToString(responseLength))
						this.Log(incrementalMessage)
					}
					if IsTrue(IsEqual(responseLength, 0)) {
						panic("break")
					}
					result = this.ArrayConcat(result, response)
					return nil
				}(this)

			}
			i = Add(i, 1)
		}
		var sorted interface{} = this.SortCursorPaginatedResult(result)
		var key interface{} = Ternary(IsTrue((IsEqual(method, "fetchOHLCV"))), 0, "timestamp")

		ch <- this.FilterBySinceLimit(sorted, since, limit, key)
		return nil

	}()
	return ch
}
func (this *Exchange) SortCursorPaginatedResult(result interface{}) interface{} {
	var first interface{} = this.SafeValue(result, 0)
	if IsTrue(!IsEqual(first, nil)) {
		if IsTrue(InOp(first, "timestamp")) {
			return this.SortBy(result, "timestamp", true)
		}
		if IsTrue(InOp(first, "id")) {
			return this.SortBy(result, "id", true)
		}
	}
	return result
}
func (this *Exchange) RemoveRepeatedElementsFromArray(input interface{}, optionalArgs ...interface{}) interface{} {
	fallbackToTimestamp := GetArg(optionalArgs, 0, true)
	_ = fallbackToTimestamp
	var uniqueDic interface{} = map[string]interface{}{}
	var uniqueResult interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(input)); i++ {
		var entry interface{} = GetValue(input, i)
		var uniqValue interface{} = Ternary(IsTrue(fallbackToTimestamp), this.SafeStringN(entry, []interface{}{"id", "timestamp", 0}), this.SafeString(entry, "id"))
		if IsTrue(IsTrue(!IsEqual(uniqValue, nil)) && !IsTrue((InOp(uniqueDic, uniqValue)))) {
			AddElementToObject(uniqueDic, uniqValue, 1)
			AppendToArray(&uniqueResult, entry)
		}
	}
	var valuesLength interface{} = GetArrayLength(uniqueResult)
	if IsTrue(IsGreaterThan(valuesLength, 0)) {
		return uniqueResult
	}
	return input
}
func (this *Exchange) RemoveRepeatedTradesFromArray(input interface{}) interface{} {
	var uniqueResult interface{} = map[string]interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(input)); i++ {
		var entry interface{} = GetValue(input, i)
		var id interface{} = this.SafeString(entry, "id")
		if IsTrue(IsEqual(id, nil)) {
			var price interface{} = this.SafeString(entry, "price")
			var amount interface{} = this.SafeString(entry, "amount")
			var timestamp interface{} = this.SafeString(entry, "timestamp")
			var side interface{} = this.SafeString(entry, "side")
			// unique trade identifier
			id = Add(Add(Add(Add(Add(Add(Add("t_", ToString(timestamp)), "_"), side), "_"), price), "_"), amount)
		}
		if IsTrue(IsTrue(!IsEqual(id, nil)) && !IsTrue((InOp(uniqueResult, id)))) {
			AddElementToObject(uniqueResult, id, entry)
		}
	}
	var values interface{} = ObjectValues(uniqueResult)
	return values
}
func (this *Exchange) HandleUntilOption(key interface{}, request interface{}, params interface{}, optionalArgs ...interface{}) interface{} {
	multiplier := GetArg(optionalArgs, 0, 1)
	_ = multiplier
	var until interface{} = this.SafeInteger2(params, "until", "till")
	if IsTrue(!IsEqual(until, nil)) {
		AddElementToObject(request, key, this.ParseToInt(Multiply(until, multiplier)))
		params = this.Omit(params, []interface{}{"until", "till"})
	}
	return []interface{}{request, params}
}
func (this *Exchange) SafeOpenInterest(interest interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var symbol interface{} = this.SafeString(interest, "symbol")
	if IsTrue(IsEqual(symbol, nil)) {
		symbol = this.SafeString(market, "symbol")
	}
	return this.Extend(interest, map[string]interface{}{
		"symbol":             symbol,
		"baseVolume":         this.SafeNumber(interest, "baseVolume"),
		"quoteVolume":        this.SafeNumber(interest, "quoteVolume"),
		"openInterestAmount": this.SafeNumber(interest, "openInterestAmount"),
		"openInterestValue":  this.SafeNumber(interest, "openInterestValue"),
		"timestamp":          this.SafeInteger(interest, "timestamp"),
		"datetime":           this.SafeString(interest, "datetime"),
		"info":               this.SafeValue(interest, "info"),
	})
}
func (this *Exchange) ParseLiquidation(liquidation interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseLiquidation () is not supported yet")))
}
func (this *Exchange) ParseLiquidations(liquidations interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @description parses liquidation info from the exchange response
	 * @param {object[]} liquidations each item describes an instance of a liquidation event
	 * @param {object} market ccxt market
	 * @param {int} [since] when defined, the response items are filtered to only include items after this timestamp
	 * @param {int} [limit] limits the number of items in the response
	 * @returns {object[]} an array of [liquidation structures]{@link https://docs.ccxt.com/#/?id=liquidation-structure}
	 */
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(liquidations)); i++ {
		var entry interface{} = GetValue(liquidations, i)

		var parsed interface{} = this.DerivedExchange.ParseLiquidation(entry, market)
		PanicOnError(parsed)
		AppendToArray(&result, parsed)
	}
	var sorted interface{} = this.SortBy(result, "timestamp")
	var symbol interface{} = this.SafeString(market, "symbol")
	return this.FilterBySymbolSinceLimit(sorted, symbol, since, limit)
}
func (this *Exchange) ParseGreeks(greeks interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseGreeks () is not supported yet")))
}
func (this *Exchange) ParseAllGreeks(greeks interface{}, optionalArgs ...interface{}) interface{} {
	//
	// the value of greeks is either a dict or a list
	//
	symbols := GetArg(optionalArgs, 0, nil)
	_ = symbols
	params := GetArg(optionalArgs, 1, map[string]interface{}{})
	_ = params
	var results interface{} = []interface{}{}
	if IsTrue(IsArray(greeks)) {
		for i := 0; IsLessThan(i, GetArrayLength(greeks)); i++ {

			var parsedTicker interface{} = this.DerivedExchange.ParseGreeks(GetValue(greeks, i))
			PanicOnError(parsedTicker)
			var greek interface{} = this.Extend(parsedTicker, params)
			AppendToArray(&results, greek)
		}
	} else {
		var marketIds interface{} = ObjectKeys(greeks)
		for i := 0; IsLessThan(i, GetArrayLength(marketIds)); i++ {
			var marketId interface{} = GetValue(marketIds, i)

			var market interface{} = this.DerivedExchange.SafeMarket(marketId)
			PanicOnError(market)

			var parsed interface{} = this.DerivedExchange.ParseGreeks(GetValue(greeks, marketId), market)
			PanicOnError(parsed)
			var greek interface{} = this.Extend(parsed, params)
			AppendToArray(&results, greek)
		}
	}
	symbols = this.MarketSymbols(symbols)
	return this.FilterByArray(results, "symbol", symbols)
}
func (this *Exchange) ParseOption(chain interface{}, optionalArgs ...interface{}) interface{} {
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	market := GetArg(optionalArgs, 1, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseOption () is not supported yet")))
}
func (this *Exchange) ParseOptionChain(response interface{}, optionalArgs ...interface{}) interface{} {
	currencyKey := GetArg(optionalArgs, 0, nil)
	_ = currencyKey
	symbolKey := GetArg(optionalArgs, 1, nil)
	_ = symbolKey
	var optionStructures interface{} = map[string]interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(response)); i++ {
		var info interface{} = GetValue(response, i)
		var currencyId interface{} = this.SafeString(info, currencyKey)
		var currency interface{} = this.SafeCurrency(currencyId)
		var marketId interface{} = this.SafeString(info, symbolKey)

		var market interface{} = this.DerivedExchange.SafeMarket(marketId, nil, nil, "option")
		PanicOnError(market)
		AddElementToObject(optionStructures, GetValue(market, "symbol"), this.DerivedExchange.ParseOption(info, currency, market))
	}
	return optionStructures
}
func (this *Exchange) ParseMarginModes(response interface{}, optionalArgs ...interface{}) interface{} {
	symbols := GetArg(optionalArgs, 0, nil)
	_ = symbols
	symbolKey := GetArg(optionalArgs, 1, nil)
	_ = symbolKey
	marketType := GetArg(optionalArgs, 2, nil)
	_ = marketType
	var marginModeStructures interface{} = map[string]interface{}{}
	if IsTrue(IsEqual(marketType, nil)) {
		marketType = "swap" // default to swap
	}
	for i := 0; IsLessThan(i, GetArrayLength(response)); i++ {
		var info interface{} = GetValue(response, i)
		var marketId interface{} = this.SafeString(info, symbolKey)

		var market interface{} = this.DerivedExchange.SafeMarket(marketId, nil, nil, marketType)
		PanicOnError(market)
		if IsTrue(IsTrue((IsEqual(symbols, nil))) || IsTrue(this.InArray(GetValue(market, "symbol"), symbols))) {
			AddElementToObject(marginModeStructures, GetValue(market, "symbol"), this.DerivedExchange.ParseMarginMode(info, market))
		}
	}
	return marginModeStructures
}
func (this *Exchange) ParseMarginMode(marginMode interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseMarginMode () is not supported yet")))
}
func (this *Exchange) ParseLeverages(response interface{}, optionalArgs ...interface{}) interface{} {
	symbols := GetArg(optionalArgs, 0, nil)
	_ = symbols
	symbolKey := GetArg(optionalArgs, 1, nil)
	_ = symbolKey
	marketType := GetArg(optionalArgs, 2, nil)
	_ = marketType
	var leverageStructures interface{} = map[string]interface{}{}
	if IsTrue(IsEqual(marketType, nil)) {
		marketType = "swap" // default to swap
	}
	for i := 0; IsLessThan(i, GetArrayLength(response)); i++ {
		var info interface{} = GetValue(response, i)
		var marketId interface{} = this.SafeString(info, symbolKey)

		var market interface{} = this.DerivedExchange.SafeMarket(marketId, nil, nil, marketType)
		PanicOnError(market)
		if IsTrue(IsTrue((IsEqual(symbols, nil))) || IsTrue(this.InArray(GetValue(market, "symbol"), symbols))) {
			AddElementToObject(leverageStructures, GetValue(market, "symbol"), this.DerivedExchange.ParseLeverage(info, market))
		}
	}
	return leverageStructures
}
func (this *Exchange) ParseLeverage(leverage interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseLeverage () is not supported yet")))
}
func (this *Exchange) ParseConversions(conversions interface{}, optionalArgs ...interface{}) interface{} {
	code := GetArg(optionalArgs, 0, nil)
	_ = code
	fromCurrencyKey := GetArg(optionalArgs, 1, nil)
	_ = fromCurrencyKey
	toCurrencyKey := GetArg(optionalArgs, 2, nil)
	_ = toCurrencyKey
	since := GetArg(optionalArgs, 3, nil)
	_ = since
	limit := GetArg(optionalArgs, 4, nil)
	_ = limit
	params := GetArg(optionalArgs, 5, map[string]interface{}{})
	_ = params
	conversions = this.ToArray(conversions)
	var result interface{} = []interface{}{}
	var fromCurrency interface{} = nil
	var toCurrency interface{} = nil
	for i := 0; IsLessThan(i, GetArrayLength(conversions)); i++ {
		var entry interface{} = GetValue(conversions, i)
		var fromId interface{} = this.SafeString(entry, fromCurrencyKey)
		var toId interface{} = this.SafeString(entry, toCurrencyKey)
		if IsTrue(!IsEqual(fromId, nil)) {
			fromCurrency = this.SafeCurrency(fromId)
		}
		if IsTrue(!IsEqual(toId, nil)) {
			toCurrency = this.SafeCurrency(toId)
		}
		var conversion interface{} = this.Extend(this.DerivedExchange.ParseConversion(entry, fromCurrency, toCurrency), params)
		AppendToArray(&result, conversion)
	}
	var sorted interface{} = this.SortBy(result, "timestamp")
	var currency interface{} = nil
	if IsTrue(!IsEqual(code, nil)) {
		currency = this.SafeCurrency(code)
		code = GetValue(currency, "code")
	}
	if IsTrue(IsEqual(code, nil)) {
		return this.FilterBySinceLimit(sorted, since, limit)
	}
	var fromConversion interface{} = this.FilterBy(sorted, "fromCurrency", code)
	var toConversion interface{} = this.FilterBy(sorted, "toCurrency", code)
	var both interface{} = this.ArrayConcat(fromConversion, toConversion)
	return this.FilterBySinceLimit(both, since, limit)
}
func (this *Exchange) ParseConversion(conversion interface{}, optionalArgs ...interface{}) interface{} {
	fromCurrency := GetArg(optionalArgs, 0, nil)
	_ = fromCurrency
	toCurrency := GetArg(optionalArgs, 1, nil)
	_ = toCurrency
	panic(NotSupported(Add(this.Id, " parseConversion () is not supported yet")))
}
func (this *Exchange) ConvertExpireDate(date interface{}) interface{} {
	// parse YYMMDD to datetime string
	var year interface{} = Slice(date, 0, 2)
	var month interface{} = Slice(date, 2, 4)
	var day interface{} = Slice(date, 4, 6)
	var reconstructedDate interface{} = Add(Add(Add(Add(Add(Add("20", year), "-"), month), "-"), day), "T00:00:00Z")
	return reconstructedDate
}
func (this *Exchange) ConvertExpireDateToMarketIdDate(date interface{}) interface{} {
	// parse 240119 to 19JAN24
	var year interface{} = Slice(date, 0, 2)
	var monthRaw interface{} = Slice(date, 2, 4)
	var month interface{} = nil
	var day interface{} = Slice(date, 4, 6)
	if IsTrue(IsEqual(monthRaw, "01")) {
		month = "JAN"
	} else if IsTrue(IsEqual(monthRaw, "02")) {
		month = "FEB"
	} else if IsTrue(IsEqual(monthRaw, "03")) {
		month = "MAR"
	} else if IsTrue(IsEqual(monthRaw, "04")) {
		month = "APR"
	} else if IsTrue(IsEqual(monthRaw, "05")) {
		month = "MAY"
	} else if IsTrue(IsEqual(monthRaw, "06")) {
		month = "JUN"
	} else if IsTrue(IsEqual(monthRaw, "07")) {
		month = "JUL"
	} else if IsTrue(IsEqual(monthRaw, "08")) {
		month = "AUG"
	} else if IsTrue(IsEqual(monthRaw, "09")) {
		month = "SEP"
	} else if IsTrue(IsEqual(monthRaw, "10")) {
		month = "OCT"
	} else if IsTrue(IsEqual(monthRaw, "11")) {
		month = "NOV"
	} else if IsTrue(IsEqual(monthRaw, "12")) {
		month = "DEC"
	}
	var reconstructedDate interface{} = Add(Add(day, month), year)
	return reconstructedDate
}
func (this *Exchange) ConvertMarketIdExpireDate(date interface{}) interface{} {
	// parse 03JAN24 to 240103.
	var monthMappping interface{} = map[string]interface{}{
		"JAN": "01",
		"FEB": "02",
		"MAR": "03",
		"APR": "04",
		"MAY": "05",
		"JUN": "06",
		"JUL": "07",
		"AUG": "08",
		"SEP": "09",
		"OCT": "10",
		"NOV": "11",
		"DEC": "12",
	}
	// if exchange omits first zero and provides i.e. '3JAN24' instead of '03JAN24'
	if IsTrue(IsEqual(GetLength(date), 6)) {
		date = Add("0", date)
	}
	var year interface{} = Slice(date, 0, 2)
	var monthName interface{} = Slice(date, 2, 5)
	var month interface{} = this.SafeString(monthMappping, monthName)
	var day interface{} = Slice(date, 5, 7)
	var reconstructedDate interface{} = Add(Add(day, month), year)
	return reconstructedDate
}
func (this *Exchange) FetchPositionHistory(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		/**
		 * @method
		 * @name exchange#fetchPositionHistory
		 * @description fetches the history of margin added or reduced from contract isolated positions
		 * @param {string} [symbol] unified market symbol
		 * @param {int} [since] timestamp in ms of the position
		 * @param {int} [limit] the maximum amount of candles to fetch, default=1000
		 * @param {object} params extra parameters specific to the exchange api endpoint
		 * @returns {object[]} a list of [position structures]{@link https://docs.ccxt.com/#/?id=position-structure}
		 */
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params
		if IsTrue(GetValue(this.Has, "fetchPositionsHistory")) {

			positions := <-this.DerivedExchange.FetchPositionsHistory([]interface{}{symbol}, since, limit, params)
			PanicOnError(positions)

			ch <- positions
			return nil
		} else {
			panic(NotSupported(Add(this.Id, " fetchPositionHistory () is not supported yet")))
		}

	}()
	return ch
}
func (this *Exchange) FetchPositionsHistory(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchPositionsHistory () is not supported yet")))

	}()
	return ch
}
func (this *Exchange) ParseMarginModification(data interface{}, optionalArgs ...interface{}) interface{} {
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	panic(NotSupported(Add(this.Id, " parseMarginModification() is not supported yet")))
}
func (this *Exchange) ParseMarginModifications(response interface{}, optionalArgs ...interface{}) interface{} {
	symbols := GetArg(optionalArgs, 0, nil)
	_ = symbols
	symbolKey := GetArg(optionalArgs, 1, nil)
	_ = symbolKey
	marketType := GetArg(optionalArgs, 2, nil)
	_ = marketType
	var marginModifications interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(response)); i++ {
		var info interface{} = GetValue(response, i)
		var marketId interface{} = this.SafeString(info, symbolKey)

		var market interface{} = this.DerivedExchange.SafeMarket(marketId, nil, nil, marketType)
		PanicOnError(market)
		if IsTrue(IsTrue((IsEqual(symbols, nil))) || IsTrue(this.InArray(GetValue(market, "symbol"), symbols))) {
			AppendToArray(&marginModifications, this.DerivedExchange.ParseMarginModification(info, market))
		}
	}
	return marginModifications
}
func (this *Exchange) FetchTransfer(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchTransfer () is not supported yet")))

	}()
	return ch
}
func (this *Exchange) FetchTransfers(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		panic(NotSupported(Add(this.Id, " fetchTransfers () is not supported yet")))

	}()
	return ch
}
func (this *Exchange) CleanUnsubscription(client Client, subHash interface{}, unsubHash interface{}, optionalArgs ...interface{}) {
	subHashIsPrefix := GetArg(optionalArgs, 0, false)
	_ = subHashIsPrefix
	if IsTrue(InOp(client.Subscriptions, unsubHash)) {
		Remove(client.Subscriptions, unsubHash)
	}
	if !IsTrue(subHashIsPrefix) {
		if IsTrue(InOp(client.Subscriptions, subHash)) {
			Remove(client.Subscriptions, subHash)
		}
		if IsTrue(InOp(client.Futures, subHash)) {
			error := UnsubscribeError(Add(Add(this.Id, " "), subHash))
			client.Reject(error, subHash)
		}
	} else {
		var clientSubscriptions interface{} = ObjectKeys(client.Subscriptions)
		for i := 0; IsLessThan(i, GetArrayLength(clientSubscriptions)); i++ {
			var sub interface{} = GetValue(clientSubscriptions, i)
			if IsTrue(StartsWith(sub, subHash)) {
				Remove(client.Subscriptions, sub)
			}
		}
		var clientFutures interface{} = ObjectKeys(client.Futures)
		for i := 0; IsLessThan(i, GetArrayLength(clientFutures)); i++ {
			var future interface{} = GetValue(clientFutures, i)
			if IsTrue(StartsWith(future, subHash)) {
				error := UnsubscribeError(Add(Add(this.Id, " "), future))
				client.Reject(error, future)
			}
		}
	}
	client.Resolve(true, unsubHash)
}
func (this *Exchange) CleanCache(subscription interface{}) {
	var topic interface{} = this.SafeString(subscription, "topic")
	var symbols interface{} = this.SafeList(subscription, "symbols", []interface{}{})
	var symbolsLength interface{} = GetArrayLength(symbols)
	if IsTrue(IsEqual(topic, "ohlcv")) {
		var symbolsAndTimeFrames interface{} = this.SafeList(subscription, "symbolsAndTimeframes", []interface{}{})
		for i := 0; IsLessThan(i, GetArrayLength(symbolsAndTimeFrames)); i++ {
			var symbolAndTimeFrame interface{} = GetValue(symbolsAndTimeFrames, i)
			var symbol interface{} = this.SafeString(symbolAndTimeFrame, 0)
			var timeframe interface{} = this.SafeString(symbolAndTimeFrame, 1)
			if IsTrue(IsTrue((!IsEqual(this.Ohlcvs, nil))) && IsTrue((InOp(this.Ohlcvs, symbol)))) {
				if IsTrue(InOp(GetValue(this.Ohlcvs, symbol), timeframe)) {
					Remove(GetValue(this.Ohlcvs, symbol), timeframe)
				}
			}
		}
	} else if IsTrue(IsGreaterThan(symbolsLength, 0)) {
		for i := 0; IsLessThan(i, GetArrayLength(symbols)); i++ {
			var symbol interface{} = GetValue(symbols, i)
			if IsTrue(IsEqual(topic, "trades")) {
				if IsTrue(InOp(this.Trades, symbol)) {
					Remove(this.Trades, symbol)
				}
			} else if IsTrue(IsEqual(topic, "orderbook")) {
				if IsTrue(InOp(this.Orderbooks, symbol)) {
					Remove(this.Orderbooks, symbol)
				}
			} else if IsTrue(IsEqual(topic, "ticker")) {
				if IsTrue(InOp(this.Tickers, symbol)) {
					Remove(this.Tickers, symbol)
				}
			}
		}
	} else {
		if IsTrue(IsTrue(IsEqual(topic, "myTrades")) && IsTrue((!IsEqual(this.MyTrades, nil)))) {
			this.MyTrades = nil
		} else if IsTrue(IsTrue(IsEqual(topic, "orders")) && IsTrue((!IsEqual(this.Orders, nil)))) {
			this.Orders = nil
		} else if IsTrue(IsTrue(IsEqual(topic, "positions")) && IsTrue((!IsEqual(this.Positions, nil)))) {
			this.Positions = nil
			var clients interface{} = ObjectValues(this.Clients)
			for i := 0; IsLessThan(i, GetArrayLength(clients)); i++ {
				var client interface{} = GetValue(clients, i)
				var futures interface{} = this.SafeDict(client, "futures")
				if IsTrue(IsTrue((!IsEqual(futures, nil))) && IsTrue((InOp(futures, "fetchPositionsSnapshot")))) {
					Remove(futures, "fetchPositionsSnapshot")
				}
			}
		} else if IsTrue(IsTrue(IsEqual(topic, "ticker")) && IsTrue((!IsEqual(this.Tickers, nil)))) {
			var tickerSymbols interface{} = ObjectKeys(this.Tickers)
			for i := 0; IsLessThan(i, GetArrayLength(tickerSymbols)); i++ {
				var tickerSymbol interface{} = GetValue(tickerSymbols, i)
				if IsTrue(InOp(this.Tickers, tickerSymbol)) {
					Remove(this.Tickers, tickerSymbol)
				}
			}
		}
	}
}
