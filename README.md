# crypto-monitor

## Different exchanges nuances:
- Binance
    - can use both watchOrderBook and watchOrderBookForSymbols.
    - does not support unwatching symbols.
    - uses multiple clients (ws-connections) for watchOrderBook and watchOrderBookForSymbols (sometimes mixed and dynamically merged).
    - we use 'exchange-connections-helpers.ts' to close the connections when we are done watching them.
- Bybit
    - uses single client for watchOrderBook and watchOrderBookForSymbols.
    - does not support unwatching symbols, however it does have 'unWatchOrderBook' implementation.
    - we use 'monitored-symbols-registry.ts' keep track of monitored symbols and force call "unWatchOrderBook" when we are done watching them.
    - it also doesn't allow to start watching more than 10 symbols in less than 500ms timeframe.
    - we use 'exchange-watch-sheduler.ts' to split the symbols into slots of 10 (or less) and add delays between starting the groups watching.
    - max group size is 10 symbols as well.
- Coinbase
    - uses single client for watchOrderBook and watchOrderBookForSymbols.
    - does not support unwatching symbols... at all.
    - only supports 30 symbols per client/connection.
    - we use fetch<PERSON>rderBook instead of watch<PERSON>rderBook for it.
    - we use separate exchange instance for each group monitor.
    - when we finish monitoring all symbols, we close the exchange instance of the group monitor.
- Mexc
    - only supports watchOrderBook/unWatchOrderBook methods.
    - uses single client/connection.
    - cannot handle more than 70 symbols per connection.
    - we use individual monitors (several) for it with separate exchange instances.
    - each individual monitor can monitor up to 30 symbols (limited on purpose to improve stability).
    - when we finish monitoring all symbols, we close the exchange instance of the individual monitor.
- Gate
    - only supports watchOrderBook methods.
    - does not support unwatching symbols, however it does have 'unWatchOrderBook' implementation.
    - we use mix of Bybit approach of handling unwatching symbols and Mexc approach of handling multiple individual monitors.