{"name": "ccxt/ccxt", "type": "library", "description": "A JavaScript / TypeScript / Python / C# / PHP cryptocurrency trading library with support for more than 100 bitcoin/altcoin exchanges", "keywords": ["algorithmic", "algotrading", "altcoin", "altcoins", "api", "arbitrage", "backtest", "backtesting", "bitcoin", "bot", "btc", "cny", "coin", "coins", "crypto", "cryptocurrency", "crypto currency", "crypto market", "currency", "currencies", "darkcoin", "dash", "digital currency", "doge", "<PERSON><PERSON><PERSON><PERSON>", "e-commerce", "etc", "eth", "ether", "ethereum", "exchange", "exchanges", "eur", "framework", "invest", "investing", "investor", "library", "light", "litecoin", "ltc", "market", "market data", "markets", "merchandise", "merchant", "minimal", "order", "orderbook", "order book", "price", "price data", "pricefeed", "private", "public", "ripple", "strategy", "toolkit", "trade", "trader", "trading", "usd", "volume", "xbt", "xrp", "zec", "zerocoin", "1Broker", "1BTCXE", "ACX", "acx.io", "ANX", "ANXPro", "Binance", "binance.com", "bit2c.co.il", "Bit2C", "BitBay", "BitBays", "bitcoincoid", "Bitcoin.co.id", "Bitfinex", "bitFLyer", "bitflyer.jp", "bithumb", "bithumb.com", "bitlish", "BitMarket", "BitMEX", "Bitso", "Bitstamp", "Bittrex", "BL3P", "Bleutrade", "bleutrade.com", "BlinkTrade", "BtcBox", "btcbox.co.jp", "BTCC", "BTCChina", "BTC-e", "BTCe", "BTCExchange", "btcexchange.ph", "BTC Markets", "btcmarkets", "btcmarkets.net", "BTCTrader", "btctrader.com", "btc-trade.com.ua", "BTC Trade UA", "BTCTurk", "btcturk.com", "BTCX", "btc-x", "bter", "Bter.com", "BX.in.th", "ccex", "C-CEX", "cex", "CEX.IO", "CHBTC", "ChileBit", "chilebit.net", "coincheck", "coingi", "coingi.com", "CoinMarketCap", "CoinMate", "Coinsecure", "CoinSpot", "coinspot.com.au", "Crypto Capital", "cryptocapital.co", "DSX", "dsx.uk", "EXMO", "flowBTC", "flowbtc.com", "FoxBit", "foxbit.exchange", "FYB-SE", "FYB-SG", "Gatecoin", "GDAX", "Gemini", "HitBTC", "<PERSON><PERSON><PERSON>", "HuobiPRO", "huobi.pro", "Independent Reserve", "independentreserve.com", "itBit", "jubi.com", "<PERSON><PERSON><PERSON>", "LakeBTC", "lakebtc.com", "LiveCoin", "Liqui", "liqui.io", "luno", "mercado", "MercadoBitcoin", "mercadobitcoin.br", "mixcoins", "mixcoins.com", "nova", "novaexchange", "novaexchange.com", "OKCoin", "OKCoin.com", "OKCoin.cn", "OKEX", "okex.com", "Paymium", "Poloniex", "QuadrigaCX", "Qryptos", "QUOINE", "Southxchange", "SurBitcoin", "surbitcoin.com", "Tidex", "tidex.com", "TheRockTrading", "UrduBit", "urdubit.com", "Vaultoro", "VBTC", "vbtc.exchange", "vbtc.vn", "VirWoX", "WEX", "wex.nz", "xBTCe", "xbtce.com", "YoBit", "yobit.net", "YUNBI", "<PERSON><PERSON><PERSON>"], "homepage": "https://github.com/ccxt/ccxt", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/kroitor", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/frosty00", "role": "Junior Developer"}], "autoload": {"psr-4": {"ccxt\\": "php/", "ccxt\\async\\": "php/async/", "ccxt\\pro\\": "php/pro/"}, "classmap": ["php/protobuf/mexc/"], "files": ["ccxt.php", "php/pro/base/functions.php"]}, "require": {"php": ">=8.1.0", "ext-bcmath": "*", "ext-curl": "*", "ext-iconv": "*", "ext-pcre": "*", "ext-json": "*", "ext-openssl": "*", "ext-gmp": "*", "symfony/polyfill-mbstring": "^1.7", "pear/console_table": "1.3.1", "react/http": ">=1.11.0", "react/async": "^4.3.0", "react/promise": "^3.2.0", "react/promise-timer": "^1.11", "evenement/evenement": "^3.0", "guzzlehttp/psr7": "^2.0"}, "suggest": {"recoil/recoil": "Required for asynchronous API calls to exchanges with PHP", "recoil/react": "Required for asynchronous API calls to exchanges with PHP", "react/http": "Required for asynchronous API calls to exchanges with PHP", "clue/buzz-react": "Required for asynchronous API calls to exchanges with PHP", "react/event-loop": "Required for asynchronous API calls to exchanges with PHP", "clue/http-proxy-react": "Required for using a proxy when doing asynchronous API calls to exchanges with PHP"}, "archive": {"exclude": ["/build", "/dist", "/doc", "/js", "/python", "/wiki"]}}