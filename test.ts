import { WatchIndividualExchangeMonitor } from './src/orderbook-monitor/watch-individual-exchange-monitor';

i use forceUnwatchLoop for bitget exchange. and it works fine when i run first iteration of tests. but on the second one, i get a lot of errors like this for different symbols in WatchIndividualExchangeMonitor:
  ERROR: WatchIndividualExchangeMonitor 'watchSymbol' COMP/USDT error:
  err: {
    "type": "UnsubscribeError",
      "message": "bitget orderbook COMP/USDT",
      "stack":
    UnsubscribeError: bitget orderbook COMP/USDT
    at bitget.handleOrderBookUnSubscription (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/bitget.js:2318:23)
    at bitget.handleUnSubscriptionStatus (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/bitget.js:2444:22)
    at bitget.handleMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/bitget.js:2250:18)
    at WsClient.onMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/base/ws/Client.js:305:18)
    at callListener (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:290:14)
    at WebSocket.onMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:209:9)
    at WebSocket.emit (node:events:507:28)
    at Receiver.receiverOnMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/websocket.js:1220:20)
    at Receiver.emit (node:events:507:28)
    at Receiver.dataMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/receiver.js:596:14)
    "name": "UnsubscribeError"
  }

  i assume that there are probably some promises left unprocessed when i call unwatch method. could you please check it and tell me if i'm right?
  if so, give me your advice on how to fix it, but do not change any code yet.